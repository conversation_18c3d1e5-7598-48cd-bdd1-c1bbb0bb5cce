# You = Life Backend Server

A comprehensive Node.js backend server with Socket.IO real-time functionality and Redis integration.

## Features

- ✅ Express.js REST API
- ✅ Socket.IO for real-time communication
- ✅ Redis for caching and session management
- ✅ Rate limiting with Redis
- ✅ MongoDB integration
- ✅ JWT authentication support
- ✅ Swagger API documentation
- ✅ Real-time notifications
- ✅ Chat and messaging system
- ✅ User presence tracking
- ✅ Room-based communication
- ✅ Graceful shutdown handling

## Prerequisites

Before running this server, make sure you have the following installed:

- **Node.js** (v14 or higher)
- **npm** or **yarn**
- **MongoDB** (local or cloud instance)
- **Redis** (local or cloud instance)

## Installation

1. **Clone the repository** (if not already done):
   ```bash
   git clone <repository-url>
   cd You-Life/server
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Set up environment variables**:
   - Copy the `.env` file and update the values:
   ```env
   MONGO_URI=mongodb://localhost:27017/you-life
   STRIPE_SECRET_KEY=your_stripe_secret_key

   # Redis Configuration
   REDIS_HOST=localhost
   REDIS_PORT=6379
   REDIS_PASSWORD=
   REDIS_URL=

   # Socket.IO Configuration
   CLIENT_URL=http://localhost:3000
   USE_REDIS_ADAPTER=false

   # JWT Configuration
   JWT_SECRET=your_jwt_secret_key_here
   JWT_EXPIRE=7d

   # Server Configuration
   NODE_ENV=development
   PORT=5000
   ```

## Local Development Setup

### 1. Start MongoDB

**Option A: Using MongoDB locally**
```bash
# Install MongoDB on Ubuntu
sudo apt update
sudo apt install -y mongodb
sudo systemctl start mongodb
sudo systemctl enable mongodb
```

**Option B: Using MongoDB Compass or MongoDB Atlas**
- Update the `MONGO_URI` in your `.env` file with your cloud connection string

### 2. Start Redis

**Option A: Using Redis locally**
```bash
# Install Redis on Ubuntu
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server

# Test Redis connection
redis-cli ping
```

**Option B: Using Redis Cloud**
- Update the `REDIS_URL` in your `.env` file with your cloud Redis URL

### 3. Start the Server

```bash
# Development mode with auto-restart
npm run dev

# Production mode
npm start
```

The server will start on `http://localhost:5000`

## API Documentation

Once the server is running, you can access the Swagger API documentation at:
- **Local**: http://localhost:5000/api-docs
- **Production**: https://your-domain.com/api-docs

## Socket.IO Integration

### Client Connection Example

```javascript
import io from 'socket.io-client';

const socket = io('http://localhost:5000', {
  transports: ['websocket', 'polling']
});

// Authenticate user
socket.emit('authenticate', {
  userId: 'user123',
  token: 'your-jwt-token'
});

// Listen for authentication response
socket.on('authenticated', (data) => {
  console.log('Authenticated:', data);
});

// Join a chat room
socket.emit('join_chat_room', {
  roomId: 'room123',
  roomType: 'group'
});

// Send a message
socket.emit('send_message', {
  roomId: 'room123',
  message: 'Hello World!',
  messageType: 'text'
});

// Listen for new messages
socket.on('new_message', (message) => {
  console.log('New message:', message);
});
```

### Available Socket Events

#### Authentication
- `authenticate` - Authenticate user with token
- `activity` - Update user activity timestamp

#### Chat & Messaging
- `join_chat_room` - Join a chat room
- `leave_chat_room` - Leave a chat room
- `send_message` - Send message to room
- `typing_start` - Start typing indicator
- `typing_stop` - Stop typing indicator

#### Presence & Status
- `update_status` - Update user online status
- `get_online_users` - Get list of online users

#### Notifications
- `send_notification` - Send real-time notification
- `mark_notification_read` - Mark notification as read

#### Collaboration
- `join_collaboration` - Join collaboration session
- `document_change` - Broadcast document changes
- `cursor_update` - Update cursor position

## Redis Integration

### Available Redis Services

The server includes a comprehensive Redis service (`utils/redisService.js`) with the following features:

- **Basic Operations**: set, get, del, exists, expire
- **Hash Operations**: hset, hget, hgetall, hdel
- **List Operations**: lpush, rpush, lpop, lrange
- **Set Operations**: sadd, srem, smembers
- **Session Management**: setSession, getSession, deleteSession
- **Rate Limiting**: incrementRateLimit
- **Caching**: cache, getCached, invalidateCache
- **Pub/Sub**: publish, subscribe

### Usage Example

```javascript
const redisService = require('./utils/redisService');

// Cache data
await redisService.cache('user:123', { name: 'John Doe' }, 3600);

// Get cached data
const userData = await redisService.getCached('user:123');

// Session management
await redisService.setSession('session123', { userId: '123' }, 3600);
const session = await redisService.getSession('session123');

// Rate limiting
const rateLimit = await redisService.incrementRateLimit('api:user:123', 60, 100);
```

## Rate Limiting

The server includes comprehensive rate limiting using Redis:

- **API Limiter**: 1000 requests per 15 minutes
- **Auth Limiter**: 5 requests per 15 minutes
- **Register Limiter**: 3 requests per hour
- **Upload Limiter**: 20 requests per hour
- **Message Limiter**: 30 messages per minute

### Custom Rate Limiter

```javascript
const { createRateLimiter } = require('./middleware/rateLimiter');

const customLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 50, // 50 requests per minute
  message: 'Too many requests'
});

app.use('/api/custom', customLimiter);
```

## Environment Configuration

### Development vs Production

- **Development**: Set `NODE_ENV=development`
- **Production**: Set `NODE_ENV=production`

### Redis Adapter for Socket.IO

For production deployments with multiple server instances:

```env
USE_REDIS_ADAPTER=true
REDIS_URL=redis://your-redis-instance:6379
```

## Testing

```bash
# Run tests (if available)
npm test

# Run linting
npm run lint

# Check for security vulnerabilities
npm audit
```

## Deployment

### Docker Deployment

```dockerfile
# Dockerfile example
FROM node:16-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
EXPOSE 5000

CMD ["npm", "start"]
```

### Production Checklist

- [ ] Set `NODE_ENV=production`
- [ ] Use production MongoDB instance
- [ ] Use production Redis instance
- [ ] Set secure JWT secret
- [ ] Enable Redis adapter for Socket.IO clustering
- [ ] Set up proper CORS origins
- [ ] Configure rate limits appropriately
- [ ] Set up monitoring and logging

## Troubleshooting

### Common Issues

1. **Redis Connection Failed**
   - Check if Redis server is running
   - Verify REDIS_HOST and REDIS_PORT in .env
   - Check firewall settings

2. **MongoDB Connection Failed**
   - Verify MONGO_URI in .env
   - Check if MongoDB service is running
   - Ensure network connectivity

3. **Socket.IO Connection Issues**
   - Check CORS configuration
   - Verify CLIENT_URL in .env
   - Check firewall/proxy settings

4. **Rate Limiting Not Working**
   - Ensure Redis is connected
   - Check rate limiter configuration
   - Verify middleware order

### Logs

The server provides detailed logging for:
- Database connections
- Redis operations
- Socket.IO connections
- API requests
- Error handling

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the ISC License.
