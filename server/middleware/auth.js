const User = require('../models/User');
const jwtUtils = require('../utils/jwtUtils');
const redisService = require('../utils/redisService');

/**
 * Basic authentication middleware
 * Verifies JWT token and attaches user to request
 */
const authenticateToken = async (req, res, next) => {
	try {
		const authHeader = req.headers.authorization;
		const token = jwtUtils.extractTokenFromHeader(authHeader);

		if (!token) {
			return res.status(401).json({
				success: false,
				message: 'Access token required',
			});
		}

		// Verify the token
		const decoded = jwtUtils.verifyAccessToken(token);

		// Check if token is blacklisted (optional - for logout functionality)
		const isBlacklisted = await redisService.get(`blacklisted_token:${token}`);
		if (isBlacklisted) {
			return res.status(401).json({
				success: false,
				message: 'Token has been invalidated',
			});
		}

		// Get user from database
		const user = await User.findById(decoded.id).select('-password');
		if (!user) {
			return res.status(401).json({
				success: false,
				message: 'User not found',
			});
		}

		// Check if user account is active
		if (user.status !== 'active') {
			return res.status(401).json({
				success: false,
				message: 'Account is not active',
			});
		}

		// Check if user account is locked
		if (user.isLocked) {
			return res.status(401).json({
				success: false,
				message: 'Account is temporarily locked',
			});
		}

		// Attach user and token to request
		req.user = user;
		req.token = token;
		req.decoded = decoded;

		next();
	} catch (error) {
		console.error('Authentication error:', error);

		if (error.message.includes('expired')) {
			return res.status(401).json({
				success: false,
				message: 'Token has expired',
				code: 'TOKEN_EXPIRED',
			});
		}

		return res.status(401).json({
			success: false,
			message: 'Invalid token',
		});
	}
};

/**
 * Optional authentication middleware
 * Attaches user to request if token is valid, but doesn't fail if no token
 */
const optionalAuth = async (req, res, next) => {
	try {
		const authHeader = req.headers.authorization;
		const token = jwtUtils.extractTokenFromHeader(authHeader);

		if (!token) {
			return next();
		}

		const decoded = jwtUtils.verifyAccessToken(token);
		const user = await User.findById(decoded.id).select('-password');

		if (user && user.status === 'active' && !user.isLocked) {
			req.user = user;
			req.token = token;
			req.decoded = decoded;
		}

		next();
	} catch (error) {
		// Continue without authentication if token is invalid
		next();
	}
};

/**
 * Email verification middleware
 * Ensures user has verified their email
 */
const requireEmailVerification = (req, res, next) => {
	if (!req.user) {
		return res.status(401).json({
			success: false,
			message: 'Authentication required',
		});
	}

	if (!req.user.isEmailVerified) {
		return res.status(403).json({
			success: false,
			message: 'Email verification required',
			code: 'EMAIL_NOT_VERIFIED',
		});
	}

	next();
};

/**
 * Role-based authorization middleware factory
 * @param {Array|String} allowedRoles - Array of roles or single role
 * @returns {Function} Middleware function
 */
const requireRoles = (...allowedRoles) => {
	return (req, res, next) => {
		if (!req.user) {
			return res.status(401).json({
				success: false,
				message: 'Authentication required',
			});
		}

		const flattenedRoles = allowedRoles.flat();
		const hasRequiredRole = req.user.roles.some((role) =>
			flattenedRoles.includes(role),
		);

		if (!hasRequiredRole) {
			return res.status(403).json({
				success: false,
				message: 'Insufficient permissions',
				requiredRoles: flattenedRoles,
				userRoles: req.user.roles,
			});
		}

		next();
	};
};

/**
 * Superadmin only middleware
 */
const requireSuperAdmin = requireRoles('superadmin');

/**
 * Tutor or higher middleware
 */
const requireTutorOrHigher = requireRoles('tutor', 'superadmin');

/**
 * Account owner or admin middleware
 * Allows access if user is the account owner or has admin privileges
 * @param {String} paramName - Name of the parameter containing user ID
 */
const requireOwnershipOrAdmin = (paramName = 'userId') => {
	return (req, res, next) => {
		if (!req.user) {
			return res.status(401).json({
				success: false,
				message: 'Authentication required',
			});
		}

		const resourceUserId = req.params[paramName] || req.body[paramName];
		const currentUserId = req.user._id.toString();

		// Allow if user owns the resource
		if (resourceUserId === currentUserId) {
			return next();
		}

		// Allow if user is superadmin
		if (req.user.roles.includes('superadmin')) {
			return next();
		}
		if (req.user.roles.includes('tutor')) {
			return next();
		}

		return res.status(403).json({
			success: false,
			message: 'Access denied: insufficient permissions',
		});
	};
};

/**
 * Rate limiting check based on user
 */
const checkUserRateLimit = async (req, res, next) => {
	try {
		if (!req.user) {
			return next();
		}

		const key = `user_rate_limit:${req.user._id}`;
		const limit = req.user.roles.includes('superadmin') ? 1000 : 100;
		const window = 3600; // 1 hour

		const rateLimitInfo = await redisService.incrementRateLimit(
			key,
			window,
			limit,
		);

		if (rateLimitInfo.current > limit) {
			return res.status(429).json({
				success: false,
				message: 'Rate limit exceeded',
				retryAfter: Math.ceil((rateLimitInfo.resetTime - Date.now()) / 1000),
			});
		}

		// Add rate limit info to response headers
		res.set({
			'X-RateLimit-Limit': limit,
			'X-RateLimit-Remaining': rateLimitInfo.remaining,
			'X-RateLimit-Reset': new Date(rateLimitInfo.resetTime).toISOString(),
		});

		next();
	} catch (error) {
		console.error('Rate limit check error:', error);
		next(); // Continue on error
	}
};

/**
 * Check if user account is suspended
 */
const checkAccountStatus = (req, res, next) => {
	if (!req.user) {
		return res.status(401).json({
			success: false,
			message: 'Authentication required',
		});
	}

	if (req.user.status === 'suspended') {
		return res.status(403).json({
			success: false,
			message: 'Account has been suspended',
			code: 'ACCOUNT_SUSPENDED',
		});
	}

	if (req.user.status === 'inactive') {
		return res.status(403).json({
			success: false,
			message: 'Account is inactive',
			code: 'ACCOUNT_INACTIVE',
		});
	}

	next();
};

/**
 * Middleware to check if user needs to change password
 */
const checkPasswordChangeRequired = (req, res, next) => {
	if (!req.user) {
		return next();
	}

	// Skip for password change endpoints
	if (
		req.path.includes('/change-password') ||
		req.path.includes('/reset-password')
	) {
		return next();
	}

	if (req.user.requirePasswordChange) {
		return res.status(403).json({
			success: false,
			message: 'Password change required',
			code: 'PASSWORD_CHANGE_REQUIRED',
		});
	}

	next();
};

/**
 * Middleware to log user activity
 */
const logUserActivity = async (req, res, next) => {
	try {
		if (req.user) {
			const activityKey = `user_activity:${req.user._id}`;
			const activity = {
				timestamp: new Date().toISOString(),
				action: `${req.method} ${req.path}`,
				ip: req.ip,
				userAgent: req.get('User-Agent'),
			};

			// Store last 100 activities
			await redisService.lpush(activityKey, activity);
			await redisService.client.ltrim(activityKey, 0, 99);

			// Update last activity timestamp
			await User.findByIdAndUpdate(req.user._id, {
				lastActivity: new Date(),
			});
		}
		next();
	} catch (error) {
		console.error('Activity logging error:', error);
		next(); // Continue on error
	}
};

/**
 * Combined middleware for common authentication requirements
 */
const requireAuthAndVerification = [
	authenticateToken,
	requireEmailVerification,
	checkAccountStatus,
	checkPasswordChangeRequired,
	logUserActivity,
];

/**
 * Combined middleware for admin operations
 */
const requireAdmin = [
	authenticateToken,
	requireEmailVerification,
	requireSuperAdmin,
	checkAccountStatus,
	logUserActivity,
];

module.exports = {
	authenticateToken,
	optionalAuth,
	requireEmailVerification,
	requireRoles,
	requireSuperAdmin,
	requireTutorOrHigher,
	requireOwnershipOrAdmin,
	checkUserRateLimit,
	checkAccountStatus,
	checkPasswordChangeRequired,
	logUserActivity,
	requireAuthAndVerification,
	requireAdmin,
};
