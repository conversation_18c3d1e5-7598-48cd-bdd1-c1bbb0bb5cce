const redisService = require('../utils/redisService');

const createRateLimiter = (options = {}) => {
    const {
        windowMs = 15 * 60 * 1000, // 15 minutes
        max = 100, // Limit each IP to 100 requests per windowMs
        message = 'Too many requests from this IP, please try again later.',
        standardHeaders = true, // Return rate limit info in the `RateLimit-*` headers
        legacyHeaders = false, // Disable the `X-RateLimit-*` headers
        keyGenerator = (req) => req.ip, // Function to generate keys
        skip = () => false, // Function to skip certain requests
        onLimitReached = null, // Callback when limit is reached
    } = options;

    return async (req, res, next) => {
        try {
            // Skip rate limiting if specified
            if (skip(req)) {
                return next();
            }

            const key = `rate_limit:${keyGenerator(req)}`;
            const windowInSeconds = Math.floor(windowMs / 1000);

            // Get current rate limit status
            const rateLimitInfo = await redisService.incrementRateLimit(key, windowInSeconds, max);

            // Add headers if enabled
            if (standardHeaders) {
                res.set({
                    'RateLimit-Limit': max,
                    'RateLimit-Remaining': rateLimitInfo.remaining,
                    'RateLimit-Reset': new Date(rateLimitInfo.resetTime).toISOString(),
                });
            }

            if (legacyHeaders) {
                res.set({
                    'X-RateLimit-Limit': max,
                    'X-RateLimit-Remaining': rateLimitInfo.remaining,
                    'X-RateLimit-Reset': Math.floor(rateLimitInfo.resetTime / 1000),
                });
            }

            // Check if limit exceeded
            if (rateLimitInfo.current > max) {
                // Call onLimitReached callback if provided
                if (onLimitReached) {
                    onLimitReached(req, res);
                }

                return res.status(429).json({
                    error: message,
                    retryAfter: Math.ceil((rateLimitInfo.resetTime - Date.now()) / 1000),
                });
            }

            next();
        } catch (error) {
            console.error('Rate limiter error:', error);
            // If Redis is down, allow the request to proceed
            next();
        }
    };
};

// Predefined rate limiters for common use cases

// General API rate limiter
const apiLimiter = createRateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 1000, // Limit each IP to 1000 requests per 15 minutes
    message: 'Too many API requests from this IP, please try again later.',
});

// Strict rate limiter for authentication endpoints
const authLimiter = createRateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // Limit each IP to 5 login requests per 15 minutes
    message: 'Too many authentication attempts from this IP, please try again later.',
    keyGenerator: (req) => `auth:${req.ip}`,
});

// Registration rate limiter
const registerLimiter = createRateLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 3, // Limit each IP to 3 registration attempts per hour
    message: 'Too many registration attempts from this IP, please try again later.',
    keyGenerator: (req) => `register:${req.ip}`,
});

// Password reset rate limiter
const passwordResetLimiter = createRateLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 3, // Limit each IP to 3 password reset requests per hour
    message: 'Too many password reset attempts from this IP, please try again later.',
    keyGenerator: (req) => `password_reset:${req.ip}`,
});

// File upload rate limiter
const uploadLimiter = createRateLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 20, // Limit each IP to 20 uploads per hour
    message: 'Too many file uploads from this IP, please try again later.',
    keyGenerator: (req) => `upload:${req.ip}`,
});

// Chat/messaging rate limiter
const messageLimiter = createRateLimiter({
    windowMs: 60 * 1000, // 1 minute
    max: 30, // Limit each user to 30 messages per minute
    message: 'You are sending messages too quickly, please slow down.',
    keyGenerator: (req) => `message:${req.user?.id || req.ip}`,
    skip: (req) => !req.user, // Skip if user is not authenticated
});

// Search rate limiter
const searchLimiter = createRateLimiter({
    windowMs: 60 * 1000, // 1 minute
    max: 60, // Limit each IP to 60 search requests per minute
    message: 'Too many search requests, please slow down.',
    keyGenerator: (req) => `search:${req.ip}`,
});

// User-specific rate limiter (requires authentication)
const createUserLimiter = (options = {}) => {
    return createRateLimiter({
        ...options,
        keyGenerator: (req) => `user:${req.user?.id || req.ip}`,
        skip: (req) => !req.user, // Skip if user is not authenticated
    });
};

// IP-based rate limiter with custom key prefix
const createIPLimiter = (prefix, options = {}) => {
    return createRateLimiter({
        ...options,
        keyGenerator: (req) => `${prefix}:${req.ip}`,
    });
};

module.exports = {
    createRateLimiter,
    apiLimiter,
    authLimiter,
    registerLimiter,
    passwordResetLimiter,
    uploadLimiter,
    messageLimiter,
    searchLimiter,
    createUserLimiter,
    createIPLimiter,
};
