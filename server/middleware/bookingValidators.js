const { body, param } = require('express-validator');

// Validation for creating a booking
exports.validateCreateBooking = [
	body('date')
		.notEmpty()
		.withMessage('Date is required')
		.isISO8601()
		.withMessage('Date must be a valid ISO 8601 date')
		.custom((value) => {
			const bookingDate = new Date(value);
			const now = new Date();
			if (bookingDate < now) {
				throw new Error('Booking date cannot be in the past');
			}
			return true;
		}),

	body('email')
		.notEmpty()
		.withMessage('Email is required')
		.isEmail()
		.withMessage('Please provide a valid email')
		.normalizeEmail(),

	body('bookedBy')
		.notEmpty()
		.withMessage('Booked by name is required')
		.isLength({ min: 2, max: 100 })
		.withMessage('Name must be between 2 and 100 characters'),

	body('phone')
		.notEmpty()
		.withMessage('Phone number is required')
		.matches(/^[0-9]{10,15}$/)
		.withMessage('Phone number must be 10-15 digits'),

	body('sessionType')
		.optional()
		.isIn(['Discovery', 'Transformation', 'Follow-up'])
		.withMessage(
			'Session type must be Discovery, Transformation, or Follow-up',
		),
];

// Validation for updating booking status
exports.validateUpdateStatus = [
	param('id').isMongoId().withMessage('Invalid booking ID'),

	body('status')
		.notEmpty()
		.withMessage('Status is required')
		.isIn(['scheduled', 'completed', 'cancelled'])
		.withMessage('Status must be scheduled, completed, or cancelled'),
];

// Validation for booking ID parameter
exports.validateBookingId = [
	param('id').isMongoId().withMessage('Invalid booking ID'),
];

// Validation for email parameter
exports.validateEmailParam = [
	param('email')
		.isEmail()
		.withMessage('Please provide a valid email')
		.normalizeEmail(),
];
