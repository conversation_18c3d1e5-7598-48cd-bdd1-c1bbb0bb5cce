const User = require('../models/User');
const jwtUtils = require('../utils/jwtUtils');
const emailService = require('../utils/emailService');
const googleAuthService = require('../utils/googleAuth');
const redisService = require('../utils/redisService');
const bcrypt = require('bcryptjs');

// Helper function to generate OTP
const generateOTP = () => {
	return Math.floor(100000 + Math.random() * 900000).toString();
};

// Register User - Store temporarily until email verification
exports.registerUser = async (req, res) => {
	try {
		const { name, email, password } = req.body;

		// Validate input
		if (!name || !email || !password) {
			return res.status(400).json({
				success: false,
				message: 'Name, email, and password are required',
			});
		}

		// Check if user already exists in database
		const existingUser = await User.findOne({ email: email.toLowerCase() });
		if (existingUser) {
			return res.status(400).json({
				success: false,
				message: 'Email already registered and verified',
			});
		}

		// Check if there's a pending registration
		const pendingUserKey = `pending_user:${email.toLowerCase()}`;
		const existingPendingUser = await redisService.get(pendingUserKey);

		if (existingPendingUser) {
			const timeDiff = Date.now() - existingPendingUser.otpGeneratedAt;
			if (timeDiff < 10 * 60 * 1000) {
				return res.status(400).json({
					success: false,
					message:
						'Registration already pending. Please verify your email or wait for OTP to expire.',
				});
			}
		}

		// Generate OTP
		const otp = generateOTP();
		const otpExpiresAt = Date.now() + 10 * 60 * 1000;

		// Store user data temporarily in Redis (password will be hashed when saved to DB)
		const tempUserData = {
			name: name.trim(),
			email: email.toLowerCase(),
			password: password, // Store plain password temporarily - will be hashed by model
			otp,
			otpExpiresAt,
			otpGeneratedAt: Date.now(),
			otpAttempts: 0,
			createdAt: Date.now(),
		};

		await redisService.set(pendingUserKey, tempUserData, 24 * 60 * 60);
		await emailService.sendOTPEmail(email, name, otp);

		res.status(200).json({
			success: true,
			message:
				'Registration initiated. Please check your email for verification code.',
			data: {
				email: email.toLowerCase(),
				otpExpiresIn: 10 * 60,
			},
		});
	} catch (error) {
		console.error('Register User Error:', error);
		res.status(500).json({
			success: false,
			message: 'Registration failed. Please try again.',
		});
	}
};

// Login - Enhanced with security checks
exports.login = async (req, res) => {
	try {
		const { email, password } = req.body;

		if (!email || !password) {
			return res.status(400).json({
				success: false,
				message: 'Email and password are required',
			});
		}

		// Find user and include password for verification
		const user = await User.findOne({ email: email.toLowerCase() }).select(
			'+password +loginAttempts +lockUntil',
		);

		if (!user) {
			return res.status(401).json({
				success: false,
				message: 'Invalid email or password',
			});
		}

		// Check if account is locked
		if (user.isLocked) {
			return res.status(423).json({
				success: false,
				message:
					'Account is temporarily locked due to too many failed login attempts. Please try again later.',
			});
		}

		// Check if email is verified
		if (!user.isEmailVerified) {
			return res.status(403).json({
				success: false,
				message: 'Please verify your email address before logging in.',
			});
		}

		// Check account status
		if (user.status === 'suspended') {
			return res.status(403).json({
				success: false,
				message: 'Account is suspended. Please contact support.',
			});
		}

		// Verify password
		const isPasswordValid = await bcrypt.compare(password, user.password);

		if (!isPasswordValid) {
			// Increment login attempts
			await user.incLoginAttempts();
			return res.status(401).json({
				success: false,
				message: 'Invalid email or password',
			});
		}

		// Reset login attempts on successful login
		if (user.loginAttempts > 0) {
			await user.resetLoginAttempts();
		}

		// Update last login
		user.lastLogin = new Date();
		await user.save();

		// Generate tokens
		const tokens = jwtUtils.generateTokens(user);

		res.status(200).json({
			success: true,
			message: 'Login successful',
			data: {
				user: {
					id: user._id,
					name: user.name,
					email: user.email,
					roles: user.roles,
					isEmailVerified: user.isEmailVerified,
					status: user.status,
					lastLogin: user.lastLogin,
				},
				tokens,
			},
		});
	} catch (error) {
		console.error('Login Error:', error);
		res.status(500).json({
			success: false,
			message: 'Login failed. Please try again.',
		});
	}
};

// Verify Email - Create user in database after successful verification
exports.verifyEmail = async (req, res) => {
	try {
		const { email, otp } = req.body;

		if (!email || !otp) {
			return res.status(400).json({
				success: false,
				message: 'Email and OTP are required',
			});
		}

		const pendingUserKey = `pending_user:${email.toLowerCase()}`;
		const pendingUserData = await redisService.get(pendingUserKey);

		if (!pendingUserData) {
			return res.status(400).json({
				success: false,
				message: 'No pending registration found. Please register again.',
			});
		}

		if (Date.now() > pendingUserData.otpExpiresAt) {
			await redisService.del(pendingUserKey);
			return res.status(400).json({
				success: false,
				message: 'OTP has expired. Please register again.',
			});
		}

		if (pendingUserData.otpAttempts >= 3) {
			await redisService.del(pendingUserKey);
			return res.status(400).json({
				success: false,
				message: 'Too many failed attempts. Please register again.',
			});
		}

		if (pendingUserData.otp !== otp.toString()) {
			pendingUserData.otpAttempts += 1;
			await redisService.set(pendingUserKey, pendingUserData, 24 * 60 * 60);

			return res.status(400).json({
				success: false,
				message: `Invalid OTP. ${
					3 - pendingUserData.otpAttempts
				} attempts remaining.`,
			});
		}

		const existingUser = await User.findOne({ email: email.toLowerCase() });
		if (existingUser) {
			await redisService.del(pendingUserKey);
			return res.status(400).json({
				success: false,
				message: 'User already exists. Please login instead.',
			});
		}

		// Create user - model will handle password hashing automatically
		const newUser = new User({
			name: pendingUserData.name,
			email: pendingUserData.email,
			password: pendingUserData.password, // Plain password - model will hash it
			isEmailVerified: true,
			status: 'active', // Explicitly set to active
			roles: ['user'],
		});

		await newUser.save();

		await redisService.del(pendingUserKey);
		await emailService.sendWelcomeEmail(newUser.email, newUser.name);

		const tokens = jwtUtils.generateTokens(newUser);

		res.status(201).json({
			success: true,
			message: 'Email verified successfully. Registration completed!',
			data: {
				user: {
					id: newUser._id,
					name: newUser.name,
					email: newUser.email,
					roles: newUser.roles,
					isEmailVerified: newUser.isEmailVerified,
					status: newUser.status,
				},
				tokens,
			},
		});
	} catch (error) {
		console.error('Verify Email Error:', error);
		res.status(500).json({
			success: false,
			message: 'Email verification failed. Please try again.',
		});
	}
};

// Resend Verification Code - For pending registrations
exports.resendVerificationCode = async (req, res) => {
	try {
		const { email } = req.body;

		if (!email) {
			return res.status(400).json({
				success: false,
				message: 'Email is required',
			});
		}

		// Check if user already exists and is verified
		const existingUser = await User.findOne({ email: email.toLowerCase() });
		if (existingUser && existingUser.isEmailVerified) {
			return res.status(400).json({
				success: false,
				message: 'Email already verified. Please login instead.',
			});
		}

		// Check for pending registration
		const pendingUserKey = `pending_user:${email.toLowerCase()}`;
		const pendingUserData = await redisService.get(pendingUserKey);

		if (!pendingUserData) {
			return res.status(404).json({
				success: false,
				message: 'No pending registration found. Please register first.',
			});
		}

		// Check if we can resend (rate limiting)
		const lastOtpTime = pendingUserData.otpGeneratedAt;
		const timeSinceLastOtp = Date.now() - lastOtpTime;
		const minResendInterval = 2 * 60 * 1000; // 2 minutes

		if (timeSinceLastOtp < minResendInterval) {
			const waitTime = Math.ceil((minResendInterval - timeSinceLastOtp) / 1000);
			return res.status(429).json({
				success: false,
				message: `Please wait ${waitTime} seconds before requesting a new code.`,
			});
		}

		// Generate new OTP
		const newOtp = generateOTP();
		const newOtpExpiresAt = Date.now() + 10 * 60 * 1000; // 10 minutes from now

		// Update pending user data with new OTP
		const updatedUserData = {
			...pendingUserData,
			otp: newOtp,
			otpExpiresAt: newOtpExpiresAt,
			otpGeneratedAt: Date.now(),
			otpAttempts: 0, // Reset attempts
		};

		// Update Redis with new data (extend expiry to 24 hours)
		await redisService.set(pendingUserKey, updatedUserData, 24 * 60 * 60);

		// Send new OTP email
		await emailService.sendOTPEmail(email, pendingUserData.name, newOtp);

		res.status(200).json({
			success: true,
			message: 'New verification code sent successfully.',
			data: {
				email: email.toLowerCase(),
				otpExpiresIn: 10 * 60, // 10 minutes in seconds
			},
		});
	} catch (error) {
		console.error('Resend Verification Code Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to resend verification code. Please try again.',
		});
	}
};

// Forgot Password - Send OTP for password reset
exports.forgotPassword = async (req, res) => {
	try {
		const { email } = req.body;

		if (!email) {
			return res.status(400).json({
				success: false,
				message: 'Email is required',
			});
		}

		// Find user by email
		const user = await User.findOne({ email: email.toLowerCase() });

		if (!user) {
			return res.status(404).json({
				success: false,
				message: 'No account found with this email address',
			});
		}

		// Check if user's email is verified
		if (!user.isEmailVerified) {
			return res.status(403).json({
				success: false,
				message: 'Please verify your email address first',
			});
		}

		// Check if there's already a pending password reset
		const resetKey = `password_reset:${email.toLowerCase()}`;
		const existingReset = await redisService.get(resetKey);

		if (existingReset) {
			const timeDiff = Date.now() - existingReset.otpGeneratedAt;
			const minResendInterval = 2 * 60 * 1000; // 2 minutes

			if (timeDiff < minResendInterval) {
				const waitTime = Math.ceil((minResendInterval - timeDiff) / 1000);
				return res.status(429).json({
					success: false,
					message: `Please wait ${waitTime} seconds before requesting a new code.`,
				});
			}
		}

		// Generate OTP for password reset
		const otp = generateOTP();
		const otpExpiresAt = Date.now() + 10 * 60 * 1000; // 10 minutes

		// Store password reset data in Redis
		const resetData = {
			email: email.toLowerCase(),
			userId: user._id.toString(),
			otp,
			otpExpiresAt,
			otpGeneratedAt: Date.now(),
			otpAttempts: 0,
			verified: false,
		};

		// Store in Redis with 1 hour expiry
		await redisService.set(resetKey, resetData, 60 * 60);

		// Send OTP email for password reset
		await emailService.sendPasswordResetOTPEmail(email, user.name, otp);

		res.status(200).json({
			success: true,
			message: 'Password reset code sent to your email',
			data: {
				email: email.toLowerCase(),
				otpExpiresIn: 10 * 60, // 10 minutes in seconds
			},
		});
	} catch (error) {
		console.error('Forgot Password Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to send password reset code. Please try again.',
		});
	}
};

// Verify Password Reset OTP
exports.verifyPasswordResetOTP = async (req, res) => {
	try {
		const { email, otp } = req.body;

		if (!email || !otp) {
			return res.status(400).json({
				success: false,
				message: 'Email and OTP are required',
			});
		}

		const resetKey = `password_reset:${email.toLowerCase()}`;
		const resetData = await redisService.get(resetKey);

		if (!resetData) {
			return res.status(400).json({
				success: false,
				message: 'No password reset request found. Please request a new one.',
			});
		}

		// Check if OTP has expired
		if (Date.now() > resetData.otpExpiresAt) {
			await redisService.del(resetKey);
			return res.status(400).json({
				success: false,
				message: 'OTP has expired. Please request a new password reset.',
			});
		}

		// Check attempt limit
		if (resetData.otpAttempts >= 3) {
			await redisService.del(resetKey);
			return res.status(400).json({
				success: false,
				message:
					'Too many failed attempts. Please request a new password reset.',
			});
		}

		// Verify OTP
		if (resetData.otp !== otp.toString()) {
			resetData.otpAttempts += 1;
			await redisService.set(resetKey, resetData, 60 * 60);

			return res.status(400).json({
				success: false,
				message: `Invalid OTP. ${
					3 - resetData.otpAttempts
				} attempts remaining.`,
			});
		}

		// Mark as verified and extend expiry for password change
		resetData.verified = true;
		resetData.verifiedAt = Date.now();
		await redisService.set(resetKey, resetData, 30 * 60); // 30 minutes to change password

		res.status(200).json({
			success: true,
			message: 'OTP verified successfully. You can now reset your password.',
			data: {
				email: email.toLowerCase(),
				resetToken: resetKey, // You can use this as a reference for the next step
			},
		});
	} catch (error) {
		console.error('Verify Password Reset OTP Error:', error);
		res.status(500).json({
			success: false,
			message: 'OTP verification failed. Please try again.',
		});
	}
};

// Reset Password - Change password after OTP verification
exports.resetPassword = async (req, res) => {
	try {
		const { email, newPassword } = req.body;

		if (!email || !newPassword) {
			return res.status(400).json({
				success: false,
				message: 'Email and new password are required',
			});
		}

		// Validate password strength (add your own validation rules)
		if (newPassword.length < 6) {
			return res.status(400).json({
				success: false,
				message: 'Password must be at least 6 characters long',
			});
		}

		const resetKey = `password_reset:${email.toLowerCase()}`;
		const resetData = await redisService.get(resetKey);

		if (!resetData) {
			return res.status(400).json({
				success: false,
				message: 'No password reset session found. Please start over.',
			});
		}

		// Check if OTP was verified
		if (!resetData.verified) {
			return res.status(400).json({
				success: false,
				message: 'Please verify your OTP first before resetting password.',
			});
		}

		// Check if verification has expired (30 minutes after OTP verification)
		if (Date.now() - resetData.verifiedAt > 30 * 60 * 1000) {
			await redisService.del(resetKey);
			return res.status(400).json({
				success: false,
				message: 'Password reset session expired. Please start over.',
			});
		}

		// Find user and update password
		const user = await User.findById(resetData.userId);
		if (!user) {
			await redisService.del(resetKey);
			return res.status(404).json({
				success: false,
				message: 'User not found',
			});
		}

		// Hash new password
		const saltRounds = 12;
		const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

		// Update user password
		user.password = hashedPassword;
		user.passwordChangedAt = new Date();
		await user.save();

		// Clean up Redis
		await redisService.del(resetKey);

		// Send confirmation email
		await emailService.sendPasswordChangeConfirmationEmail(
			user.email,
			user.name,
		);

		res.status(200).json({
			success: true,
			message: 'Password reset successfully',
		});
	} catch (error) {
		console.error('Reset Password Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to reset password. Please try again.',
		});
	}
};

// Resend Password Reset OTP
exports.resendPasswordResetOTP = async (req, res) => {
	try {
		const { email } = req.body;

		if (!email) {
			return res.status(400).json({
				success: false,
				message: 'Email is required',
			});
		}

		const resetKey = `password_reset:${email.toLowerCase()}`;
		const resetData = await redisService.get(resetKey);

		if (!resetData) {
			return res.status(404).json({
				success: false,
				message: 'No password reset request found. Please start over.',
			});
		}

		// Check rate limiting
		const timeSinceLastOtp = Date.now() - resetData.otpGeneratedAt;
		const minResendInterval = 2 * 60 * 1000; // 2 minutes

		if (timeSinceLastOtp < minResendInterval) {
			const waitTime = Math.ceil((minResendInterval - timeSinceLastOtp) / 1000);
			return res.status(429).json({
				success: false,
				message: `Please wait ${waitTime} seconds before requesting a new code.`,
			});
		}

		// Generate new OTP
		const newOtp = generateOTP();
		const newOtpExpiresAt = Date.now() + 10 * 60 * 1000;

		// Update reset data
		const updatedResetData = {
			...resetData,
			otp: newOtp,
			otpExpiresAt: newOtpExpiresAt,
			otpGeneratedAt: Date.now(),
			otpAttempts: 0,
			verified: false, // Reset verification status
		};

		await redisService.set(resetKey, updatedResetData, 60 * 60);

		// Get user for name
		const user = await User.findById(resetData.userId);
		if (!user) {
			return res.status(404).json({
				success: false,
				message: 'User not found',
			});
		}

		// Send new OTP
		await emailService.sendPasswordResetOTPEmail(email, user.name, newOtp);

		res.status(200).json({
			success: true,
			message: 'New password reset code sent successfully.',
			data: {
				email: email.toLowerCase(),
				otpExpiresIn: 10 * 60,
			},
		});
	} catch (error) {
		console.error('Resend Password Reset OTP Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to resend password reset code. Please try again.',
		});
	}
};

// Google Login
exports.googleLogin = async (req, res) => {
	try {
		const { idToken } = req.body;
		const googleUser = await googleAuthService.verifyIdToken(idToken);

		if (!googleUser.emailVerified) {
			return res.status(403).json({
				success: false,
				message: 'Google email not verified',
			});
		}

		let user = await User.findOne({ googleId: googleUser.googleId });

		if (!user) {
			user = await User.findOne({ email: googleUser.email });

			if (user && !user.googleId) {
				// Link existing account with Google
				user.googleId = googleUser.googleId;
				user.isEmailVerified = true;
			} else {
				// Create new user
				user = new User({
					googleId: googleUser.googleId,
					email: googleUser.email,
					avatar: googleUser.picture,
					name: googleUser.name,
					isEmailVerified: true,
					status: 'active',
					roles: ['user'],
				});
			}
		}

		// Update last login
		user.lastLogin = new Date();
		await user.save();

		const tokens = jwtUtils.generateTokens(user);

		res.status(200).json({
			success: true,
			message: 'Google login successful',
			data: {
				user: {
					id: user._id,
					name: user.name,
					email: user.email,
					roles: user.roles,
					avatar: googleUser.picture,
					isEmailVerified: user.isEmailVerified,
					status: user.status,
					lastLogin: user.lastLogin,
				},
				tokens,
			},
		});
	} catch (error) {
		console.error('Google Login Error:', error);
		res.status(500).json({
			success: false,
			message: 'Google login failed',
		});
	}
};
