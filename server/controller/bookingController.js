const { validationResult } = require('express-validator');
const BookingSlot = require('../models/BookingSlot');

// Create a new booking
exports.createBooking = async (req, res) => {
	try {
		const errors = validationResult(req);
		if (!errors.isEmpty()) {
			return res.status(400).json({
				success: false,
				message: 'Validation failed',
				errors: errors.array(),
			});
		}

		const { date, email, bookedBy, phone, sessionType } = req.body;

		// Check if the slot already exists for this date
		const existingSlot = await BookingSlot.findOne({ date: new Date(date) });
		if (existingSlot && existingSlot.isBooked) {
			return res.status(409).json({
				success: false,
				message: 'This time slot is already booked',
			});
		}

		// Check if user already has a booking for the same date
		const existingUserBooking = await BookingSlot.findOne({
			email: email.toLowerCase(),
			date: new Date(date),
		});

		if (existingUserBooking) {
			return res.status(409).json({
				success: false,
				message: 'You already have a booking for this date',
			});
		}

		// Create new booking
		const newBooking = new BookingSlot({
			date: new Date(date),
			email: email.toLowerCase(),
			bookedBy,
			phone,
			sessionType: sessionType || 'Discovery',
			isBooked: true,
			status: 'scheduled',
		});

		await newBooking.save();

		res.status(201).json({
			success: true,
			message: 'Booking created successfully',
			data: newBooking,
		});
	} catch (error) {
		console.error('Create booking error:', error);
		res.status(500).json({
			success: false,
			message: 'Internal server error',
			error: error.message,
		});
	}
};

// Get all bookings with pagination and filtering (for admin interface)
exports.getAllBookings = async (req, res) => {
	try {
		const page = parseInt(req.query.page) || 1;
		const limit = parseInt(req.query.limit) || 10;
		const skip = (page - 1) * limit;

		// Build filter object
		const filter = {};
		if (req.query.status) {
			filter.status = req.query.status;
		}
		if (req.query.sessionType) {
			filter.sessionType = req.query.sessionType;
		}
		if (req.query.email) {
			filter.email = { $regex: req.query.email, $options: 'i' };
		}
		if (req.query.bookedBy) {
			filter.bookedBy = { $regex: req.query.bookedBy, $options: 'i' };
		}

		const bookings = await BookingSlot.find(filter)
			.sort({ date: -1 }) // Most recent first
			.skip(skip)
			.limit(limit);

		const total = await BookingSlot.countDocuments(filter);
		const totalPages = Math.ceil(total / limit);

		res.status(200).json({
			success: true,
			data: bookings,
			pagination: {
				currentPage: page,
				totalPages,
				totalItems: total,
				hasNext: page < totalPages,
				hasPrev: page > 1,
			},
		});
	} catch (error) {
		console.error('Get bookings error:', error);
		res.status(500).json({
			success: false,
			message: 'Internal server error',
			error: error.message,
		});
	}
};

// Get single booking by ID
exports.getBookingById = async (req, res) => {
	try {
		const booking = await BookingSlot.findById(req.params.id);

		if (!booking) {
			return res.status(404).json({
				success: false,
				message: 'Booking not found',
			});
		}

		res.status(200).json({
			success: true,
			data: booking,
		});
	} catch (error) {
		console.error('Get booking error:', error);
		res.status(500).json({
			success: false,
			message: 'Internal server error',
			error: error.message,
		});
	}
};

// Update booking status (for admin actions like complete/cancel)
exports.updateBookingStatus = async (req, res) => {
	try {
		const errors = validationResult(req);
		if (!errors.isEmpty()) {
			return res.status(400).json({
				success: false,
				message: 'Validation failed',
				errors: errors.array(),
			});
		}

		const { id } = req.params;
		const { status } = req.body;

		const booking = await BookingSlot.findById(id);
		if (!booking) {
			return res.status(404).json({
				success: false,
				message: 'Booking not found',
			});
		}

		booking.status = status;
		await booking.save();

		res.status(200).json({
			success: true,
			message: `Booking ${status} successfully`,
			data: booking,
		});
	} catch (error) {
		console.error('Update booking error:', error);
		res.status(500).json({
			success: false,
			message: 'Internal server error',
			error: error.message,
		});
	}
};

// Cancel booking
exports.cancelBooking = async (req, res) => {
	try {
		const { id } = req.params;

		const booking = await BookingSlot.findById(id);
		if (!booking) {
			return res.status(404).json({
				success: false,
				message: 'Booking not found',
			});
		}

		if (booking.status === 'completed') {
			return res.status(400).json({
				success: false,
				message: 'Cannot cancel completed booking',
			});
		}

		booking.status = 'cancelled';
		await booking.save();

		res.status(200).json({
			success: true,
			message: 'Booking cancelled successfully',
			data: booking,
		});
	} catch (error) {
		console.error('Cancel booking error:', error);
		res.status(500).json({
			success: false,
			message: 'Internal server error',
			error: error.message,
		});
	}
};

// Get available time slots
exports.getAvailableSlots = async (req, res) => {
	try {
		const { date, startDate, endDate } = req.query;

		let dateFilter = {};
		if (date) {
			// Get slots for specific date
			const targetDate = new Date(date);
			const startOfDay = new Date(targetDate);
			startOfDay.setHours(0, 0, 0, 0);
			const endOfDay = new Date(targetDate);
			endOfDay.setHours(23, 59, 59, 999);

			dateFilter = {
				date: { $gte: startOfDay, $lte: endOfDay },
			};
		} else if (startDate && endDate) {
			// Get slots for date range
			dateFilter = {
				date: { $gte: new Date(startDate), $lte: new Date(endDate) },
			};
		}

		const bookedSlots = await BookingSlot.find({
			...dateFilter,
			isBooked: true,
			status: { $ne: 'cancelled' },
		});

		res.status(200).json({
			success: true,
			data: bookedSlots,
		});
	} catch (error) {
		console.error('Get available slots error:', error);
		res.status(500).json({
			success: false,
			message: 'Internal server error',
			error: error.message,
		});
	}
};

// Get user's bookings by email
exports.getUserBookings = async (req, res) => {
	try {
		const { email } = req.params;

		const bookings = await BookingSlot.find({
			email: email.toLowerCase(),
			isBooked: true,
		}).sort({ date: -1 });

		res.status(200).json({
			success: true,
			data: bookings,
		});
	} catch (error) {
		console.error('Get user bookings error:', error);
		res.status(500).json({
			success: false,
			message: 'Internal server error',
			error: error.message,
		});
	}
};

// Delete booking (admin only)
exports.deleteBooking = async (req, res) => {
	try {
		const { id } = req.params;

		const booking = await BookingSlot.findByIdAndDelete(id);
		if (!booking) {
			return res.status(404).json({
				success: false,
				message: 'Booking not found',
			});
		}

		res.status(200).json({
			success: true,
			message: 'Booking deleted successfully',
		});
	} catch (error) {
		console.error('Delete booking error:', error);
		res.status(500).json({
			success: false,
			message: 'Internal server error',
			error: error.message,
		});
	}
};
