const Diary = require('../models/Diary');
const { validationResult } = require('express-validator');
const cloudinary = require('../config/cloudinary');
const multer = require('multer');

// Configure multer for memory storage
const upload = multer({ 
	storage: multer.memoryStorage(),
	limits: {
		fileSize: 5 * 1024 * 1024, // 5MB limit
	},
	fileFilter: (req, file, cb) => {
		if (file.mimetype.startsWith('image/')) {
			cb(null, true);
		} else {
			cb(new Error('Only image files are allowed!'), false);
		}
	}
});

/**
 * Create a new diary entry
 */
exports.createDiaryEntry = async (req, res) => {
	try {
		const errors = validationResult(req);
		if (!errors.isEmpty()) {
			return res.status(400).json({
				success: false,
				message: 'Validation failed',
				errors: errors.array(),
			});
		}

		const { title, story, date, image } = req.body;
		const userId = req.user._id;

		// Check if entry already exists for this date
		const existingEntry = await Diary.findOne({
			userId,
			date: new Date(date)
		});

		if (existingEntry) {
			return res.status(400).json({
				success: false,
				message: 'A diary entry already exists for this date',
			});
		}

		const diaryEntry = new Diary({
			userId,
			title: title.trim(),
			story: story.trim(),
			date: new Date(date),
			image: image || null,
		});

		await diaryEntry.save();

		res.status(201).json({
			success: true,
			message: 'Diary entry created successfully',
			data: {
				entry: diaryEntry,
			},
		});
	} catch (error) {
		console.error('Create Diary Entry Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to create diary entry',
		});
	}
};

/**
 * Get all diary entries for the authenticated user
 */
exports.getUserDiaryEntries = async (req, res) => {
	try {
		const userId = req.user._id;
		const { page = 1, limit = 10, search, month, year } = req.query;

		const query = { userId };
		
		// Add search functionality
		if (search) {
			query.$or = [
				{ title: { $regex: search, $options: 'i' } },
				{ story: { $regex: search, $options: 'i' } },
			];
		}

		// Filter by month and year if provided
		if (month && year) {
			const startDate = new Date(year, month - 1, 1);
			const endDate = new Date(year, month, 0);
			query.date = { $gte: startDate, $lte: endDate };
		} else if (year) {
			const startDate = new Date(year, 0, 1);
			const endDate = new Date(year, 11, 31);
			query.date = { $gte: startDate, $lte: endDate };
		}

		const options = {
			page: parseInt(page),
			limit: parseInt(limit),
			sort: { date: -1 },
		};

		const entries = await Diary.find(query)
			.sort(options.sort)
			.limit(options.limit * options.page)
			.skip((options.page - 1) * options.limit)
			.populate('userId', 'name email');

		const total = await Diary.countDocuments(query);

		res.status(200).json({
			success: true,
			message: 'Diary entries retrieved successfully',
			data: {
				entries,
				pagination: {
					page: options.page,
					limit: options.limit,
					total,
					totalPages: Math.ceil(total / options.limit),
					hasNext: options.page < Math.ceil(total / options.limit),
					hasPrev: options.page > 1,
				},
			},
		});
	} catch (error) {
		console.error('Get User Diary Entries Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to retrieve diary entries',
		});
	}
};

/**
 * Get a specific diary entry by ID
 */
exports.getDiaryEntryById = async (req, res) => {
	try {
		const { id } = req.params;
		const userId = req.user._id;

		const entry = await Diary.findOne({
			_id: id,
			userId
		}).populate('userId', 'name');

		if (!entry) {
			return res.status(404).json({
				success: false,
				message: 'Diary entry not found',
			});
		}

		res.status(200).json({
			success: true,
			message: 'Diary entry retrieved successfully',
			data: {
				entry,
			},
		});
	} catch (error) {
		console.error('Get Diary Entry By ID Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to retrieve diary entry',
		});
	}
};

/**
 * Get diary entry by date
 */
exports.getDiaryEntryByDate = async (req, res) => {
	try {
		const { date } = req.params;
		const userId = req.user._id;

		const entry = await Diary.findOne({
			userId,
			date: new Date(date)
		}).populate('userId', 'name');

		if (!entry) {
			return res.status(404).json({
				success: false,
				message: 'No diary entry found for this date',
			});
		}

		res.status(200).json({
			success: true,
			message: 'Diary entry retrieved successfully',
			data: {
				entry,
			},
		});
	} catch (error) {
		console.error('Get Diary Entry By Date Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to retrieve diary entry',
		});
	}
};

/**
 * Update a diary entry
 */
exports.updateDiaryEntry = async (req, res) => {
	try {
		const errors = validationResult(req);
		if (!errors.isEmpty()) {
			return res.status(400).json({
				success: false,
				message: 'Validation failed',
				errors: errors.array(),
			});
		}

		const { id } = req.params;
		const userId = req.user._id;
		const { title, story, image } = req.body;

		// Find the entry and ensure user owns it
		const entry = await Diary.findOne({ _id: id, userId });
		if (!entry) {
			return res.status(404).json({
				success: false,
				message: 'Diary entry not found or access denied',
			});
		}

		// Update fields
		if (title) entry.title = title.trim();
		if (story) entry.story = story.trim();
		if (image !== undefined) entry.image = image;

		await entry.save();

		res.status(200).json({
			success: true,
			message: 'Diary entry updated successfully',
			data: {
				entry,
			},
		});
	} catch (error) {
		console.error('Update Diary Entry Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to update diary entry',
		});
	}
};

/**
 * Delete a diary entry
 */
exports.deleteDiaryEntry = async (req, res) => {
	try {
		const { id } = req.params;
		const userId = req.user._id;

		const entry = await Diary.findOneAndDelete({ _id: id, userId });
		if (!entry) {
			return res.status(404).json({
				success: false,
				message: 'Diary entry not found or access denied',
			});
		}

		res.status(200).json({
			success: true,
			message: 'Diary entry deleted successfully',
			data: {
				entry,
			},
		});
	} catch (error) {
		console.error('Delete Diary Entry Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to delete diary entry',
		});
	}
};

/**
 * Get recent diary entries (last 5)
 */
exports.getRecentDiaryEntries = async (req, res) => {
	try {
		const userId = req.user._id;
		const limit = 5;

		const entries = await Diary.find({ userId })
			.sort({ date: -1 })
			.limit(limit)
			.populate('userId', 'name');

		res.status(200).json({
			success: true,
			message: 'Recent diary entries retrieved successfully',
			data: {
				entries,
			},
		});
	} catch (error) {
		console.error('Get Recent Diary Entries Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to retrieve recent diary entries',
		});
	}
};

/**
 * Upload image to Cloudinary
 */
exports.uploadImage = async (req, res) => {
	try {
		if (!req.file) {
			return res.status(400).json({
				success: false,
				message: 'No image file provided',
			});
		}

		// Upload to Cloudinary using the uploader
		const result = await new Promise((resolve, reject) => {
			cloudinary.uploader.upload_stream(
				{
					folder: 'diary-images',
					transformation: [
						{ width: 1200, height: 800, crop: 'limit', quality: 'auto' }
					],
					resource_type: 'auto'
				},
				(error, result) => {
					if (error) {
						reject(error);
					} else {
						resolve(result);
					}
				}
			).end(req.file.buffer);
		});

		res.status(200).json({
			success: true,
			message: 'Image uploaded successfully',
			data: {
				url: result.secure_url,
				public_id: result.public_id,
			},
		});
	} catch (error) {
		console.error('Upload Image Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to upload image',
		});
	}
};

// Export multer upload middleware
exports.uploadMiddleware = upload.single('image');
