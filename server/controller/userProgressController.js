const UserProgress = require('../models/UserProgress');
const Lesson = require('../models/Lesson');

// Update user progress (video progress, reflections, notes)
exports.updateUserProgress = async (req, res) => {
	try {
		const { lessonId, videoProgress, watchTime, reflections, notes } = req.body;
		const userId = req.user.id;

		// Find or create user progress
		let userProgress = await UserProgress.findOne({ userId, lessonId });

		if (userProgress) {
			// Update existing progress
			if (videoProgress !== undefined) {
				userProgress.videoProgress = videoProgress;
			}
			if (watchTime !== undefined) {
				userProgress.watchTime = watchTime;
			}
			if (reflections !== undefined) {
				userProgress.reflections = reflections;
			}
			if (notes !== undefined) {
				userProgress.notes = notes;
			}
			userProgress.lastWatchedAt = new Date();

			// Mark as completed if video progress is >= 90%
			if (videoProgress >= 0.9) {
				userProgress.isCompleted = true;
			}

			await userProgress.save();
		} else {
			// Create new progress
			userProgress = new UserProgress({
				userId,
				lessonId,
				videoProgress: videoProgress || 0,
				watchTime: watchTime || 0,
				reflections: reflections || [],
				notes: notes || '',
				isCompleted: videoProgress >= 0.9,
			});
			await userProgress.save();
		}

		res.status(200).json({
			status: 'success',
			data: userProgress,
		});
	} catch (error) {
		console.error('Error updating user progress:', error);
		res.status(500).json({
			status: 'error',
			message: 'Failed to update user progress',
		});
	}
};

// Get user progress for a specific lesson
exports.getUserProgress = async (req, res) => {
	try {
		const { lessonId } = req.params;
		const userId = req.user.id;

		const userProgress = await UserProgress.findOne({ userId, lessonId });

		if (!userProgress) {
			return res.status(404).json({
				status: 'error',
				message: 'User progress not found',
			});
		}

		res.status(200).json({
			status: 'success',
			data: userProgress,
		});
	} catch (error) {
		console.error('Error fetching user progress:', error);
		res.status(500).json({
			status: 'error',
			message: 'Failed to fetch user progress',
		});
	}
};

// Get all user progress (for ongoing videos on homepage)
exports.getAllUserProgress = async (req, res) => {
	try {
		const userId = req.user.id;

		const userProgress = await UserProgress.find({ userId })
			.populate('lessonId', 'title thumbnail instructor category videoUrl')
			.sort({ lastWatchedAt: -1 });

		// Filter out completed lessons and only show those with progress > 0
		const ongoingProgress = userProgress.filter(
			(progress) => progress.videoProgress > 0 && progress.videoProgress < 0.9,
		);

		// Return only the most recently watched video (first item after sorting)
		const lastWatchedVideo =
			ongoingProgress.length > 0 ? [ongoingProgress[0]] : [];

		res.status(200).json({
			status: 'success',
			data: lastWatchedVideo,
		});
	} catch (error) {
		console.error('Error fetching user progress:', error);
		res.status(500).json({
			status: 'error',
			message: 'Failed to fetch user progress',
		});
	}
};

// Update reflection answer
exports.updateReflection = async (req, res) => {
	try {
		const { lessonId, questionIndex, answer } = req.body;
		const userId = req.user.id;

		let userProgress = await UserProgress.findOne({ userId, lessonId });

		if (!userProgress) {
			// Create new progress if it doesn't exist
			userProgress = new UserProgress({
				userId,
				lessonId,
				reflections: [],
			});
		}

		// Find existing reflection or create new one
		const existingReflectionIndex = userProgress.reflections.findIndex(
			(r) => r.questionIndex === questionIndex,
		);

		if (existingReflectionIndex !== -1) {
			// Update existing reflection
			userProgress.reflections[existingReflectionIndex].answer = answer;
			userProgress.reflections[existingReflectionIndex].updatedAt = new Date();
		} else {
			// Add new reflection
			userProgress.reflections.push({
				questionIndex,
				answer,
				createdAt: new Date(),
				updatedAt: new Date(),
			});
		}

		await userProgress.save();

		res.status(200).json({
			status: 'success',
			data: userProgress.reflections,
		});
	} catch (error) {
		console.error('Error updating reflection:', error);
		res.status(500).json({
			status: 'error',
			message: 'Failed to update reflection',
		});
	}
};

// Delete reflection answer
exports.deleteReflection = async (req, res) => {
	try {
		const { lessonId, questionIndex } = req.params;
		const userId = req.user.id;

		const userProgress = await UserProgress.findOne({ userId, lessonId });

		if (!userProgress) {
			return res.status(404).json({
				status: 'error',
				message: 'User progress not found',
			});
		}

		// Remove reflection
		userProgress.reflections = userProgress.reflections.filter(
			(r) => r.questionIndex !== parseInt(questionIndex),
		);

		await userProgress.save();

		res.status(200).json({
			status: 'success',
			data: userProgress.reflections,
		});
	} catch (error) {
		console.error('Error deleting reflection:', error);
		res.status(500).json({
			status: 'error',
			message: 'Failed to delete reflection',
		});
	}
};
