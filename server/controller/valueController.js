const Value = require('../models/Value');
const { validationResult } = require('express-validator');

/**
 * Create a new value
 */
exports.createValue = async (req, res) => {
	try {
		const errors = validationResult(req);
		if (!errors.isEmpty()) {
			return res.status(400).json({
				success: false,
				message: 'Validation failed',
				errors: errors.array(),
			});
		}

		const { name, definition, isPublic = false } = req.body;
		const userId = req.user._id;

		// Check if value with same name already exists for this user
		const existingValue = await Value.findOne({
			userId,
			name: { $regex: new RegExp(`^${name}$`, 'i') },
		});

		if (existingValue) {
			return res.status(400).json({
				success: false,
				message: 'A value with this name already exists',
			});
		}

		const value = new Value({
			userId,
			name: name.trim(),
			definition: definition,
			isPublic,
		});

		await value.save();

		res.status(201).json({
			success: true,
			message: 'Value created successfully',
			data: {
				value,
			},
		});
	} catch (error) {
		console.error('Create Value Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to create value',
		});
	}
};

/**
 * Get all values for the authenticated user
 */
exports.getUserValues = async (req, res) => {
	try {
		const userId = req.user._id;
		const { page = 1, limit = 10, search } = req.query;

		const query = { userId };
		if (search) {
			query.$or = [
				{ name: { $regex: search, $options: 'i' } },
				{ definition: { $regex: search, $options: 'i' } },
			];
		}

		const options = {
			page: parseInt(page),
			limit: parseInt(limit),
			sort: { createdAt: -1 },
		};

		const values = await Value.find(query)
			.sort(options.sort)
			.limit(options.limit * options.page)
			.skip((options.page - 1) * options.limit)
			.populate('userId', 'name email');

		const total = await Value.countDocuments(query);

		res.status(200).json({
			success: true,
			message: 'Values retrieved successfully',
			data: {
				values,
				pagination: {
					page: options.page,
					limit: options.limit,
					total,
					totalPages: Math.ceil(total / options.limit),
					hasNext: options.page < Math.ceil(total / options.limit),
					hasPrev: options.page > 1,
				},
			},
		});
	} catch (error) {
		console.error('Get User Values Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to retrieve values',
		});
	}
};

/**
 * Get public values from all users
 */
exports.getPublicValues = async (req, res) => {
	try {
		const { page = 1, limit = 10, search } = req.query;

		const query = { isPublic: true };
		if (search) {
			query.$or = [
				{ name: { $regex: search, $options: 'i' } },
				{ definition: { $regex: search, $options: 'i' } },
			];
		}

		const options = {
			page: parseInt(page),
			limit: parseInt(limit),
			sort: { createdAt: -1 },
		};

		const values = await Value.find(query)
			.sort(options.sort)
			.limit(options.limit * options.page)
			.skip((options.page - 1) * options.limit)
			.populate('userId', 'name');

		const total = await Value.countDocuments(query);

		res.status(200).json({
			success: true,
			message: 'Public values retrieved successfully',
			data: {
				values,
				pagination: {
					page: options.page,
					limit: options.limit,
					total,
					totalPages: Math.ceil(total / options.limit),
					hasNext: options.page < Math.ceil(total / options.limit),
					hasPrev: options.page > 1,
				},
			},
		});
	} catch (error) {
		console.error('Get Public Values Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to retrieve public values',
		});
	}
};

/**
 * Get a specific value by ID
 */
exports.getValueById = async (req, res) => {
	try {
		const { id } = req.params;
		const userId = req.user._id;

		const value = await Value.findOne({
			_id: id,
			$or: [{ userId }, { isPublic: true }],
		}).populate('userId', 'name');

		if (!value) {
			return res.status(404).json({
				success: false,
				message: 'Value not found',
			});
		}

		res.status(200).json({
			success: true,
			message: 'Value retrieved successfully',
			data: {
				value,
			},
		});
	} catch (error) {
		console.error('Get Value By ID Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to retrieve value',
		});
	}
};

/**
 * Update a value
 */
exports.updateValue = async (req, res) => {
	try {
		const errors = validationResult(req);
		if (!errors.isEmpty()) {
			return res.status(400).json({
				success: false,
				message: 'Validation failed',
				errors: errors.array(),
			});
		}

		const { id } = req.params;
		const userId = req.user._id;
		const { name, definition, isPublic } = req.body;

		// Find the value and ensure user owns it
		const value = await Value.findOne({ _id: id, userId });
		if (!value) {
			return res.status(404).json({
				success: false,
				message: 'Value not found or access denied',
			});
		}

		// Check if another value with the same name exists (excluding current one)
		if (name && name !== value.name) {
			const existingValue = await Value.findOne({
				userId,
				name: { $regex: new RegExp(`^${name}$`, 'i') },
				_id: { $ne: id },
			});

			if (existingValue) {
				return res.status(400).json({
					success: false,
					message: 'A value with this name already exists',
				});
			}
		}

		// Update fields
		if (name) value.name = name.trim();
		if (definition) value.definition = definition.trim();
		if (typeof isPublic === 'boolean') value.isPublic = isPublic;

		await value.save();

		res.status(200).json({
			success: true,
			message: 'Value updated successfully',
			data: {
				value,
			},
		});
	} catch (error) {
		console.error('Update Value Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to update value',
		});
	}
};

/**
 * Delete a value
 */
exports.deleteValue = async (req, res) => {
	try {
		const { id } = req.params;
		const userId = req.user._id;

		const value = await Value.findOneAndDelete({ _id: id, userId });
		if (!value) {
			return res.status(404).json({
				success: false,
				message: 'Value not found or access denied',
			});
		}

		res.status(200).json({
			success: true,
			message: 'Value deleted successfully',
			data: {
				value,
			},
		});
	} catch (error) {
		console.error('Delete Value Error:', error);
		res.status(500).json({
			success: false,
			message: 'Failed to delete value',
		});
	}
};
