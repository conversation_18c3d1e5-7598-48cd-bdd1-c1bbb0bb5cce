const Lesson = require('../models/Lesson');
const cloudinary = require('../config/cloudinary');
const fs = require('fs');
const streamifier = require('streamifier');
const { Readable } = require('stream');
const Subscription = require('../models/NotificationSubs');
const webpush = require('web-push');

// Initialize VAPID keys for notifications
const vapidKeys = {
	publicKey: process.env.PUBLIC_VAPID_KEY,
	privateKey: process.env.PRIVATE_VAPID_KEY,
};

if (vapidKeys.publicKey && vapidKeys.privateKey) {
	webpush.setVapidDetails(
		'mailto:<EMAIL>',
		vapidKeys.publicKey,
		vapidKeys.privateKey,
	);
}

// Helper function to fix FCM endpoints if needed
const fixFcmEndpoint = (endpoint) => {
	if (endpoint.includes('fcm.googleapis.com/fcm/send/')) {
		const token = endpoint.split('/').pop();
		return `https://fcm.googleapis.com/wp/${token}`;
	}
	return endpoint;
};

// Import the broadcast notification function
const { broadcastNotification } = require('./notificationController');

// Helper function to send notifications for new lessons using broadcast
const sendNewLessonNotifications = async (lesson) => {
	try {
		// Prepare notification data
		const notificationData = {
			title: '🎓 New Course Available!',
			messageContent: `"${lesson.title}" by ${lesson.instructor} is now available. Start learning today!`
		};

		// Create a mock request object for the broadcast function
		const mockReq = {
			body: notificationData
		};

		// Create a mock response object to capture the result
		let broadcastResult = null;
		const mockRes = {
			status: (code) => ({
				json: (data) => {
					broadcastResult = { status: code, data };
					return mockRes;
				}
			})
		};

		// Call the broadcast notification function
		await broadcastNotification(mockReq, mockRes);

		if (broadcastResult && broadcastResult.status === 200) {
			console.log('New lesson broadcast notification sent successfully:', broadcastResult.data);
			return broadcastResult.data;
		} else {
			console.error('Failed to send broadcast notification:', broadcastResult);
			throw new Error('Broadcast notification failed');
		}
	} catch (error) {
		console.error('Error sending new lesson notifications:', error);
		throw error;
	}
};

// Utility: Check if user is superadmin or tutor
const isAuthorized = (user) => {
	return user.roles.includes('superadmin') || user.roles.includes('tutor');
};

// Enhanced upload function for large files with retry mechanism
const uploadToCloudinary = (buffer, options, retries = 3) => {
	return new Promise((resolve, reject) => {
		const attemptUpload = (attempt) => {
			const stream = cloudinary.uploader.upload_stream(
				{
					...options,
					// Optimize for large files
					chunk_size: 20000000, // 20MB chunks for better reliability
					timeout: 1800000, // 30 minutes timeout
					retry_delay: 5000,
					max_results: 1,
					// Enable auto backup for failed uploads
					backup: true,
					// Use large file upload mode
					use_filename: true,
					unique_filename: false,
				},
				(error, result) => {
					if (error) {
						console.error(`Upload attempt ${attempt} failed:`, error.message);

						// Retry on specific errors
						if (
							attempt < retries &&
							(error.code === 'ETIMEDOUT' ||
								error.message.includes('timeout') ||
								error.message.includes('network') ||
								error.http_code >= 500)
						) {
							console.log(`Retrying upload... Attempt ${attempt + 1}`);
							setTimeout(() => attemptUpload(attempt + 1), 5000);
							return;
						}

						return reject(error);
					}
					resolve(result);
				},
			);

			// Create readable stream from buffer
			const readableStream = new Readable({
				read() {
					this.push(buffer);
					this.push(null);
				},
			});

			readableStream.pipe(stream);
		};

		attemptUpload(1);
	});
};

// Upload lesson video and create lesson
exports.uploadLesson = async (req, res) => {
	try {
		if (!isAuthorized(req.user)) {
			return res.status(403).json({ message: 'Unauthorized' });
		}

	const { title, description, category, instructor, videoUrl, thumbnail, reflectionQuestions } = req.body;

	if (!title || !description || !category || !instructor) {
		return res.status(400).json({
			message:
				'All fields are required: title, description, category, instructor',
		});
	}

	if (!videoUrl || !thumbnail) {
		return res.status(400).json({
			message: 'Both video URL and thumbnail URL are required.',
		});
	}

		// Validate URLs (basic validation)
		const urlPattern = /^https?:\/\/.+/;
		if (!urlPattern.test(videoUrl)) {
			return res.status(400).json({
				message: 'Invalid video URL format.',
			});
		}

		if (!urlPattern.test(thumbnail)) {
			return res.status(400).json({
				message: 'Invalid thumbnail URL format.',
			});
		}

		try {
			// Create lesson record directly with provided URLs
			const lesson = await Lesson.create({
				title,
				description,
				category,
				instructor,
				thumbnail,
				videoUrl,
				reflectionQuestions: reflectionQuestions || [],
			});

			// Send notifications to all subscribed users
			try {
				await sendNewLessonNotifications(lesson);
			} catch (notifError) {
				console.error('Error sending notifications:', notifError);
				// Don't fail the lesson upload if notifications fail
			}

			// Send success response
			res.status(201).json({
				status: 'success',
				message: 'Lesson created successfully',
				data: lesson,
			});
		} catch (error) {
			console.error('Lesson creation error:', error);
			res.status(500).json({
				status: 'error',
				message: 'Failed to create lesson',
				error: process.env.NODE_ENV === 'development' ? error.message : undefined,
			});
		}
	} catch (error) {
		console.error('Upload error:', error);

		// Handle different types of errors
		const errorResponse = {
			status: 'error',
			message: 'Lesson upload failed',
		};

		if (error.code === 'ETIMEDOUT' || error.message.includes('timeout')) {
			errorResponse.message =
				'Upload timeout. Large files may take longer. Please try again.';
			errorResponse.code = 408;
		} else if (error.http_code >= 400) {
			errorResponse.message = error.message || 'Upload service error';
			errorResponse.code = error.http_code;
		} else {
			errorResponse.code = 500;
			if (process.env.NODE_ENV === 'development') {
				errorResponse.error = error.message;
			}
		}

		// If response hasn't been sent yet
		if (!res.headersSent) {
			return res.status(errorResponse.code || 500).json(errorResponse);
		} else {
			// If streaming response, end with error
			res.end(JSON.stringify(errorResponse));
		}
	}
};

// Alternative chunked upload endpoint for very large files
exports.uploadLessonChunked = async (req, res) => {
	try {
		if (!isAuthorized(req.user)) {
			return res.status(403).json({ message: 'Unauthorized' });
		}

		const {
			chunkIndex,
			totalChunks,
			fileName,
			title,
			description,
			category,
			instructor,
		} = req.body;

		const chunk = req.files?.chunk?.[0];

		if (!chunk) {
			return res.status(400).json({ message: 'Chunk data required' });
		}

		// Store chunk temporarily (you might want to use Redis or temporary storage)
		const chunkDir = `./temp/uploads/${fileName}`;
		if (!fs.existsSync(chunkDir)) {
			fs.mkdirSync(chunkDir, { recursive: true });
		}

		const chunkPath = `${chunkDir}/chunk_${chunkIndex}`;
		fs.writeFileSync(chunkPath, chunk.buffer);

		// If this is the last chunk, combine all chunks
		if (parseInt(chunkIndex) === parseInt(totalChunks) - 1) {
			const finalFilePath = `./temp/uploads/${fileName}_complete`;
			const writeStream = fs.createWriteStream(finalFilePath);

			// Combine all chunks
			for (let i = 0; i < totalChunks; i++) {
				const chunkData = fs.readFileSync(`${chunkDir}/chunk_${i}`);
				writeStream.write(chunkData);
			}
			writeStream.end();

			// Clean up chunk files
			fs.rmSync(chunkDir, { recursive: true });

			// Upload the complete file
			const completeFileBuffer = fs.readFileSync(finalFilePath);

			const videoUpload = await uploadToCloudinary(
				completeFileBuffer,
				{
					resource_type: 'video',
					folder: 'lesson-videos',
					chunk_size: 20000000,
					timeout: 1800000,
					quality: 'auto:good',
					format: 'mp4',
					flags: 'progressive',
				},
				5,
			);

			// Clean up complete file
			fs.unlinkSync(finalFilePath);

			const lesson = await Lesson.create({
				title,
				description,
				category,
				instructor,
				videoUrl: videoUpload.secure_url,
			});

			return res.json({
				message: 'Lesson uploaded successfully',
				lesson,
			});
		}

		// Return success for chunk upload
		res.json({
			message: `Chunk ${
				parseInt(chunkIndex) + 1
			}/${totalChunks} uploaded successfully`,
			chunkIndex: parseInt(chunkIndex),
		});
	} catch (error) {
		console.error('Chunked upload error:', error);
		res.status(500).json({
			message: 'Chunked upload failed',
			error:
				process.env.NODE_ENV === 'development'
					? error.message
					: 'Internal server error',
		});
	}
};

// Fetch lessons with pagination and optional category filter
exports.getLessons = async (req, res) => {
	try {
		const { page = 1, limit = 10, category, title } = req.query;
		const query = {};

		if (category) {
			query.category = category;
		}

		if (title) {
			query.title = { $regex: title, $options: 'i' };
		}

		const lessons = await Lesson.find(query)
			.skip((page - 1) * limit)
			.limit(parseInt(limit))
			.sort({ createdAt: -1 });

		const total = await Lesson.countDocuments(query);

		res.json({
			data: lessons,
			pagination: {
				total,
				page: Number(page),
				limit: Number(limit),
				totalPages: Math.ceil(total / limit),
			},
		});
	} catch (error) {
		res.status(500).json({
			message: 'Error fetching lessons',
			error:
				process.env.NODE_ENV === 'development'
					? error.message
					: 'Internal server error',
		});
	}
};

// Get single lesson by ID
exports.getLessonById = async (req, res) => {
	try {
		const lesson = await Lesson.findById(req.params.id);
		if (!lesson) {
			return res.status(404).json({ message: 'Lesson not found' });
		}
		res.json({ data: lesson });
	} catch (error) {
		res.status(500).json({
			message: 'Error fetching lesson',
			error:
				process.env.NODE_ENV === 'development'
					? error.message
					: 'Internal server error',
		});
	}
};

// Update lesson details
exports.updateLesson = async (req, res) => {
	try {
		if (!isAuthorized(req.user)) {
			return res.status(403).json({ message: 'Unauthorized' });
		}

	const { title, description, category, instructor, videoUrl, thumbnail, reflectionQuestions } = req.body;

	const lesson = await Lesson.findById(req.params.id);
	if (!lesson) {
		return res.status(404).json({ message: 'Lesson not found' });
	}

	// Update text fields
	if (title) lesson.title = title;
	if (description) lesson.description = description;
	if (category) lesson.category = category;
	if (instructor) lesson.instructor = instructor;
	if (reflectionQuestions !== undefined) lesson.reflectionQuestions = reflectionQuestions;

		// Update URLs if provided
		if (thumbnail) {
			// Validate URL format
			const urlPattern = /^https?:\/\/.+/;
			if (!urlPattern.test(thumbnail)) {
				return res.status(400).json({
					message: 'Invalid thumbnail URL format.',
				});
			}
			lesson.thumbnail = thumbnail;
		}

		// Update video URL if provided (optional for updates)
		if (videoUrl) {
			// Validate URL format
			const urlPattern = /^https?:\/\/.+/;
			if (!urlPattern.test(videoUrl)) {
				return res.status(400).json({
					message: 'Invalid video URL format.',
				});
			}
			lesson.videoUrl = videoUrl;
		}

		await lesson.save();

		res.json({
			message: 'Lesson updated successfully',
			data: lesson,
		});
	} catch (error) {
		console.error('Update error:', error);

		if (error.code === 'ETIMEDOUT' || error.message.includes('timeout')) {
			return res.status(408).json({
				message:
					'Upload timeout. Large files may take longer. Please try again.',
			});
		}

		if (error.http_code >= 400) {
			return res.status(error.http_code).json({
				message: error.message || 'Upload failed',
			});
		}

		res.status(500).json({
			message: 'Error updating lesson',
			error:
				process.env.NODE_ENV === 'development'
					? error.message
					: 'Internal server error',
		});
	}
};

// Delete lesson
exports.deleteLesson = async (req, res) => {
	try {
		if (!isAuthorized(req.user)) {
			return res.status(403).json({ message: 'Unauthorized' });
		}

		const lesson = await Lesson.findByIdAndDelete(req.params.id);
		if (!lesson) {
			return res.status(404).json({ message: 'Lesson not found' });
		}

		res.json({ message: 'Lesson deleted successfully' });
	} catch (error) {
		res.status(500).json({
			message: 'Error deleting lesson',
			error:
				process.env.NODE_ENV === 'development'
					? error.message
					: 'Internal server error',
		});
	}
};
