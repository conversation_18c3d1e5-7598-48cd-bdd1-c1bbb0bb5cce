const redisService = require('./redisService');
const { getSocketIO } = require('../config/socket');

class SocketHandlers {
    constructor() {
        this.connectedUsers = new Map(); // In-memory store for quick access
    }

    // Initialize socket handlers
    initializeHandlers(io) {
        io.on('connection', (socket) => {
            console.log(`👤 New connection: ${socket.id}`);
            
            // Bind all handlers to this socket
            this.bindAuthenticationHandlers(socket);
            this.bindChatHandlers(socket);
            this.bindPresenceHandlers(socket);
            this.bindNotificationHandlers(socket);
            this.bindCollaborationHandlers(socket);
            this.bindDisconnectionHandlers(socket);
        });
    }

    // Authentication and user management
    bindAuthenticationHandlers(socket) {
        // User authentication
        socket.on('authenticate', async (data) => {
            try {
                const { userId, token } = data;
                
                // TODO: Verify JWT token here
                // For now, we'll simulate authentication
                if (userId && token) {
                    socket.userId = userId;
                    socket.authenticated = true;
                    
                    // Store user connection in Redis
                    await redisService.hset('connected_users', userId, {
                        socketId: socket.id,
                        connectedAt: new Date().toISOString(),
                        lastActivity: new Date().toISOString()
                    });
                    
                    // Store in memory for quick access
                    this.connectedUsers.set(userId, socket.id);
                    
                    socket.emit('authenticated', { 
                        success: true, 
                        userId,
                        message: 'Successfully authenticated' 
                    });
                    
                    console.log(`✅ User ${userId} authenticated with socket ${socket.id}`);
                } else {
                    socket.emit('authentication_error', { 
                        message: 'Invalid credentials' 
                    });
                }
            } catch (error) {
                console.error('Authentication error:', error);
                socket.emit('authentication_error', { 
                    message: 'Authentication failed' 
                });
            }
        });

        // Update user activity
        socket.on('activity', async () => {
            if (socket.userId) {
                await redisService.hset('connected_users', socket.userId, {
                    socketId: socket.id,
                    lastActivity: new Date().toISOString()
                });
            }
        });
    }

    // Chat and messaging handlers
    bindChatHandlers(socket) {
        // Join chat room
        socket.on('join_chat_room', async (data) => {
            try {
                const { roomId, roomType = 'group' } = data;
                
                if (!socket.authenticated) {
                    return socket.emit('error', { message: 'Authentication required' });
                }
                
                socket.join(roomId);
                
                // Add user to room in Redis
                await redisService.sadd(`room:${roomId}:members`, socket.userId);
                
                // Notify others in the room
                socket.to(roomId).emit('user_joined_room', {
                    userId: socket.userId,
                    roomId,
                    timestamp: new Date().toISOString()
                });
                
                // Send confirmation to user
                socket.emit('joined_room', { roomId, roomType });
                
                console.log(`👤 User ${socket.userId} joined room ${roomId}`);
            } catch (error) {
                console.error('Join room error:', error);
                socket.emit('error', { message: 'Failed to join room' });
            }
        });

        // Leave chat room
        socket.on('leave_chat_room', async (data) => {
            try {
                const { roomId } = data;
                
                socket.leave(roomId);
                
                // Remove user from room in Redis
                await redisService.srem(`room:${roomId}:members`, socket.userId);
                
                // Notify others in the room
                socket.to(roomId).emit('user_left_room', {
                    userId: socket.userId,
                    roomId,
                    timestamp: new Date().toISOString()
                });
                
                socket.emit('left_room', { roomId });
                
                console.log(`👤 User ${socket.userId} left room ${roomId}`);
            } catch (error) {
                console.error('Leave room error:', error);
                socket.emit('error', { message: 'Failed to leave room' });
            }
        });

        // Send message to room
        socket.on('send_message', async (data) => {
            try {
                const { roomId, message, messageType = 'text', replyTo = null } = data;
                
                if (!socket.authenticated) {
                    return socket.emit('error', { message: 'Authentication required' });
                }
                
                const messageData = {
                    id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                    senderId: socket.userId,
                    roomId,
                    message,
                    messageType,
                    replyTo,
                    timestamp: new Date().toISOString()
                };
                
                // Store message in Redis (optional - you might want to use MongoDB instead)
                await redisService.lpush(`room:${roomId}:messages`, messageData);
                
                // Emit to all users in the room except sender
                socket.to(roomId).emit('new_message', messageData);
                
                // Confirm message sent to sender
                socket.emit('message_sent', { messageId: messageData.id });
                
                console.log(`📩 Message sent in room ${roomId} by user ${socket.userId}`);
            } catch (error) {
                console.error('Send message error:', error);
                socket.emit('error', { message: 'Failed to send message' });
            }
        });

        // Typing indicators
        socket.on('typing_start', (data) => {
            const { roomId } = data;
            socket.to(roomId).emit('user_typing', {
                userId: socket.userId,
                roomId,
                typing: true
            });
        });

        socket.on('typing_stop', (data) => {
            const { roomId } = data;
            socket.to(roomId).emit('user_typing', {
                userId: socket.userId,
                roomId,
                typing: false
            });
        });
    }

    // Presence and status handlers
    bindPresenceHandlers(socket) {
        // Update user status
        socket.on('update_status', async (data) => {
            try {
                const { status, customMessage = '' } = data;
                
                if (!socket.authenticated) {
                    return socket.emit('error', { message: 'Authentication required' });
                }
                
                const statusData = {
                    status, // online, away, busy, invisible
                    customMessage,
                    lastUpdated: new Date().toISOString()
                };
                
                // Update status in Redis
                await redisService.hset(`user:${socket.userId}:status`, 'current', statusData);
                
                // Notify relevant users (friends, team members, etc.)
                socket.broadcast.emit('user_status_update', {
                    userId: socket.userId,
                    ...statusData
                });
                
                console.log(`👤 User ${socket.userId} status updated to ${status}`);
            } catch (error) {
                console.error('Status update error:', error);
                socket.emit('error', { message: 'Failed to update status' });
            }
        });

        // Get online users
        socket.on('get_online_users', async () => {
            try {
                const connectedUsers = await redisService.hgetall('connected_users');
                const onlineUsers = Object.keys(connectedUsers);
                
                socket.emit('online_users', { users: onlineUsers });
            } catch (error) {
                console.error('Get online users error:', error);
                socket.emit('error', { message: 'Failed to get online users' });
            }
        });
    }

    // Notification handlers
    bindNotificationHandlers(socket) {
        // Send real-time notification
        socket.on('send_notification', async (data) => {
            try {
                const { targetUserId, type, title, message, data: notificationData } = data;
                
                if (!socket.authenticated) {
                    return socket.emit('error', { message: 'Authentication required' });
                }
                
                const notification = {
                    id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                    fromUserId: socket.userId,
                    toUserId: targetUserId,
                    type,
                    title,
                    message,
                    data: notificationData,
                    timestamp: new Date().toISOString(),
                    read: false
                };
                
                // Store notification in Redis
                await redisService.lpush(`user:${targetUserId}:notifications`, notification);
                
                // Send real-time notification if user is online
                const targetSocketId = this.connectedUsers.get(targetUserId);
                if (targetSocketId) {
                    socket.to(targetSocketId).emit('new_notification', notification);
                }
                
                console.log(`🔔 Notification sent from ${socket.userId} to ${targetUserId}`);
            } catch (error) {
                console.error('Send notification error:', error);
                socket.emit('error', { message: 'Failed to send notification' });
            }
        });

        // Mark notification as read
        socket.on('mark_notification_read', async (data) => {
            try {
                const { notificationId } = data;
                
                // Update notification status in Redis
                // This is a simplified implementation - you might want to use a more sophisticated approach
                await redisService.hset(`notification:${notificationId}`, 'read', true);
                
                socket.emit('notification_marked_read', { notificationId });
            } catch (error) {
                console.error('Mark notification read error:', error);
                socket.emit('error', { message: 'Failed to mark notification as read' });
            }
        });
    }

    // Collaboration handlers (for real-time editing, etc.)
    bindCollaborationHandlers(socket) {
        // Join collaboration session
        socket.on('join_collaboration', async (data) => {
            try {
                const { sessionId, documentId } = data;
                
                if (!socket.authenticated) {
                    return socket.emit('error', { message: 'Authentication required' });
                }
                
                socket.join(`collab:${sessionId}`);
                
                // Add user to collaboration session
                await redisService.sadd(`collab:${sessionId}:participants`, socket.userId);
                
                // Notify other participants
                socket.to(`collab:${sessionId}`).emit('user_joined_collaboration', {
                    userId: socket.userId,
                    sessionId,
                    documentId
                });
                
                console.log(`👥 User ${socket.userId} joined collaboration session ${sessionId}`);
            } catch (error) {
                console.error('Join collaboration error:', error);
                socket.emit('error', { message: 'Failed to join collaboration' });
            }
        });

        // Broadcast document changes
        socket.on('document_change', (data) => {
            const { sessionId, changes, version } = data;
            
            socket.to(`collab:${sessionId}`).emit('document_updated', {
                userId: socket.userId,
                changes,
                version,
                timestamp: new Date().toISOString()
            });
        });

        // Cursor position updates
        socket.on('cursor_update', (data) => {
            const { sessionId, position, selection } = data;
            
            socket.to(`collab:${sessionId}`).emit('cursor_moved', {
                userId: socket.userId,
                position,
                selection
            });
        });
    }

    // Disconnection handlers
    bindDisconnectionHandlers(socket) {
        socket.on('disconnect', async (reason) => {
            try {
                console.log(`👤 User disconnected: ${socket.id}, reason: ${reason}`);
                
                if (socket.userId) {
                    // Remove from connected users
                    await redisService.hdel('connected_users', socket.userId);
                    this.connectedUsers.delete(socket.userId);
                    
                    // Update user status to offline
                    await redisService.hset(`user:${socket.userId}:status`, 'current', {
                        status: 'offline',
                        lastSeen: new Date().toISOString()
                    });
                    
                    // Notify relevant users about disconnection
                    socket.broadcast.emit('user_status_update', {
                        userId: socket.userId,
                        status: 'offline',
                        lastSeen: new Date().toISOString()
                    });
                    
                    console.log(`👤 User ${socket.userId} disconnected`);
                }
            } catch (error) {
                console.error('Disconnection cleanup error:', error);
            }
        });
    }

    // Utility methods for external use
    async sendNotificationToUser(userId, notification) {
        try {
            const socketId = this.connectedUsers.get(userId);
            if (socketId) {
                const io = getSocketIO();
                io.to(socketId).emit('new_notification', notification);
                return true;
            }
            return false;
        } catch (error) {
            console.error('Send notification to user error:', error);
            return false;
        }
    }

    async broadcastToRoom(roomId, event, data) {
        try {
            const io = getSocketIO();
            io.to(roomId).emit(event, data);
            return true;
        } catch (error) {
            console.error('Broadcast to room error:', error);
            return false;
        }
    }

    async getUserOnlineStatus(userId) {
        try {
            return this.connectedUsers.has(userId);
        } catch (error) {
            console.error('Get user online status error:', error);
            return false;
        }
    }
}

// Create and export singleton instance
const socketHandlers = new SocketHandlers();

module.exports = socketHandlers;
