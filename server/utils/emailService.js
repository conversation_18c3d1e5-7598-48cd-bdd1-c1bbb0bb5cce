const nodemailer = require('nodemailer');
const fs = require('fs');
const path = require('path');

class EmailService {
	constructor() {
		this.transporter = this.createTransporter();
	}

	createTransporter() {
		const transporter = nodemailer.createTransport({
			service: process.env.EMAIL_SERVICE || 'gmail',
			host: process.env.EMAIL_HOST || 'smtp.gmail.com',
			port: process.env.EMAIL_PORT || 587,
			secure: false, // true for 465, false for other ports
			auth: {
				user: process.env.EMAIL_USER,
				pass: process.env.EMAIL_PASSWORD, // Use app password for Gmail
			},
			tls: {
				rejectUnauthorized: false,
			},
		});

		// Verify connection configuration
		transporter.verify((error, success) => {
			if (error) {
				console.error('❌ Email service configuration error:', error);
			} else {
				console.log('✅ Email service is ready to send messages');
			}
		});

		return transporter;
	}

	async sendOTPEmail(email, name, otp) {
		try {
			const mailOptions = {
				from: {
					name: process.env.EMAIL_FROM_NAME || 'YouLife',
					address: process.env.EMAIL_FROM || process.env.EMAIL_USER,
				},
				to: email,
				subject: 'Verify Your Email - YouLife',
				html: this.getOTPEmailTemplate(name, otp),
				text: `Hello ${name},\n\nYour verification code is: ${otp}\n\nThis code will expire in 10 minutes.\n\nIf you didn't request this, please ignore this email.\n\nBest regards,\nYouLife Team`,
			};

			const result = await this.transporter.sendMail(mailOptions);
			console.log('📧 OTP email sent successfully:', result.messageId);
			return { success: true, messageId: result.messageId };
		} catch (error) {
			console.error('❌ Error sending OTP email:', error);
			throw new Error('Failed to send verification email');
		}
	}

	async sendWelcomeEmail(email, name) {
		try {
			const mailOptions = {
				from: {
					name: process.env.EMAIL_FROM_NAME || 'YouLife',
					address: process.env.EMAIL_FROM || process.env.EMAIL_USER,
				},
				to: email,
				subject: 'Welcome to YouLife!',
				html: this.getWelcomeEmailTemplate(name),
				text: `Hello ${name},\n\nWelcome to YouLife! Your account has been successfully verified.\n\nYou can now access all features of our platform.\n\nBest regards,\nYouLife Team`,
			};

			const result = await this.transporter.sendMail(mailOptions);
			console.log('📧 Welcome email sent successfully:', result.messageId);
			return { success: true, messageId: result.messageId };
		} catch (error) {
			console.error('❌ Error sending welcome email:', error);
			throw new Error('Failed to send welcome email');
		}
	}

	// Legacy method - keeping for backward compatibility
	async sendPasswordResetEmail(email, name, resetToken, resetUrl) {
		try {
			const mailOptions = {
				from: {
					name: process.env.EMAIL_FROM_NAME || 'YouLife',
					address: process.env.EMAIL_FROM || process.env.EMAIL_USER,
				},
				to: email,
				subject: 'Reset Your Password - YouLife',
				html: this.getPasswordResetEmailTemplate(name, resetUrl),
				text: `Hello ${name},\n\nYou requested a password reset for your YouLife account.\n\nClick the link below to reset your password:\n${resetUrl}\n\nThis link will expire in 30 minutes.\n\nIf you didn't request this, please ignore this email.\n\nBest regards,\nYouLife Team`,
			};

			const result = await this.transporter.sendMail(mailOptions);
			console.log(
				'📧 Password reset email sent successfully:',
				result.messageId,
			);
			return { success: true, messageId: result.messageId };
		} catch (error) {
			console.error('❌ Error sending password reset email:', error);
			throw new Error('Failed to send password reset email');
		}
	}

	// NEW: Send OTP for password reset
	async sendPasswordResetOTPEmail(email, name, otp) {
		try {
			const mailOptions = {
				from: {
					name: process.env.EMAIL_FROM_NAME || 'YouLife',
					address: process.env.EMAIL_FROM || process.env.EMAIL_USER,
				},
				to: email,
				subject: 'Password Reset Code - YouLife',
				html: this.getPasswordResetOTPEmailTemplate(name, otp),
				text: `Hello ${name},\n\nYour password reset code is: ${otp}\n\nThis code will expire in 10 minutes.\n\nIf you didn't request this, please ignore this email.\n\nBest regards,\nYouLife Team`,
			};

			const result = await this.transporter.sendMail(mailOptions);
			console.log(
				'📧 Password reset OTP email sent successfully:',
				result.messageId,
			);
			return { success: true, messageId: result.messageId };
		} catch (error) {
			console.error('❌ Error sending password reset OTP email:', error);
			throw new Error('Failed to send password reset OTP email');
		}
	}

	// Updated method name for consistency
	async sendPasswordChangeConfirmationEmail(email, name) {
		try {
			const mailOptions = {
				from: {
					name: process.env.EMAIL_FROM_NAME || 'YouLife',
					address: process.env.EMAIL_FROM || process.env.EMAIL_USER,
				},
				to: email,
				subject: 'Password Changed - YouLife',
				html: this.getPasswordChangeConfirmationTemplate(name),
				text: `Hello ${name},\n\nYour password has been successfully changed.\n\nIf you didn't make this change, please contact our support team immediately.\n\nBest regards,\nYouLife Team`,
			};

			const result = await this.transporter.sendMail(mailOptions);
			console.log(
				'📧 Password change confirmation email sent successfully:',
				result.messageId,
			);
			return { success: true, messageId: result.messageId };
		} catch (error) {
			console.error(
				'❌ Error sending password change confirmation email:',
				error,
			);
			throw new Error('Failed to send password change confirmation email');
		}
	}

	// Legacy method alias for backward compatibility
	async sendPasswordChangeConfirmation(email, name) {
		return this.sendPasswordChangeConfirmationEmail(email, name);
	}

	getOTPEmailTemplate(name, otp) {
		return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Verify Your Email</title>
            <style>
                body { 
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                    line-height: 1.6; 
                    color: #333; 
                    margin: 0;
                    padding: 0;
                    background-color: #f8fffe;
                }
                .container { 
                    max-width: 600px; 
                    margin: 0 auto; 
                    padding: 20px; 
                }
                .header { 
                    background: linear-gradient(135deg, #14b8a6 0%, #0f766e 100%); 
                    color: white; 
                    padding: 40px 30px; 
                    text-align: center; 
                    border-radius: 12px 12px 0 0;
                    box-shadow: 0 4px 12px rgba(20, 184, 166, 0.2);
                }
                .logo {
                    margin-bottom: 20px;
                }
                .logo img {
                    height: 60px;
                    width: auto;
                }
                .header h1 {
                    margin: 0;
                    font-size: 28px;
                    font-weight: 600;
                }
                .header p {
                    margin: 10px 0 0 0;
                    font-size: 16px;
                    opacity: 0.9;
                }
                .content { 
                    background: white; 
                    padding: 40px 30px; 
                    border-radius: 0 0 12px 12px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                }
                .otp-box { 
                    background: linear-gradient(135deg, #f0fdfc 0%, #ccfbf1 100%); 
                    border: 2px solid #14b8a6; 
                    border-radius: 12px; 
                    padding: 30px 20px; 
                    text-align: center; 
                    margin: 30px 0;
                    box-shadow: 0 2px 8px rgba(20, 184, 166, 0.1);
                }
                .otp-code { 
                    font-size: 36px; 
                    font-weight: 700; 
                    color: #0f766e; 
                    letter-spacing: 8px; 
                    margin: 15px 0;
                    font-family: 'Courier New', monospace;
                }
                .footer { 
                    text-align: center; 
                    margin-top: 30px; 
                    color: #6b7280; 
                    font-size: 14px; 
                    padding: 20px;
                }
                .divider {
                    height: 1px;
                    background: linear-gradient(to right, transparent, #14b8a6, transparent);
                    margin: 30px 0;
                }
                .highlight {
                    color: #0f766e;
                    font-weight: 600;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="logo">
                        <img src="https://res.cloudinary.com/dsaqhw9qj/image/upload/v1751413629/logo_jxduxv.png" alt="YouLife Logo">
                    </div>
                    <h1>Verify Your Email Address</h1>
                    <p>Welcome to YouLife</p>
                </div>
                <div class="content">
                    <h2>Hello <span class="highlight">${name}</span>!</h2>
                    <p>Thank you for joining <strong>YouLife</strong>. To complete your registration and unlock all our amazing features, please verify your email address using the verification code below:</p>
                    
                    <div class="otp-box">
                        <p style="margin: 0; font-size: 16px; color: #0f766e; font-weight: 600;">Your verification code is:</p>
                        <div class="otp-code">${otp}</div>
                        <p style="margin: 0; font-size: 14px; color: #6b7280;"><small>⏰ This code expires in 10 minutes</small></p>
                    </div>
                    
                    <div class="divider"></div>
                    
                    <p>Simply enter this code on the verification page to activate your account and start your journey with YouLife.</p>
                    <p style="color: #6b7280; font-size: 14px;">If you didn't create an account with YouLife, please ignore this email and no further action is required.</p>
                </div>
                <div class="footer">
                    <p>© 2025 <strong>YouLife</strong>. All rights reserved.</p>
                    <p>This is an automated message, please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>
        `;
	}

	getWelcomeEmailTemplate(name) {
		return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Welcome to YouLife</title>
            <style>
                body { 
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                    line-height: 1.6; 
                    color: #333; 
                    margin: 0;
                    padding: 0;
                    background-color: #f8fffe;
                }
                .container { 
                    max-width: 600px; 
                    margin: 0 auto; 
                    padding: 20px; 
                }
                .header { 
                    background: linear-gradient(135deg, #14b8a6 0%, #0f766e 100%); 
                    color: white; 
                    padding: 40px 30px; 
                    text-align: center; 
                    border-radius: 12px 12px 0 0;
                    box-shadow: 0 4px 12px rgba(20, 184, 166, 0.2);
                }
                .logo {
                    margin-bottom: 20px;
                }
                .logo img {
                    height: 60px;
                    width: auto;
                }
                .header h1 {
                    margin: 0;
                    font-size: 28px;
                    font-weight: 600;
                }
                .header p {
                    margin: 10px 0 0 0;
                    font-size: 16px;
                    opacity: 0.9;
                }
                .content { 
                    background: white; 
                    padding: 40px 30px; 
                    border-radius: 0 0 12px 12px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                }
                .footer { 
                    text-align: center; 
                    margin-top: 30px; 
                    color: #6b7280; 
                    font-size: 14px; 
                    padding: 20px;
                }
                .button { 
                    background: linear-gradient(135deg, #14b8a6 0%, #0f766e 100%); 
                    color: white; 
                    padding: 15px 35px; 
                    text-decoration: none; 
                    border-radius: 8px; 
                    display: inline-block; 
                    margin: 25px 0;
                    font-weight: 600;
                    box-shadow: 0 4px 12px rgba(20, 184, 166, 0.3);
                    transition: all 0.3s ease;
                }
                .features {
                    background: #f0fdfc;
                    border-radius: 12px;
                    padding: 25px;
                    margin: 25px 0;
                    border-left: 4px solid #14b8a6;
                }
                .features ul {
                    margin: 15px 0;
                    padding-left: 0;
                }
                .features li {
                    list-style: none;
                    padding: 8px 0;
                    padding-left: 25px;
                    position: relative;
                }
                .features li:before {
                    content: "✨";
                    position: absolute;
                    left: 0;
                }
                .highlight {
                    color: #0f766e;
                    font-weight: 600;
                }
                .divider {
                    height: 1px;
                    background: linear-gradient(to right, transparent, #14b8a6, transparent);
                    margin: 30px 0;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="logo">
                        <img src="https://res.cloudinary.com/dsaqhw9qj/image/upload/v1751413629/logo_jxduxv.png" alt="YouLife Logo">
                    </div>
                    <h1>🎉 Welcome to YouLife!</h1>
                    <p>Your journey starts here</p>
                </div>
                <div class="content">
                    <h2>Hello <span class="highlight">${name}</span>!</h2>
                    <p>🎊 <strong>Congratulations!</strong> Your email has been successfully verified and your YouLife account is now fully active.</p>
                    
                    <div class="features">
                        <h3 style="color: #0f766e; margin-top: 0;">🚀 What's waiting for you:</h3>
                        <ul>
                            <li><strong>Access to learning resources</strong> - Comprehensive materials tailored for you</li>
                            <li><strong>Real-time chat and collaboration</strong> - Connect instantly with your community</li>
                            <li><strong>Connect with tutors and peers</strong> - Build meaningful learning relationships</li>
                            <li><strong>Track your progress</strong> - Monitor your growth every step of the way</li>
                            <li><strong>Set and achieve your goals</strong> - Turn your dreams into reality</li>
                        </ul>
                    </div>
                    
                    <div class="divider"></div>
                    
                    <p>Ready to dive in? Click the button below to explore your personalized dashboard:</p>
                    <div style="text-align: center;">
                        <a href="${process.env.CLIENT_URL}" class="button">YouLife</a>
                    </div>
                    <p style="color: #6b7280; font-size: 14px;">If you have any questions or need help getting started, our support team is here to help you succeed!</p>
                </div>
                <div class="footer">
                    <p>© 2025 <strong>YouLife</strong>. All rights reserved.</p>
                    <p>This is an automated message, please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>
        `;
	}

	// Legacy template - keeping for backward compatibility
	getPasswordResetEmailTemplate(name, resetUrl) {
		return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Reset Your Password</title>
            <style>
                body { 
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                    line-height: 1.6; 
                    color: #333; 
                    margin: 0;
                    padding: 0;
                    background-color: #f8fffe;
                }
                .container { 
                    max-width: 600px; 
                    margin: 0 auto; 
                    padding: 20px; 
                }
                .header { 
                    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); 
                    color: white; 
                    padding: 40px 30px; 
                    text-align: center; 
                    border-radius: 12px 12px 0 0;
                    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.2);
                }
                .logo {
                    margin-bottom: 20px;
                }
                .logo img {
                    height: 60px;
                    width: auto;
                }
                .header h1 {
                    margin: 0;
                    font-size: 28px;
                    font-weight: 600;
                }
                .header p {
                    margin: 10px 0 0 0;
                    font-size: 16px;
                    opacity: 0.9;
                }
                .content { 
                    background: white; 
                    padding: 40px 30px; 
                    border-radius: 0 0 12px 12px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                }
                .footer { 
                    text-align: center; 
                    margin-top: 30px; 
                    color: #6b7280; 
                    font-size: 14px; 
                    padding: 20px;
                }
                .button { 
                    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); 
                    color: white; 
                    padding: 15px 35px; 
                    text-decoration: none; 
                    border-radius: 8px; 
                    display: inline-block; 
                    margin: 25px 0;
                    font-weight: 600;
                    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
                }
                .warning { 
                    background: #fef3c7; 
                    border: 1px solid #f59e0b; 
                    padding: 20px; 
                    border-radius: 8px; 
                    margin: 25px 0;
                    border-left: 4px solid #f59e0b;
                }
                .highlight {
                    color: #0f766e;
                    font-weight: 600;
                }
                .divider {
                    height: 1px;
                    background: linear-gradient(to right, transparent, #dc2626, transparent);
                    margin: 30px 0;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="logo">
                        <img src="https://res.cloudinary.com/dsaqhw9qj/image/upload/v1751413629/logo_jxduxv.png" alt="YouLife Logo">
                    </div>
                    <h1>🔒 Reset Your Password</h1>
                    <p>YouLife Account Security</p>
                </div>
                <div class="content">
                    <h2>Hello <span class="highlight">${name}</span>!</h2>
                    <p>We received a request to reset your password for your YouLife account.</p>
                    <p>Click the button below to securely reset your password:</p>
                    <div style="text-align: center;">
                        <a href="${resetUrl}" class="button">🔐 Reset Password</a>
                    </div>
                    <div class="warning">
                        <strong>⚠️ Important Security Information:</strong>
                        <ul style="margin: 15px 0;">
                            <li>This link will expire in <strong>30 minutes</strong></li>
                            <li>If you didn't request this reset, please ignore this email</li>
                            <li>Your password will not be changed unless you click the link above</li>
                            <li>Never share this link with anyone</li>
                        </ul>
                    </div>
                    <div class="divider"></div>
                    <p>If the button doesn't work, copy and paste this link into your browser:</p>
                    <p style="word-break: break-all; color: #14b8a6; background: #f0fdfc; padding: 10px; border-radius: 4px; font-family: monospace;">${resetUrl}</p>
                </div>
                <div class="footer">
                    <p>© 2025 <strong>YouLife</strong>. All rights reserved.</p>
                    <p>This is an automated message, please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>
        `;
	}

	// NEW: Password Reset OTP Email Template
	getPasswordResetOTPEmailTemplate(name, otp) {
		return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Password Reset Code</title>
            <style>
                body { 
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                    line-height: 1.6; 
                    color: #333; 
                    margin: 0;
                    padding: 0;
                    background-color: #f8fffe;
                }
                .container { 
                    max-width: 600px; 
                    margin: 0 auto; 
                    padding: 20px; 
                }
                .header { 
                    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); 
                    color: white; 
                    padding: 40px 30px; 
                    text-align: center; 
                    border-radius: 12px 12px 0 0;
                    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.2);
                }
                .logo {
                    margin-bottom: 20px;
                }
                .logo img {
                    height: 60px;
                    width: auto;
                }
                .header h1 {
                    margin: 0;
                    font-size: 28px;
                    font-weight: 600;
                }
                .header p {
                    margin: 10px 0 0 0;
                    font-size: 16px;
                    opacity: 0.9;
                }
                .content { 
                    background: white; 
                    padding: 40px 30px; 
                    border-radius: 0 0 12px 12px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                }
                .otp-box { 
                    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%); 
                    border: 2px solid #dc2626; 
                    border-radius: 12px; 
                    padding: 30px 20px; 
                    text-align: center; 
                    margin: 30px 0;
                    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.1);
                }
                .otp-code { 
                    font-size: 36px; 
                    font-weight: 700; 
                    color: #b91c1c; 
                    letter-spacing: 8px; 
                    margin: 15px 0;
                    font-family: 'Courier New', monospace;
                }
                .footer { 
                    text-align: center; 
                    margin-top: 30px; 
                    color: #6b7280; 
                    font-size: 14px; 
                    padding: 20px;
                }
                .warning { 
                    background: #fef3c7; 
                    border: 1px solid #f59e0b; 
                    padding: 20px; 
                    border-radius: 8px; 
                    margin: 25px 0;
                    border-left: 4px solid #f59e0b;
                }
                .security-tips { 
                    background: #f0fdfc; 
                    border-left: 4px solid #14b8a6; 
                    padding: 20px; 
                    margin: 25px 0;
                    border-radius: 8px;
                }
                .highlight {
                    color: #0f766e;
                    font-weight: 600;
                }
                .divider {
                    height: 1px;
                    background: linear-gradient(to right, transparent, #dc2626, transparent);
                    margin: 30px 0;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="logo">
                        <img src="https://res.cloudinary.com/dsaqhw9qj/image/upload/v1751413629/logo_jxduxv.png" alt="YouLife Logo">
                    </div>
                    <h1>🔒 Password Reset Code</h1>
                    <p>YouLife Account Security</p>
                </div>
                <div class="content">
                    <h2>Hello <span class="highlight">${name}</span>!</h2>
                    <p>We received a request to reset your password for your YouLife account. Use the verification code below to proceed:</p>
                    
                    <div class="otp-box">
                        <p style="margin: 0; font-size: 16px; color: #b91c1c; font-weight: 600;">Your password reset code is:</p>
                        <div class="otp-code">${otp}</div>
                        <p style="margin: 0; font-size: 14px; color: #6b7280;"><small>⏰ This code expires in 10 minutes</small></p>
                    </div>
                    
                    <div class="warning">
                        <strong>⚠️ Security Notice:</strong>
                        <ul style="margin: 15px 0;">
                            <li>This code is valid for <strong>10 minutes only</strong></li>
                            <li>You have <strong>3 attempts</strong> to enter the correct code</li>
                            <li>If you didn't request this reset, please ignore this email</li>
                            <li><strong>Never share this code with anyone</strong></li>
                        </ul>
                    </div>

                    <div class="security-tips">
                        <strong>🛡️ Security Tips for Your New Password:</strong>
                        <p style="margin: 15px 0;">When choosing a new password, make sure it:</p>
                        <ul style="margin: 15px 0;">
                            <li>Is at least <strong>8 characters long</strong></li>
                            <li>Contains <strong>uppercase and lowercase letters</strong></li>
                            <li>Includes <strong>numbers and special characters</strong></li>
                            <li>Is <strong>unique and not used elsewhere</strong></li>
                        </ul>
                    </div>

                    <div class="divider"></div>

                    <p>Enter this code on the password reset page to continue with changing your password.</p>
                </div>
                <div class="footer">
                    <p>© 2025 <strong>YouLife</strong>. All rights reserved.</p>
                    <p>This is an automated message, please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>
        `;
	}

	getPasswordChangeConfirmationTemplate(name) {
		return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Password Changed</title>
            <style>
                body { 
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                    line-height: 1.6; 
                    color: #333; 
                    margin: 0;
                    padding: 0;
                    background-color: #f8fffe;
                }
                .container { 
                    max-width: 600px; 
                    margin: 0 auto; 
                    padding: 20px; 
                }
                .header { 
                    background: linear-gradient(135deg, #059669 0%, #047857 100%); 
                    color: white; 
                    padding: 40px 30px; 
                    text-align: center; 
                    border-radius: 12px 12px 0 0;
                    box-shadow: 0 4px 12px rgba(5, 150, 105, 0.2);
                }
                .logo {
                    margin-bottom: 20px;
                }
                .logo img {
                    height: 60px;
                    width: auto;
                }
                .header h1 {
                    margin: 0;
                    font-size: 28px;
                    font-weight: 600;
                }
                .header p {
                    margin: 10px 0 0 0;
                    font-size: 16px;
                    opacity: 0.9;
                }
                .content { 
                    background: white; 
                    padding: 40px 30px; 
                    border-radius: 0 0 12px 12px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                }
                .footer { 
                    text-align: center; 
                    margin-top: 30px; 
                    color: #6b7280; 
                    font-size: 14px; 
                    padding: 20px;
                }
                .success { 
                    background: #d1fae5; 
                    border: 1px solid #059669; 
                    padding: 20px; 
                    border-radius: 8px; 
                    margin: 25px 0; 
                    color: #064e3b;
                    border-left: 4px solid #059669;
                }
                .alert { 
                    background: #fef2f2; 
                    border: 1px solid #dc2626; 
                    padding: 20px; 
                    border-radius: 8px; 
                    margin: 25px 0; 
                    color: #7f1d1d;
                    border-left: 4px solid #dc2626;
                }
                .security-reminder { 
                    background: #f0fdfc; 
                    border: 1px solid #14b8a6; 
                    padding: 20px; 
                    border-radius: 8px; 
                    margin: 25px 0; 
                    color: #134e4a;
                    border-left: 4px solid #14b8a6;
                }
                .highlight {
                    color: #0f766e;
                    font-weight: 600;
                }
                .divider {
                    height: 1px;
                    background: linear-gradient(to right, transparent, #059669, transparent);
                    margin: 30px 0;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="logo">
                        <img src="https://res.cloudinary.com/dsaqhw9qj/image/upload/v1751413629/logo_jxduxv.png" alt="YouLife Logo">
                    </div>
                    <h1>✅ Password Changed Successfully</h1>
                    <p>YouLife Account Security</p>
                </div>
                <div class="content">
                    <h2>Hello <span class="highlight">${name}</span>!</h2>
                    <div class="success">
                        <strong>✅ Success!</strong> Your password has been successfully changed.
                    </div>
                    <p>Your YouLife account password was updated on <strong>${new Date().toLocaleString()}</strong>.</p>
                    
                    <p>If you made this change, no further action is required. Your account remains secure and protected.</p>
                    
                    <div class="alert">
                        <strong>🚨 If you didn't make this change:</strong>
                        <ul style="margin: 15px 0;">
                            <li>Contact our support team immediately at <strong><EMAIL></strong></li>
                            <li>Review your account for any suspicious activity</li>
                            <li>Consider updating your security settings</li>
                            <li>Check your recent login activity</li>
                        </ul>
                    </div>

                    <div class="security-reminder">
                        <strong>🔐 Security Reminders:</strong>
                        <ul style="margin: 15px 0;">
                            <li>Keep your password confidential and never share it</li>
                            <li>Use unique passwords for different accounts</li>
                            <li>Enable two-factor authentication if available</li>
                            <li>Always log out from shared or public devices</li>
                            <li>Regularly review your account activity</li>
                        </ul>
                    </div>

                    <div class="divider"></div>

                    <p>Thank you for keeping your YouLife account secure! We're here to support your journey.</p>
                </div>
                <div class="footer">
                    <p>© 2025 <strong>YouLife</strong>. All rights reserved.</p>
                    <p>This is an automated message, please do not reply to this email.</p>
                    <p>Need help? Contact us at <strong><EMAIL></strong></p>
                </div>
            </div>
        </body>
        </html>
        `;
	}
}

// Create and export singleton instance
const emailService = new EmailService();

module.exports = emailService;
