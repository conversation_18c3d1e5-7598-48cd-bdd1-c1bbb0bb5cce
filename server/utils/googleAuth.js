const { OAuth2Client } = require('google-auth-library');

class GoogleAuthService {
    constructor() {
        this.client = new OAuth2Client(
            process.env.GOOGLE_CLIENT_ID,
            process.env.GOOGLE_CLIENT_SECRET,
            process.env.GOOGLE_REDIRECT_URI
        );
    }

    /**
     * Verify Google ID token
     * @param {String} idToken - Google ID token from client
     * @returns {Object} Verified user payload
     */
    async verifyIdToken(idToken) {
        try {
            const ticket = await this.client.verifyIdToken({
                idToken,
                audience: process.env.GOOGLE_CLIENT_ID
            });

            const payload = ticket.getPayload();
            
            if (!payload) {
                throw new Error('Invalid token payload');
            }

            return {
                googleId: payload.sub,
                email: payload.email,
                name: payload.name,
                picture: payload.picture,
                emailVerified: payload.email_verified,
                givenName: payload.given_name,
                familyName: payload.family_name,
                locale: payload.locale
            };
        } catch (error) {
            console.error('Google token verification error:', error);
            throw new Error('Invalid Google token');
        }
    }

    /**
     * Get authorization URL for OAuth flow
     * @param {String} state - Optional state parameter
     * @returns {String} Authorization URL
     */
    getAuthUrl(state = null) {
        const scopes = [
            'https://www.googleapis.com/auth/userinfo.email',
            'https://www.googleapis.com/auth/userinfo.profile'
        ];

        const authUrl = this.client.generateAuthUrl({
            access_type: 'offline',
            scope: scopes,
            state: state,
            prompt: 'consent'
        });

        return authUrl;
    }

    /**
     * Exchange authorization code for tokens
     * @param {String} code - Authorization code from callback
     * @returns {Object} Token information
     */
    async getTokensFromCode(code) {
        try {
            const { tokens } = await this.client.getToken(code);
            this.client.setCredentials(tokens);

            // Get user info using the access token
            const userInfo = await this.getUserInfo(tokens.access_token);

            return {
                tokens,
                userInfo
            };
        } catch (error) {
            console.error('Error exchanging code for tokens:', error);
            throw new Error('Failed to exchange authorization code');
        }
    }

    /**
     * Get user information using access token
     * @param {String} accessToken - Google access token
     * @returns {Object} User information
     */
    async getUserInfo(accessToken) {
        try {
            const response = await fetch(
                `https://www.googleapis.com/oauth2/v2/userinfo?access_token=${accessToken}`
            );

            if (!response.ok) {
                throw new Error('Failed to fetch user info');
            }

            const userInfo = await response.json();
            
            return {
                googleId: userInfo.id,
                email: userInfo.email,
                name: userInfo.name,
                picture: userInfo.picture,
                emailVerified: userInfo.verified_email,
                givenName: userInfo.given_name,
                familyName: userInfo.family_name,
                locale: userInfo.locale
            };
        } catch (error) {
            console.error('Error fetching user info:', error);
            throw new Error('Failed to fetch user information');
        }
    }

    /**
     * Revoke Google tokens
     * @param {String} accessToken - Access token to revoke
     * @returns {Boolean} Success status
     */
    async revokeToken(accessToken) {
        try {
            const response = await fetch(
                `https://oauth2.googleapis.com/revoke?token=${accessToken}`,
                { method: 'POST' }
            );

            return response.ok;
        } catch (error) {
            console.error('Error revoking token:', error);
            return false;
        }
    }

    /**
     * Refresh access token
     * @param {String} refreshToken - Refresh token
     * @returns {Object} New token information
     */
    async refreshAccessToken(refreshToken) {
        try {
            this.client.setCredentials({
                refresh_token: refreshToken
            });

            const { credentials } = await this.client.refreshAccessToken();
            return credentials;
        } catch (error) {
            console.error('Error refreshing token:', error);
            throw new Error('Failed to refresh access token');
        }
    }

    /**
     * Validate required environment variables
     * @returns {Boolean} True if all required vars are present
     */
    validateConfig() {
        const requiredVars = [
            'GOOGLE_CLIENT_ID',
            'GOOGLE_CLIENT_SECRET',
            'GOOGLE_REDIRECT_URI'
        ];

        const missing = requiredVars.filter(varName => !process.env[varName]);
        
        if (missing.length > 0) {
            console.error('Missing Google OAuth environment variables:', missing);
            return false;
        }

        return true;
    }

    /**
     * Generate a random state parameter for OAuth flow
     * @returns {String} Random state string
     */
    generateState() {
        return Math.random().toString(36).substring(2, 15) + 
               Math.random().toString(36).substring(2, 15);
    }
}

// Create and export singleton instance
const googleAuthService = new GoogleAuthService();

module.exports = googleAuthService;
