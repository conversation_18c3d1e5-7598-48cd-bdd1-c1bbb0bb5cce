const { getRedisClient } = require('../config/redis');

class RedisService {
    constructor() {
        this.client = null;
    }

    // Initialize Redis client
    init() {
        this.client = getRedisClient();
    }

    // Basic key-value operations
    async set(key, value, expiration = null) {
        try {
            if (!this.client) this.init();
            
            const serializedValue = typeof value === 'object' ? JSON.stringify(value) : value;
            
            if (expiration) {
                return await this.client.setex(key, expiration, serializedValue);
            } else {
                return await this.client.set(key, serializedValue);
            }
        } catch (error) {
            console.error('Redis SET error:', error);
            throw error;
        }
    }

    async get(key) {
        try {
            if (!this.client) this.init();
            
            const value = await this.client.get(key);
            if (!value) return null;

            // Try to parse as JSON, if it fails return as string
            try {
                return JSON.parse(value);
            } catch {
                return value;
            }
        } catch (error) {
            console.error('Redis GET error:', error);
            throw error;
        }
    }

    async del(key) {
        try {
            if (!this.client) this.init();
            return await this.client.del(key);
        } catch (error) {
            console.error('Redis DEL error:', error);
            throw error;
        }
    }

    async exists(key) {
        try {
            if (!this.client) this.init();
            return await this.client.exists(key);
        } catch (error) {
            console.error('Redis EXISTS error:', error);
            throw error;
        }
    }

    async expire(key, seconds) {
        try {
            if (!this.client) this.init();
            return await this.client.expire(key, seconds);
        } catch (error) {
            console.error('Redis EXPIRE error:', error);
            throw error;
        }
    }

    // Hash operations
    async hset(key, field, value) {
        try {
            if (!this.client) this.init();
            const serializedValue = typeof value === 'object' ? JSON.stringify(value) : value;
            return await this.client.hset(key, field, serializedValue);
        } catch (error) {
            console.error('Redis HSET error:', error);
            throw error;
        }
    }

    async hget(key, field) {
        try {
            if (!this.client) this.init();
            const value = await this.client.hget(key, field);
            if (!value) return null;

            try {
                return JSON.parse(value);
            } catch {
                return value;
            }
        } catch (error) {
            console.error('Redis HGET error:', error);
            throw error;
        }
    }

    async hgetall(key) {
        try {
            if (!this.client) this.init();
            const hashData = await this.client.hgetall(key);
            
            // Parse JSON values
            const result = {};
            for (const [field, value] of Object.entries(hashData)) {
                try {
                    result[field] = JSON.parse(value);
                } catch {
                    result[field] = value;
                }
            }
            return result;
        } catch (error) {
            console.error('Redis HGETALL error:', error);
            throw error;
        }
    }

    async hdel(key, field) {
        try {
            if (!this.client) this.init();
            return await this.client.hdel(key, field);
        } catch (error) {
            console.error('Redis HDEL error:', error);
            throw error;
        }
    }

    // List operations
    async lpush(key, value) {
        try {
            if (!this.client) this.init();
            const serializedValue = typeof value === 'object' ? JSON.stringify(value) : value;
            return await this.client.lpush(key, serializedValue);
        } catch (error) {
            console.error('Redis LPUSH error:', error);
            throw error;
        }
    }

    async rpush(key, value) {
        try {
            if (!this.client) this.init();
            const serializedValue = typeof value === 'object' ? JSON.stringify(value) : value;
            return await this.client.rpush(key, serializedValue);
        } catch (error) {
            console.error('Redis RPUSH error:', error);
            throw error;
        }
    }

    async lpop(key) {
        try {
            if (!this.client) this.init();
            const value = await this.client.lpop(key);
            if (!value) return null;

            try {
                return JSON.parse(value);
            } catch {
                return value;
            }
        } catch (error) {
            console.error('Redis LPOP error:', error);
            throw error;
        }
    }

    async lrange(key, start, stop) {
        try {
            if (!this.client) this.init();
            const values = await this.client.lrange(key, start, stop);
            
            return values.map(value => {
                try {
                    return JSON.parse(value);
                } catch {
                    return value;
                }
            });
        } catch (error) {
            console.error('Redis LRANGE error:', error);
            throw error;
        }
    }

    // Set operations
    async sadd(key, member) {
        try {
            if (!this.client) this.init();
            const serializedMember = typeof member === 'object' ? JSON.stringify(member) : member;
            return await this.client.sadd(key, serializedMember);
        } catch (error) {
            console.error('Redis SADD error:', error);
            throw error;
        }
    }

    async srem(key, member) {
        try {
            if (!this.client) this.init();
            const serializedMember = typeof member === 'object' ? JSON.stringify(member) : member;
            return await this.client.srem(key, serializedMember);
        } catch (error) {
            console.error('Redis SREM error:', error);
            throw error;
        }
    }

    async smembers(key) {
        try {
            if (!this.client) this.init();
            const members = await this.client.smembers(key);
            
            return members.map(member => {
                try {
                    return JSON.parse(member);
                } catch {
                    return member;
                }
            });
        } catch (error) {
            console.error('Redis SMEMBERS error:', error);
            throw error;
        }
    }

    // Session management
    async setSession(sessionId, sessionData, expiration = 3600) {
        try {
            const key = `session:${sessionId}`;
            return await this.set(key, sessionData, expiration);
        } catch (error) {
            console.error('Redis session set error:', error);
            throw error;
        }
    }

    async getSession(sessionId) {
        try {
            const key = `session:${sessionId}`;
            return await this.get(key);
        } catch (error) {
            console.error('Redis session get error:', error);
            throw error;
        }
    }

    async deleteSession(sessionId) {
        try {
            const key = `session:${sessionId}`;
            return await this.del(key);
        } catch (error) {
            console.error('Redis session delete error:', error);
            throw error;
        }
    }

    // Rate limiting
    async incrementRateLimit(key, windowSize = 60, limit = 100) {
        try {
            if (!this.client) this.init();
            
            const current = await this.client.incr(key);
            
            if (current === 1) {
                await this.client.expire(key, windowSize);
            }
            
            return {
                current,
                remaining: Math.max(0, limit - current),
                resetTime: Date.now() + (windowSize * 1000)
            };
        } catch (error) {
            console.error('Redis rate limit error:', error);
            throw error;
        }
    }

    // Cache with automatic expiration
    async cache(key, data, expiration = 3600) {
        try {
            return await this.set(`cache:${key}`, data, expiration);
        } catch (error) {
            console.error('Redis cache set error:', error);
            throw error;
        }
    }

    async getCached(key) {
        try {
            return await this.get(`cache:${key}`);
        } catch (error) {
            console.error('Redis cache get error:', error);
            throw error;
        }
    }

    async invalidateCache(pattern) {
        try {
            if (!this.client) this.init();
            
            const keys = await this.client.keys(`cache:${pattern}`);
            if (keys.length > 0) {
                return await this.client.del(...keys);
            }
            return 0;
        } catch (error) {
            console.error('Redis cache invalidation error:', error);
            throw error;
        }
    }

    // Pub/Sub operations
    async publish(channel, message) {
        try {
            if (!this.client) this.init();
            const serializedMessage = typeof message === 'object' ? JSON.stringify(message) : message;
            return await this.client.publish(channel, serializedMessage);
        } catch (error) {
            console.error('Redis publish error:', error);
            throw error;
        }
    }

    async subscribe(channel, callback) {
        try {
            if (!this.client) this.init();
            
            const subscriber = this.client.duplicate();
            
            subscriber.subscribe(channel, (err, count) => {
                if (err) {
                    console.error('Redis subscribe error:', err);
                    return;
                }
                console.log(`Subscribed to ${count} channel(s)`);
            });
            
            subscriber.on('message', (receivedChannel, message) => {
                if (receivedChannel === channel) {
                    try {
                        const parsedMessage = JSON.parse(message);
                        callback(parsedMessage);
                    } catch {
                        callback(message);
                    }
                }
            });
            
            return subscriber;
        } catch (error) {
            console.error('Redis subscribe setup error:', error);
            throw error;
        }
    }
}

// Create and export singleton instance
const redisService = new RedisService();

module.exports = redisService;
