const jwt = require('jsonwebtoken');
const crypto = require('crypto');

class JWTUtils {
    constructor() {
        this.accessTokenSecret = process.env.JWT_SECRET || 'fallback-secret-key';
        this.refreshTokenSecret = process.env.JWT_REFRESH_SECRET || 'fallback-refresh-secret-key';
        this.accessTokenExpiry = process.env.JWT_EXPIRE || '28d';
        this.refreshTokenExpiry = process.env.JWT_REFRESH_EXPIRE || '7d';
    }

    /**
     * Generate access token
     * @param {Object} payload - User data to include in token
     * @returns {String} JWT access token
     */
    generateAccessToken(payload) {
        try {
            const tokenPayload = {
                id: payload.id,
                email: payload.email,
                roles: payload.roles,
                status: payload.status,
                isEmailVerified: payload.isEmailVerified
            };

            return jwt.sign(tokenPayload, this.accessTokenSecret, {
                expiresIn: this.accessTokenExpiry,
                issuer: 'you-life-api',
                audience: 'you-life-client'
            });
        } catch (error) {
            throw new Error('Failed to generate access token');
        }
    }

    /**
     * Generate refresh token
     * @param {Object} payload - User data to include in token
     * @returns {String} JWT refresh token
     */
    generateRefreshToken(payload) {
        try {
            const tokenPayload = {
                id: payload.id,
                email: payload.email,
                tokenVersion: payload.tokenVersion || 0
            };

            return jwt.sign(tokenPayload, this.refreshTokenSecret, {
                expiresIn: this.refreshTokenExpiry,
                issuer: 'you-life-api',
                audience: 'you-life-client'
            });
        } catch (error) {
            throw new Error('Failed to generate refresh token');
        }
    }

    /**
     * Generate both access and refresh tokens
     * @param {Object} user - User object
     * @returns {Object} Object containing both tokens
     */
    generateTokens(user) {
        const payload = {
            id: user._id || user.id,
            email: user.email,
            roles: user.roles,
            status: user.status,
            isEmailVerified: user.isEmailVerified,
            tokenVersion: user.tokenVersion
        };

        return {
            accessToken: this.generateAccessToken(payload),
            refreshToken: this.generateRefreshToken(payload)
        };
    }

    /**
     * Verify access token
     * @param {String} token - JWT token to verify
     * @returns {Object} Decoded token payload
     */
    verifyAccessToken(token) {
        try {
            return jwt.verify(token, this.accessTokenSecret, {
                issuer: 'you-life-api',
                audience: 'you-life-client'
            });
        } catch (error) {
            if (error.name === 'TokenExpiredError') {
                throw new Error('Access token has expired');
            } else if (error.name === 'JsonWebTokenError') {
                throw new Error('Invalid access token');
            } else {
                throw new Error('Token verification failed');
            }
        }
    }

    /**
     * Verify refresh token
     * @param {String} token - JWT refresh token to verify
     * @returns {Object} Decoded token payload
     */
    verifyRefreshToken(token) {
        try {
            return jwt.verify(token, this.refreshTokenSecret, {
                issuer: 'you-life-api',
                audience: 'you-life-client'
            });
        } catch (error) {
            if (error.name === 'TokenExpiredError') {
                throw new Error('Refresh token has expired');
            } else if (error.name === 'JsonWebTokenError') {
                throw new Error('Invalid refresh token');
            } else {
                throw new Error('Refresh token verification failed');
            }
        }
    }

    /**
     * Decode token without verification (for debugging)
     * @param {String} token - JWT token to decode
     * @returns {Object} Decoded token payload
     */
    decodeToken(token) {
        try {
            return jwt.decode(token, { complete: true });
        } catch (error) {
            throw new Error('Failed to decode token');
        }
    }

    /**
     * Extract token from Authorization header
     * @param {String} authHeader - Authorization header value
     * @returns {String|null} Extracted token or null
     */
    extractTokenFromHeader(authHeader) {
        if (!authHeader) return null;
        
        const parts = authHeader.split(' ');
        if (parts.length !== 2 || parts[0] !== 'Bearer') {
            return null;
        }
        
        return parts[1];
    }

    /**
     * Generate a secure random token for password reset, etc.
     * @param {Number} length - Length of the token in bytes
     * @returns {String} Random token
     */
    generateSecureToken(length = 32) {
        return crypto.randomBytes(length).toString('hex');
    }

    /**
     * Hash a token for storage (e.g., password reset tokens)
     * @param {String} token - Token to hash
     * @returns {String} Hashed token
     */
    hashToken(token) {
        return crypto.createHash('sha256').update(token).digest('hex');
    }

    /**
     * Get token expiration time
     * @param {String} token - JWT token
     * @returns {Date|null} Expiration date or null
     */
    getTokenExpiration(token) {
        try {
            const decoded = jwt.decode(token);
            return decoded && decoded.exp ? new Date(decoded.exp * 1000) : null;
        } catch (error) {
            return null;
        }
    }

    /**
     * Check if token is expired
     * @param {String} token - JWT token
     * @returns {Boolean} True if expired, false otherwise
     */
    isTokenExpired(token) {
        const expiration = this.getTokenExpiration(token);
        return expiration ? expiration <= new Date() : true;
    }

    /**
     * Get time until token expires
     * @param {String} token - JWT token
     * @returns {Number} Time in milliseconds until expiration
     */
    getTimeUntilExpiration(token) {
        const expiration = this.getTokenExpiration(token);
        return expiration ? Math.max(0, expiration.getTime() - Date.now()) : 0;
    }

    /**
     * Create password reset token
     * @returns {Object} Object with token and hashed token
     */
    createPasswordResetToken() {
        const token = this.generateSecureToken();
        const hashedToken = this.hashToken(token);
        
        return {
            token,
            hashedToken,
            expiresAt: new Date(Date.now() + 30 * 60 * 1000) // 30 minutes
        };
    }

    /**
     * Verify password reset token
     * @param {String} token - Plain token
     * @param {String} hashedToken - Stored hashed token
     * @param {Date} expiresAt - Token expiration date
     * @returns {Boolean} True if valid, false otherwise
     */
    verifyPasswordResetToken(token, hashedToken, expiresAt) {
        if (!token || !hashedToken || !expiresAt) {
            return false;
        }

        if (new Date() > expiresAt) {
            return false;
        }

        const hashedProvidedToken = this.hashToken(token);
        return hashedProvidedToken === hashedToken;
    }

    /**
     * Create email verification token
     * @param {String} email - User email
     * @returns {String} Verification token
     */
    createEmailVerificationToken(email) {
        const payload = {
            email,
            purpose: 'email-verification',
            timestamp: Date.now()
        };

        return jwt.sign(payload, this.accessTokenSecret, {
            expiresIn: '24h',
            issuer: 'you-life-api'
        });
    }

    /**
     * Verify email verification token
     * @param {String} token - Verification token
     * @returns {Object} Decoded payload
     */
    verifyEmailVerificationToken(token) {
        try {
            const decoded = jwt.verify(token, this.accessTokenSecret, {
                issuer: 'you-life-api'
            });

            if (decoded.purpose !== 'email-verification') {
                throw new Error('Invalid token purpose');
            }

            return decoded;
        } catch (error) {
            if (error.name === 'TokenExpiredError') {
                throw new Error('Email verification token has expired');
            } else if (error.name === 'JsonWebTokenError') {
                throw new Error('Invalid email verification token');
            } else {
                throw new Error('Email verification token verification failed');
            }
        }
    }
}

// Create and export singleton instance
const jwtUtils = new JWTUtils();

module.exports = jwtUtils;
