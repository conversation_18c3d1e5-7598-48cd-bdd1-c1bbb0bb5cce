const express = require('express');
const goalController = require('../controller/goalController');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Goal endpoints
router.post('/', authenticateToken, goalController.createGoal);
router.get('/', authenticateToken, goalController.getUserGoals);
router.get('/public', goalController.getPublicGoals);
router.get('/:id', authenticateToken, goalController.getGoalById);
router.put('/:id', authenticateToken, goalController.updateGoal);
router.delete('/:id', authenticateToken, goalController.deleteGoal);

// Task endpoints
router.put('/:id/tasks/:taskId', authenticateToken, goalController.updateTaskStatus);
router.post('/:id/tasks', authenticateToken, goalController.addTask);
router.delete('/:id/tasks/:taskId', authenticateToken, goalController.deleteTask);

module.exports = router;

