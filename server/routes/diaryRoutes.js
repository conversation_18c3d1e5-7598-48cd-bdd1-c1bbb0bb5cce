const express = require('express');
const diaryController = require('../controller/diaryController');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Image upload endpoint
router.post('/upload-image', authenticateToken, diaryController.uploadMiddleware, diaryController.uploadImage);

// Diary entry endpoints
router.post('/', authenticateToken, diaryController.createDiaryEntry);
router.get('/', authenticateToken, diaryController.getUserDiaryEntries);
router.get('/recent', authenticateToken, diaryController.getRecentDiaryEntries);
router.get('/date/:date', authenticateToken, diaryController.getDiaryEntryByDate);
router.get('/:id', authenticateToken, diaryController.getDiaryEntryById);
router.put('/:id', authenticateToken, diaryController.updateDiaryEntry);
router.delete('/:id', authenticateToken, diaryController.deleteDiaryEntry);

module.exports = router;
