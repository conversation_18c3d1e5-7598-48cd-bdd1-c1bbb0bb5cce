const express = require('express');
const router = express.Router();

const {
	requireOwnershipOrAdmin,
	authenticateToken,
} = require('../middleware/auth');
const lessonController = require('../controller/lessonController');

// Routes
router.get('/lessons', authenticateToken, lessonController.getLessons);
router.get('/:id', authenticateToken, lessonController.getLessonById);

// Create lesson - now accepts URLs instead of files
router.post(
	'/lessons',
	authenticateToken,
	lessonController.uploadLesson,
);

// Update lesson - now accepts URLs instead of files
router.put(
	'/:id',
	authenticateToken,
	lessonController.updateLesson,
);

router.delete('/:id', authenticateToken, lessonController.deleteLesson);

module.exports = router;
