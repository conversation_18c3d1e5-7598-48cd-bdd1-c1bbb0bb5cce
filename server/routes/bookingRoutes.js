const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const router = express.Router();
const bookingController = require('../controller/bookingController');
const {
	validateCreateBooking,
	validateEmailParam,
	validateUpdateStatus,
	validateBookingId,
} = require('../middleware/bookingValidators');

// User routes
router.post(
	'/create',
	authenticateToken,
	validateCreateBooking,
	bookingController.createBooking,
);
router.get(
	'/available',
	authenticateToken,
	bookingController.getAvailableSlots,
);
router.get(
	'/user/:email',
	authenticateToken,
	validateEmailParam,
	bookingController.getUserBookings,
);

// Admin routes
router.get('/admin/all', authenticateToken, bookingController.getAllBookings);
router.get(
	'/admin/:id',
	authenticateToken,
	validateEmailParam,
	bookingController.getBookingById,
);
router.patch(
	'/admin/:id/status',
	authenticateToken,
	validateUpdateStatus,
	bookingController.updateBookingStatus,
);
router.patch(
	'/admin/:id/cancel',
	authenticateToken,
	validateBookingId,
	bookingController.cancelBooking,
);
router.delete(
	'/admin/:id',
	authenticateToken,
	validateBookingId,
	bookingController.deleteBooking,
);
