const express = require('express');
const authController = require('../controller/authController');

const router = express.Router();

router.post('/register', authController.registerUser);
router.post('/login', authController.login);
router.post('/verify-email', authController.verifyEmail);
router.post('/resend-verification', authController.resendVerificationCode);
router.post('/forgot-password', authController.forgotPassword);
router.post('/google-login', authController.googleLogin);
router.post(
	'/verify-password-reset-otp',
	authController.verifyPasswordResetOTP,
);
router.post('/reset-password', authController.resetPassword);

module.exports = router;
