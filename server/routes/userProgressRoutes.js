const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const userProgressController = require('../controller/userProgressController');

// Update user progress (video progress, reflections, notes)
router.post(
	'/progress',
	authenticateToken,
	userProgressController.updateUserProgress,
);

// Get all user progress (for ongoing videos) - must come before /:lessonId route
router.get(
	'/progress/all',
	authenticateToken,
	userProgressController.getAllUserProgress,
);

// Get user progress for a specific lesson
router.get(
	'/progress/:lessonId',
	authenticateToken,
	userProgressController.getUserProgress,
);

// Update reflection answer
router.post(
	'/reflection',
	authenticateToken,
	userProgressController.updateReflection,
);

// Delete reflection answer
router.delete(
	'/reflection/:lessonId/:questionIndex',
	authenticateToken,
	userProgressController.deleteReflection,
);

module.exports = router;
