const express = require('express');
const valueController = require('../controller/valueController');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Value endpoints
router.post('/', authenticateToken, valueController.createValue);
router.get('/', authenticateToken, valueController.getUserValues);
router.get('/public', valueController.getPublicValues);
router.get('/:id', authenticateToken, valueController.getValueById);
router.put('/:id', authenticateToken, valueController.updateValue);
router.delete('/:id', authenticateToken, valueController.deleteValue);

module.exports = router;
