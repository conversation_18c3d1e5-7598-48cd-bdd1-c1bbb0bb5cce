const express = require('express');
const router = express.Router();
const { emitToRoom, emitToUser, emitToAll } = require('../config/socket');
const redisService = require('../utils/redisService');
const socketHandlers = require('../utils/socketHandlers');

/**
 * @swagger
 * components:
 *   schemas:
 *     SocketMessage:
 *       type: object
 *       required:
 *         - event
 *         - data
 *       properties:
 *         event:
 *           type: string
 *           description: Socket event name
 *         data:
 *           type: object
 *           description: Event data
 *         roomId:
 *           type: string
 *           description: Room ID for room-specific events
 *         userId:
 *           type: string
 *           description: User ID for user-specific events
 */

/**
 * @swagger
 * /api/socket/emit-to-room:
 *   post:
 *     summary: Emit event to a specific room
 *     tags: [Socket]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - roomId
 *               - event
 *               - data
 *             properties:
 *               roomId:
 *                 type: string
 *               event:
 *                 type: string
 *               data:
 *                 type: object
 *     responses:
 *       200:
 *         description: Event emitted successfully
 *       400:
 *         description: Invalid request data
 */
router.post('/emit-to-room', async (req, res) => {
    try {
        const { roomId, event, data } = req.body;
        
        if (!roomId || !event || !data) {
            return res.status(400).json({
                success: false,
                message: 'roomId, event, and data are required'
            });
        }

        emitToRoom(roomId, event, data);

        res.json({
            success: true,
            message: `Event '${event}' emitted to room '${roomId}'`
        });
    } catch (error) {
        console.error('Emit to room error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to emit event to room'
        });
    }
});

/**
 * @swagger
 * /api/socket/emit-to-user:
 *   post:
 *     summary: Emit event to a specific user
 *     tags: [Socket]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - event
 *               - data
 *             properties:
 *               userId:
 *                 type: string
 *               event:
 *                 type: string
 *               data:
 *                 type: object
 *     responses:
 *       200:
 *         description: Event emitted successfully
 *       400:
 *         description: Invalid request data
 */
router.post('/emit-to-user', async (req, res) => {
    try {
        const { userId, event, data } = req.body;
        
        if (!userId || !event || !data) {
            return res.status(400).json({
                success: false,
                message: 'userId, event, and data are required'
            });
        }

        emitToUser(userId, event, data);

        res.json({
            success: true,
            message: `Event '${event}' emitted to user '${userId}'`
        });
    } catch (error) {
        console.error('Emit to user error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to emit event to user'
        });
    }
});

/**
 * @swagger
 * /api/socket/broadcast:
 *   post:
 *     summary: Broadcast event to all connected users
 *     tags: [Socket]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - event
 *               - data
 *             properties:
 *               event:
 *                 type: string
 *               data:
 *                 type: object
 *     responses:
 *       200:
 *         description: Event broadcasted successfully
 *       400:
 *         description: Invalid request data
 */
router.post('/broadcast', async (req, res) => {
    try {
        const { event, data } = req.body;
        
        if (!event || !data) {
            return res.status(400).json({
                success: false,
                message: 'event and data are required'
            });
        }

        emitToAll(event, data);

        res.json({
            success: true,
            message: `Event '${event}' broadcasted to all users`
        });
    } catch (error) {
        console.error('Broadcast error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to broadcast event'
        });
    }
});

/**
 * @swagger
 * /api/socket/online-users:
 *   get:
 *     summary: Get list of online users
 *     tags: [Socket]
 *     responses:
 *       200:
 *         description: List of online users
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     onlineUsers:
 *                       type: array
 *                       items:
 *                         type: string
 *                     count:
 *                       type: number
 */
router.get('/online-users', async (req, res) => {
    try {
        const connectedUsers = await redisService.hgetall('connected_users');
        const onlineUsers = Object.keys(connectedUsers);

        res.json({
            success: true,
            data: {
                onlineUsers,
                count: onlineUsers.length
            }
        });
    } catch (error) {
        console.error('Get online users error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get online users'
        });
    }
});

/**
 * @swagger
 * /api/socket/user-status/{userId}:
 *   get:
 *     summary: Check if a specific user is online
 *     tags: [Socket]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID to check
 *     responses:
 *       200:
 *         description: User status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                     isOnline:
 *                       type: boolean
 *                     lastActivity:
 *                       type: string
 */
router.get('/user-status/:userId', async (req, res) => {
    try {
        const { userId } = req.params;
        
        const isOnline = await socketHandlers.getUserOnlineStatus(userId);
        const userConnection = await redisService.hget('connected_users', userId);

        res.json({
            success: true,
            data: {
                userId,
                isOnline,
                lastActivity: userConnection?.lastActivity || null
            }
        });
    } catch (error) {
        console.error('Get user status error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get user status'
        });
    }
});

/**
 * @swagger
 * /api/socket/room-members/{roomId}:
 *   get:
 *     summary: Get members of a specific room
 *     tags: [Socket]
 *     parameters:
 *       - in: path
 *         name: roomId
 *         required: true
 *         schema:
 *           type: string
 *         description: Room ID
 *     responses:
 *       200:
 *         description: Room members
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     roomId:
 *                       type: string
 *                     members:
 *                       type: array
 *                       items:
 *                         type: string
 *                     count:
 *                       type: number
 */
router.get('/room-members/:roomId', async (req, res) => {
    try {
        const { roomId } = req.params;
        
        const members = await redisService.smembers(`room:${roomId}:members`);

        res.json({
            success: true,
            data: {
                roomId,
                members,
                count: members.length
            }
        });
    } catch (error) {
        console.error('Get room members error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get room members'
        });
    }
});

/**
 * @swagger
 * /api/socket/send-notification:
 *   post:
 *     summary: Send real-time notification to a user
 *     tags: [Socket]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - type
 *               - title
 *               - message
 *             properties:
 *               userId:
 *                 type: string
 *               type:
 *                 type: string
 *               title:
 *                 type: string
 *               message:
 *                 type: string
 *               data:
 *                 type: object
 *     responses:
 *       200:
 *         description: Notification sent successfully
 *       400:
 *         description: Invalid request data
 */
router.post('/send-notification', async (req, res) => {
    try {
        const { userId, type, title, message, data } = req.body;
        
        if (!userId || !type || !title || !message) {
            return res.status(400).json({
                success: false,
                message: 'userId, type, title, and message are required'
            });
        }

        const notification = {
            id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type,
            title,
            message,
            data: data || {},
            timestamp: new Date().toISOString(),
            read: false
        };

        // Store notification in Redis
        await redisService.lpush(`user:${userId}:notifications`, notification);

        // Send real-time notification
        const sent = await socketHandlers.sendNotificationToUser(userId, notification);

        res.json({
            success: true,
            message: 'Notification sent successfully',
            data: {
                notificationId: notification.id,
                sentRealTime: sent
            }
        });
    } catch (error) {
        console.error('Send notification error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to send notification'
        });
    }
});

/**
 * @swagger
 * /api/socket/notifications/{userId}:
 *   get:
 *     summary: Get notifications for a user
 *     tags: [Socket]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Number of notifications to retrieve
 *     responses:
 *       200:
 *         description: User notifications
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     notifications:
 *                       type: array
 *                       items:
 *                         type: object
 *                     count:
 *                       type: number
 */
router.get('/notifications/:userId', async (req, res) => {
    try {
        const { userId } = req.params;
        const limit = parseInt(req.query.limit) || 20;
        
        const notifications = await redisService.lrange(`user:${userId}:notifications`, 0, limit - 1);

        res.json({
            success: true,
            data: {
                notifications,
                count: notifications.length
            }
        });
    } catch (error) {
        console.error('Get notifications error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get notifications'
        });
    }
});

module.exports = router;
