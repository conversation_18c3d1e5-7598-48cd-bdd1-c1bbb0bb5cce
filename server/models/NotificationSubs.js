const mongoose = require('mongoose');

const subscriptionSchema = new mongoose.Schema({
	uid: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
	subscriptions: [
		{
			endpoint: { type: String, required: true },
			keys: {
				p256dh: { type: String, required: true },
				auth: { type: String, required: true },
			},
		},
	],
	createdAt: { type: Date, default: Date.now },
	updatedAt: { type: Date, default: Date.now },
});

// Export the model
module.exports = mongoose.model('Subscription', subscriptionSchema);
