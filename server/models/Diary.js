const mongoose = require('mongoose');

const diarySchema = new mongoose.Schema(
	{
		userId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
			required: true,
		},
		title: {
			type: String,
			required: true,
		},
		story: {
			type: String,
			required: true,
		},
		image: {
			type: String,
		},
		date: {
			type: Date,
			required: true,
		},
	},
	{ timestamps: true },
);

module.exports = mongoose.model('Diary', diarySchema);
