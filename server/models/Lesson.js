const mongoose = require('mongoose');

const lessonSchema = new mongoose.Schema(
	{
		title: { type: String, required: true },
		videoUrl: { type: String, required: true },
		thumbnail: { type: String, required: true }, // Store image URL or path
		instructor: { type: String, required: true },
		description: { type: String, required: true },
		category: { type: String, required: true },
		// Optional fields for future extension
		reflectionQuestions: [{ type: String }],
	},
	{ timestamps: true },
);

module.exports = mongoose.model('Lesson', lessonSchema);
