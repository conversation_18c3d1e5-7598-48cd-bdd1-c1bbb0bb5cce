const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');

const userSchema = new mongoose.Schema(
	{
		name: {
			type: String,
			required: [true, 'Name is required'],
			trim: true,
			maxlength: [50, 'Name cannot be more than 50 characters'],
		},
		email: {
			type: String,
			required: [true, 'Email is required'],
			unique: true,
			lowercase: true,
			match: [/^\S+@\S+\.\S+$/, 'Please enter a valid email'],
			index: true,
		},
		password: {
			type: String,
			required: function () {
				return !this.googleId;
			},
			minlength: [6, 'Password must be at least 6 characters'],
			select: false,
		},
		avatar: {
			type: String,
			default: null,
		},
		phone: {
			type: String,
			default: null,
		},
		roles: {
			type: [String],
			enum: ['user', 'superadmin', 'tutor'],
			default: ['user'],
		},
		status: {
			type: String,
			enum: ['active', 'inactive', 'suspended'],
			default: 'active', // Changed from 'inactive' to match controller behavior
		},
		isEmailVerified: {
			type: Boolean,
			default: false,
		},
		googleId: {
			type: String,
			sparse: true,
		},
		resetPasswordToken: {
			type: String,
			select: false,
		},
		resetPasswordExpires: {
			type: Date,
			select: false,
		},
		lastLogin: {
			type: Date,
			default: null,
		},
		loginAttempts: {
			type: Number,
			default: 0,
		},
		lockUntil: {
			type: Date,
		},
		twoFactorSecret: {
			type: String,
			select: false,
		},
		twoFactorEnabled: {
			type: Boolean,
			default: false,
		},
	},
	{
		timestamps: true,
		toJSON: { virtuals: true },
		toObject: { virtuals: true },
	},
);

// Create indexes
userSchema.index({ email: 1 });
userSchema.index({ googleId: 1 });
userSchema.index({ status: 1 });
userSchema.index({ roles: 1 });

// Virtual for account lock status
userSchema.virtual('isLocked').get(function () {
	return !!(this.lockUntil && this.lockUntil > Date.now());
});

// Pre-save middleware to hash password (only if not already hashed)
userSchema.pre('save', async function (next) {
	// Skip if password is not modified or is already hashed
	if (!this.isModified('password') || !this.password) return next();

	// Check if password is already hashed (bcrypt hashes start with $2a$, $2b$, or $2y$)
	if (this.password.match(/^\$2[aby]\$/)) {
		return next();
	}

	try {
		const salt = await bcrypt.genSalt(12);
		this.password = await bcrypt.hash(this.password, salt);
		next();
	} catch (error) {
		next(error);
	}
});

// Instance method to verify password
userSchema.methods.verifyPassword = async function (candidatePassword) {
	try {
		return await bcrypt.compare(candidatePassword, this.password);
	} catch (error) {
		throw new Error('Password verification failed');
	}
};

// Instance method to generate password reset token
userSchema.methods.generatePasswordResetToken = function () {
	const resetToken = crypto.randomBytes(32).toString('hex');
	this.resetPasswordToken = crypto
		.createHash('sha256')
		.update(resetToken)
		.digest('hex');
	this.resetPasswordExpires = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
	return resetToken;
};

// Instance method to handle failed login attempts
userSchema.methods.incLoginAttempts = function () {
	// If we have a previous lock that has expired, restart at 1
	if (this.lockUntil && this.lockUntil < Date.now()) {
		return this.updateOne({
			$unset: { lockUntil: 1 },
			$set: { loginAttempts: 1 },
		});
	}

	const updates = { $inc: { loginAttempts: 1 } };

	// If we have hit max attempts and it's not locked yet, lock it
	if (this.loginAttempts + 1 >= 5 && !this.isLocked) {
		updates.$set = { lockUntil: Date.now() + 2 * 60 * 60 * 1000 }; // Lock for 2 hours
	}

	return this.updateOne(updates);
};

// Instance method to reset login attempts
userSchema.methods.resetLoginAttempts = function () {
	return this.updateOne({
		$unset: { loginAttempts: 1, lockUntil: 1 },
	});
};

// Static method to find by email
userSchema.statics.findByEmail = function (email) {
	return this.findOne({ email: email.toLowerCase() });
};

// Static method to find active users
userSchema.statics.findActiveUsers = function () {
	return this.find({ status: 'active' });
};

// Static method to find by role
userSchema.statics.findByRole = function (role) {
	return this.find({ roles: role });
};

const User = mongoose.model('User', userSchema);

module.exports = User;
