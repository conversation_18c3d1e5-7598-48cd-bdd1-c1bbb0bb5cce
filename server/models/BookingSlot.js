const mongoose = require('mongoose');

const bookingSlotSchema = new mongoose.Schema(
	{
		date: { type: Date, required: true },
		isBooked: { type: Boolean, default: false },
		status: {
			type: String,
			enum: ['scheduled', 'completed', 'cancelled'],
			default: 'scheduled',
		},
		email: {
			type: String,
			lowercase: true,
			match: [/^\S+@\S+\.\S+$/, 'Please enter a valid email'],
			index: true,
		},
		phone: {
			type: String,
			match: [/^[0-9]{10,15}$/, 'Please enter a valid phone number'],
		},
		// Changed to store user name directly since frontend sends "Daniel Agbeni"
		bookedBy: {
			type: String, // Changed from ObjectId to String to match your frontend data
			default: null,
		},
		// Optional: Keep reference to User model if you have user accounts
		userId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
			default: null,
		},
		sessionType: {
			type: String,
			enum: ['Discovery', 'Transformation', 'Follow-up'],
			default: 'Discovery',
		},
	},
	{ timestamps: true },
);

// Compound index for better query performance
bookingSlotSchema.index({ date: 1, isBooked: 1 });
bookingSlotSchema.index({ email: 1, date: 1 });

module.exports = mongoose.model('BookingSlot', bookingSlotSchema);
