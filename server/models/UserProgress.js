const mongoose = require('mongoose');

const userProgressSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  lessonId: { type: mongoose.Schema.Types.ObjectId, ref: 'Lesson', required: true },
  isCompleted: { type: Boolean, default: false },
  videoProgress: { type: Number, default: 0 }, // Progress percentage (0-1)
  watchTime: { type: Number, default: 0 }, // Total watch time in seconds
  lastWatchedAt: { type: Date, default: Date.now },
  reflections: [{
    questionIndex: { type: Number, required: true },
    answer: { type: String, required: true },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
  }],
  notes: { type: String },
}, { timestamps: true });

module.exports = mongoose.model('UserProgress', userProgressSchema);