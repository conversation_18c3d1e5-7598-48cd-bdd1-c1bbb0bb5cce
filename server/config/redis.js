const Redis = require('ioredis');

let redisClient;

const connectRedis = async () => {
	try {
		const redisConfig = {
			host: process.env.REDIS_HOST || 'localhost',
			port: process.env.REDIS_PORT || 6379,
			password: process.env.REDIS_PASSWORD || null,
			retryDelayOnFailover: 100,
			maxRetriesPerRequest: 3,
			lazyConnect: true,
		};

		// Add Redis URL support for cloud deployments
		if (process.env.REDIS_URL) {
			redisClient = new Redis(process.env.REDIS_URL);
		} else {
			redisClient = new Redis(redisConfig);
		}

		redisClient.on('connect', () => {
			console.log('✅ Redis client connected');
		});

		redisClient.on('ready', () => {
			console.log('✅ Redis client ready to use');
		});

		redisClient.on('error', (err) => {
			console.error('❌ Redis Client Error:', err);
		});

		redisClient.on('end', () => {
			console.log('🔌 Redis client disconnected');
		});

		// Test the connection
		await redisClient.ping();
		console.log('🏓 Redis connection successful');

		return redisClient;
	} catch (error) {
		console.error('❌ Error connecting to Redis:', error);
		throw error;
	}
};

const getRedisClient = () => {
	if (!redisClient) {
		throw new Error('Redis client not initialized. Call connectRedis() first.');
	}
	return redisClient;
};

const closeRedisConnection = async () => {
	if (redisClient) {
		await redisClient.quit();
		console.log('🔌 Redis connection closed');
	}
};

module.exports = {
	connectRedis,
	getRedisClient,
	closeRedisConnection,
};
