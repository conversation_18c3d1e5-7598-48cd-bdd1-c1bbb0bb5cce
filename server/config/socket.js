const { Server } = require('socket.io');

let io;

const initializeSocket = (server) => {
	console.log('🚀 Initializing Socket.IO server...');

	io = new Server(server, {
		cors: {
			origin: process.env.CLIENT_URL || 'http://localhost:3000',
			methods: ['GET', 'POST'],
			credentials: true,
		},
		pingTimeout: 60000,
		pingInterval: 25000,
	});

	console.log('🚀 Socket.IO server initialized');
	return io;
};

const getSocketIO = () => {
	if (!io) {
		throw new Error('Socket.IO not initialized. Call initializeSocket first.');
	}
	return io;
};

module.exports = {
	initializeSocket,
	getSocketIO,
};
