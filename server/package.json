{"name": "server", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "description": "Backend server for You = Life platform", "dependencies": {"@socket.io/redis-adapter": "^8.3.0", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cloudinary": "^2.7.0", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.5.0", "express": "^4.21.2", "express-validator": "^7.2.1", "google-auth-library": "^10.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "mongodb": "^6.17.0", "mongoose": "^7.8.6", "multer": "^2.0.1", "nodemailer": "^7.0.3", "qrcode": "^1.5.4", "redis": "^5.5.6", "socket.io": "^4.8.1", "speakeasy": "^2.0.0", "streamifier": "^0.1.1", "stripe": "^18.0.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "validator": "^13.15.15", "web-push": "^3.6.7"}, "devDependencies": {"nodemon": "^3.1.10"}}