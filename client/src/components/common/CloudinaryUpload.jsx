'use client';
import { useCloudinaryUpload } from '@/src/lib/hooks';
import { useState, useEffect, useCallback } from 'react';
import {
	FaVideo,
	FaImage,
	FaTimes,
	FaSpinner,
	FaCheckCircle,
} from 'react-icons/fa';

const CloudinaryUpload = ({
	type = 'video', // 'video' or 'image'
	folder = 'lesson-videos',
	onUploadSuccess,
	onUploadError,
	onUploadProgress,
	label,
	className = '',
	accept,
	maxFileSize = 2000000000, // 2GB for video, will be adjusted for images
	disabled = false,
	value = null, // Current uploaded file URL
	showPreview = true,
	required = false,
}) => {
	const [uploadProgress, setUploadProgress] = useState(0);
	const [isUploading, setIsUploading] = useState(false);
	const [uploadedFile, setUploadedFile] = useState(value);
	const [error, setError] = useState(null);

	const { openWidget } = useCloudinaryUpload();

	useEffect(() => {
		setUploadedFile(value);
	}, [value]);

	const handleUploadSuccess = useCallback(
		(result) => {
			console.log('Upload successful:', result);
			setUploadedFile(result.secure_url);
			setIsUploading(false);
			setUploadProgress(100);
			setError(null);

			// Call parent callback
			onUploadSuccess?.({
				url: result.secure_url,
				publicId: result.public_id,
				resourceType: result.resource_type,
				format: result.format,
				bytes: result.bytes,
				width: result.width,
				height: result.height,
				duration: result.duration, // for videos
			});
		},
		[onUploadSuccess],
	);

	const handleUploadError = useCallback(
		(error) => {
			console.error('Upload error:', error);
			setIsUploading(false);
			setUploadProgress(0);
			setError(error?.message || 'Upload failed');
			onUploadError?.(error);
		},
		[onUploadError],
	);

	const handleUploadProgress = useCallback(
		(info) => {
			if (info?.progress) {
				setUploadProgress(info.progress);
				onUploadProgress?.(info);
			}
		},
		[onUploadProgress],
	);

	const handleUpload = useCallback(() => {
		if (disabled) return;

		setError(null);
		setIsUploading(true);
		setUploadProgress(0);

		const uploadOptions = {
			folder,
			resourceType: type === 'video' ? 'video' : 'image',
			maxFileSize: type === 'image' ? 10000000 : maxFileSize, // 10MB for images, configurable for videos
			onSuccess: handleUploadSuccess,
			onError: handleUploadError,
			onProgress: handleUploadProgress,
		};

		openWidget(uploadOptions);
	}, [
		disabled,
		folder,
		type,
		maxFileSize,
		handleUploadSuccess,
		handleUploadError,
		handleUploadProgress,
		openWidget,
	]);

	const handleRemove = useCallback(() => {
		setUploadedFile(null);
		setUploadProgress(0);
		setError(null);
		onUploadSuccess?.(null); // Notify parent that file was removed
	}, [onUploadSuccess]);

	const formatFileSize = (bytes) => {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	};

	const isVideo = type === 'video';
	const Icon = isVideo ? FaVideo : FaImage;
	const fileTypeLabel = isVideo ? 'video' : 'image';
	const maxSizeLabel = formatFileSize(isVideo ? maxFileSize : 10000000);

	return (
		<div className={`space-y-4 ${className}`}>
			{label && (
				<label className='block text-sm font-medium text-gray-700 mb-2'>
					{label} {required && <span className='text-red-500'>*</span>}
				</label>
			)}

			{/* Upload Area */}
			{!uploadedFile ? (
				<div
					className={`border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200 ${
						disabled
							? 'border-gray-200 bg-gray-50 cursor-not-allowed'
							: 'border-gray-300 hover:border-gray-400 cursor-pointer bg-white'
					}`}
					onClick={handleUpload}>
					<Icon className='mx-auto h-12 w-12 text-gray-400 mb-4' />
					<div className='space-y-2'>
						<p className='text-sm text-gray-600 mb-2'>
							Click to upload your {fileTypeLabel}
						</p>
						<p className='text-xs text-gray-500'>
							{isVideo
								? `MP4, MOV, AVI up to ${maxSizeLabel}`
								: `PNG, JPG, GIF up to ${maxSizeLabel}`}
						</p>
					</div>
				</div>
			) : (
				/* Preview Area */
				<div className='border border-gray-300 rounded-lg p-4 bg-gray-50'>
					<div className='flex items-center justify-between mb-3'>
						<p className='text-sm font-medium text-gray-700'>
							Uploaded {fileTypeLabel}:
						</p>
						<button
							type='button'
							onClick={handleRemove}
							disabled={disabled}
							className='text-red-500 hover:text-red-700 disabled:opacity-50 disabled:cursor-not-allowed'>
							<FaTimes />
						</button>
					</div>

					{showPreview && (
						<div className='mb-3'>
							{isVideo ? (
								<video
									src={uploadedFile}
									controls
									className='max-w-full h-48 rounded-lg mx-auto'
								/>
							) : (
								<img
									src={uploadedFile}
									alt='Uploaded preview'
									className='max-w-full h-48 object-cover rounded-lg mx-auto'
								/>
							)}
						</div>
					)}

					<p className='text-sm text-gray-600 truncate'>
						<span className='font-medium'>URL:</span> {uploadedFile}
					</p>
				</div>
			)}

			{/* Upload Progress */}
			{isUploading && (
				<div className='space-y-2'>
					<div className='flex items-center justify-between'>
						<span className='text-sm font-medium text-gray-700 flex items-center gap-2'>
							<FaSpinner className='animate-spin' />
							Uploading {fileTypeLabel}...
						</span>
						<span className='text-sm text-gray-500'>{uploadProgress}%</span>
					</div>
					<div className='w-full bg-gray-200 rounded-full h-3'>
						<div
							className='bg-blue-600 h-3 rounded-full transition-all duration-300 ease-out'
							style={{ width: `${uploadProgress}%` }}
						/>
					</div>
				</div>
			)}

			{/* Success Message */}
			{uploadedFile && !isUploading && (
				<div className='flex items-center gap-2 text-green-600 text-sm'>
					<FaCheckCircle />
					<span>
						{fileTypeLabel.charAt(0).toUpperCase() + fileTypeLabel.slice(1)}{' '}
						uploaded successfully!
					</span>
				</div>
			)}

			{/* Error Message */}
			{error && (
				<div className='bg-red-50 border border-red-200 rounded-lg p-3'>
					<p className='text-red-700 text-sm'>{error}</p>
					<button
						onClick={() => setError(null)}
						className='text-red-600 underline text-xs mt-1 hover:text-red-800'>
						Dismiss
					</button>
				</div>
			)}
		</div>
	);
};

export default CloudinaryUpload;
