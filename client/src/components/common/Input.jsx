'use client';
import React, { forwardRef, useState } from 'react';
import { FaEye, FaEyeSlash } from 'react-icons/fa';
import { cn } from '@/src/lib/utils';

const Input = forwardRef(
	(
		{
			label,
			type = 'text',
			placeholder,
			value,
			onChange,
			onBlur,
			required = false,
			disabled = false,
			error,
			helperText,
			leftIcon,
			rightIcon,
			size = 'md',
			className = '',
			...rest
		},
		ref,
	) => {
		const [showPassword, setShowPassword] = useState(false);
		const [isFocused, setIsFocused] = useState(false);

		const inputType = type === 'password' && showPassword ? 'text' : type;

		const baseClasses =
			'w-full border rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 disabled:opacity-50 disabled:cursor-not-allowed';

		const sizeClasses = {
			sm: 'px-3 py-1.5 text-sm',
			md: 'px-4 py-2 text-base',
			lg: 'px-5 py-3 text-lg',
		};

		const stateClasses = error
			? 'border-red-300 focus:border-red-500 focus:ring-red-500'
			: isFocused
			? 'border-blue-500 focus:border-blue-500 focus:ring-blue-500'
			: 'border-gray-300 focus:border-blue-500 focus:ring-blue-500';

		const handleFocus = (e) => {
			setIsFocused(true);
			rest.onFocus?.(e);
		};

		const handleBlur = (e) => {
			setIsFocused(false);
			onBlur?.(e);
		};

		const togglePasswordVisibility = () => {
			setShowPassword(!showPassword);
		};

		return (
			<div className='w-full'>
				{label && (
					<label className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>
						{label}
						{required && <span className='text-red-500 ml-1'>*</span>}
					</label>
				)}

				<div className='relative'>
					{leftIcon && (
						<div className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400'>
							{leftIcon}
						</div>
					)}

					<input
						ref={ref}
						type={inputType}
						value={value}
						onChange={onChange}
						onFocus={handleFocus}
						onBlur={handleBlur}
						placeholder={placeholder}
						required={required}
						disabled={disabled}
						className={cn(
							baseClasses,
							sizeClasses[size],
							stateClasses,
							leftIcon && 'pl-10',
							(rightIcon || type === 'password') && 'pr-10',
							'dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400',
							className,
						)}
						{...rest}
					/>

					{type === 'password' && (
						<button
							type='button'
							className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 focus:outline-none'
							onClick={togglePasswordVisibility}
							tabIndex={-1}>
							{showPassword ? <FaEyeSlash size={16} /> : <FaEye size={16} />}
						</button>
					)}

					{rightIcon && type !== 'password' && (
						<div className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400'>
							{rightIcon}
						</div>
					)}
				</div>

				{(error || helperText) && (
					<div className='mt-1'>
						{error && (
							<p className='text-sm text-red-600 dark:text-red-400'>{error}</p>
						)}
						{!error && helperText && (
							<p className='text-sm text-gray-500 dark:text-gray-400'>
								{helperText}
							</p>
						)}
					</div>
				)}
			</div>
		);
	},
);

Input.displayName = 'Input';

export default Input;
