import { useState } from 'react';
import { FaChevronDown, FaChevronUp } from 'react-icons/fa';
import toast from 'react-hot-toast';
import { valuesAPI } from '@/src/lib/api';

const ValueCard = ({ title, description, valueId, onDelete }) => {
	const [isExpanded, setIsExpanded] = useState(false);
	const [visibleOnProfile, setVisibleOnProfile] = useState(false);
	const [shareOnCommunity, setShareOnCommunity] = useState(false);
	const [isDeleting, setIsDeleting] = useState(false);
	const [showConfirmDialog, setShowConfirmDialog] = useState(false);

	const handleDeleteClick = (e) => {
		e.stopPropagation();
		setShowConfirmDialog(true);
	};

	const handleConfirmDelete = async (e) => {
		e.stopPropagation();

		try {
			setIsDeleting(true);
			await valuesAPI.deleteValue(valueId);

			// Call parent component's onDelete callback to update the UI
			if (onDelete) {
				onDelete(valueId);
			}

			toast('Value deleted successfully');
		} catch (error) {
			console.error('Failed to delete value:', error);
			toast('Failed to delete value. Please try again.', 'error');
		} finally {
			setIsDeleting(false);
			setShowConfirmDialog(false);
		}
	};

	const handleCancelDelete = (e) => {
		e.stopPropagation();
		setShowConfirmDialog(false);
	};

	return (
		<>
			<div className='bg-gray-200 dark:bg-teal-700 p-5 my-2 rounded-lg shadow-md group relative hover:shadow-lg transition-shadow duration-200'>
				{/* Confirmation Dialog Overlay */}
				{showConfirmDialog && (
					<div className='absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center z-30'>
						<div className='bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg max-w-sm'>
							<h4 className='font-semibold mb-2'>Confirm Delete</h4>
							<p className='text-sm text-gray-600 dark:text-gray-300 mb-4'>
								Are you sure you want to delete this value? This action cannot
								be undone.
							</p>
							<div className='flex gap-2 justify-end'>
								<button
									onClick={handleCancelDelete}
									className='px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 rounded hover:bg-gray-300 dark:hover:bg-gray-500'>
									Cancel
								</button>
								<button
									onClick={handleConfirmDelete}
									disabled={isDeleting}
									className='px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50'>
									{isDeleting ? 'Deleting...' : 'Delete'}
								</button>
							</div>
						</div>
					</div>
				)}

				<div
					className='flex justify-between items-center cursor-pointer'
					onClick={() => setIsExpanded(!isExpanded)}>
					<h3 className='font-bold text-xl'>{title}</h3>
					<div className='flex items-center justify-between gap-2'>
						{/* Delete Value Button */}
						<button
							onClick={handleDeleteClick}
							disabled={isDeleting}
							className='flex-shrink-0 w-8 h-8 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-full flex items-center justify-center transition-all duration-200 opacity-100 md:opacity-0 md:group-hover:opacity-100 z-20 disabled:opacity-50'
							title='Delete value'>
							{isDeleting ? (
								// Loading spinner
								<svg
									className='w-4 h-4 animate-spin'
									fill='none'
									viewBox='0 0 24 24'>
									<circle
										className='opacity-25'
										cx='12'
										cy='12'
										r='10'
										stroke='currentColor'
										strokeWidth='4'></circle>
									<path
										className='opacity-75'
										fill='currentColor'
										d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'></path>
								</svg>
							) : (
								<svg
									className='w-4 h-4'
									fill='none'
									stroke='currentColor'
									viewBox='0 0 24 24'>
									<path
										strokeLinecap='round'
										strokeLinejoin='round'
										strokeWidth={2}
										d='M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16'
									/>
								</svg>
							)}
						</button>
						{isExpanded ? <FaChevronUp /> : <FaChevronDown />}
					</div>
				</div>
				{isExpanded && <hr className=' text-gray-900 my-3' />}
				{isExpanded && <p className='mt-3 text-normal'>{description}</p>}
				{isExpanded && (
					<div className='text-sm mt-2 flex flex-col gap-2'>
						<label className='underline flex items-center gap-2 cursor-pointer'>
							Make visible on profile
							<input
								type='checkbox'
								checked={visibleOnProfile}
								onChange={() => setVisibleOnProfile(!visibleOnProfile)}
								className='form-checkbox accent-teal-500'
							/>
						</label>
						<label className='underline flex items-center gap-2 cursor-pointer'>
							Share on community
							<input
								type='checkbox'
								checked={shareOnCommunity}
								onChange={() => setShareOnCommunity(!shareOnCommunity)}
								className='form-checkbox accent-teal-500'
							/>
						</label>
					</div>
				)}
			</div>
		</>
	);
};

export default ValueCard;
