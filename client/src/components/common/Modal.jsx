'use client';
import React, { useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { FaTimes } from 'react-icons/fa';
import { cn } from '@/src/lib/utils';

const Modal = ({
	isOpen,
	onClose,
	title,
	children,
	size = 'md',
	closeOnBackdrop = true,
	closeOnEscape = true,
	showCloseButton = true,
	className = '',
	footer,
}) => {
	const modalRef = useRef(null);
	const previousFocusRef = useRef(null);

	const sizeClasses = {
		sm: 'max-w-sm',
		md: 'max-w-md',
		lg: 'max-w-lg',
		xl: 'max-w-xl',
		full: 'max-w-full mx-4',
	};

	useEffect(() => {
		if (isOpen) {
			// Store the currently focused element
			previousFocusRef.current = document.activeElement;

			// Focus the modal
			modalRef.current?.focus();

			// Prevent body scroll
			document.body.style.overflow = 'hidden';
		} else {
			// Restore body scroll
			document.body.style.overflow = 'unset';

			// Restore focus to previously focused element
			previousFocusRef.current?.focus();
		}

		return () => {
			document.body.style.overflow = 'unset';
		};
	}, [isOpen]);

	useEffect(() => {
		const handleEscape = (e) => {
			if (closeOnEscape && e.key === 'Escape') {
				onClose();
			}
		};

		if (isOpen) {
			document.addEventListener('keydown', handleEscape);
		}

		return () => {
			document.removeEventListener('keydown', handleEscape);
		};
	}, [isOpen, closeOnEscape, onClose]);

	const handleBackdropClick = (e) => {
		if (closeOnBackdrop && e.target === e.currentTarget) {
			onClose();
		}
	};

	const handleClose = () => {
		onClose();
	};

	if (!isOpen) return null;

	const modalContent = (
		<div
			className='fixed inset-0 z-50 flex items-center justify-center p-4'
			onClick={handleBackdropClick}>
			{/* Backdrop */}
			<div className='fixed inset-0 bg-black bg-opacity-50 transition-opacity' />

			{/* Modal */}
			<div
				ref={modalRef}
				className={cn(
					'relative bg-white dark:bg-gray-800 rounded-lg shadow-xl transform transition-all w-full',
					sizeClasses[size],
					className,
				)}
				role='dialog'
				aria-modal='true'
				aria-labelledby={title ? 'modal-title' : undefined}
				tabIndex={-1}>
				{/* Header */}
				{(title || showCloseButton) && (
					<div className='flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700'>
						{title && (
							<h2
								id='modal-title'
								className='text-xl font-semibold text-gray-900 dark:text-white'>
								{title}
							</h2>
						)}
						{showCloseButton && (
							<button
								type='button'
								className='text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors'
								onClick={handleClose}
								aria-label='Close modal'>
								<FaTimes size={20} />
							</button>
						)}
					</div>
				)}

				{/* Content */}
				<div className='p-6'>{children}</div>

				{/* Footer */}
				{footer && (
					<div className='flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700'>
						{footer}
					</div>
				)}
			</div>
		</div>
	);

	// Render modal in portal
	return createPortal(modalContent, document.body);
};

export default Modal;
