/**
 * Lazy Loading Wrapper Component
 * Provides lazy loading functionality with loading states and error boundaries
 */
import React, { Suspense } from 'react';
import Loading from './Loading';

/**
 * Error boundary for lazy loaded components
 */
class LazyErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Lazy loading error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex flex-col items-center justify-center p-8 text-center">
          <div className="text-red-500 mb-4">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Failed to load component
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Something went wrong while loading this section.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Reload Page
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Lazy wrapper component with loading states
 * @param {Object} props - Component props
 * @param {React.ComponentType} props.component - Lazy loaded component
 * @param {React.ReactNode} props.fallback - Custom loading fallback
 * @param {string} props.loadingMessage - Loading message
 * @param {Object} props.componentProps - Props to pass to the lazy component
 * @returns {React.ReactElement} Lazy wrapper component
 */
const LazyWrapper = ({ 
  component: Component, 
  fallback, 
  loadingMessage = 'Loading...', 
  componentProps = {} 
}) => {
  const defaultFallback = (
    <div className="flex items-center justify-center p-8">
      <Loading.Inline message={loadingMessage} />
    </div>
  );

  return (
    <LazyErrorBoundary>
      <Suspense fallback={fallback || defaultFallback}>
        <Component {...componentProps} />
      </Suspense>
    </LazyErrorBoundary>
  );
};

/**
 * Higher-order component for creating lazy loaded components
 * @param {Function} importFn - Dynamic import function
 * @param {Object} options - Lazy loading options
 * @param {React.ReactNode} options.fallback - Custom loading fallback
 * @param {string} options.loadingMessage - Loading message
 * @returns {React.ComponentType} Lazy loaded component
 */
export const withLazyLoading = (importFn, options = {}) => {
  const LazyComponent = React.lazy(importFn);
  
  return (props) => (
    <LazyWrapper
      component={LazyComponent}
      fallback={options.fallback}
      loadingMessage={options.loadingMessage}
      componentProps={props}
    />
  );
};

/**
 * Hook for creating lazy loaded components
 * @param {Function} importFn - Dynamic import function
 * @param {Object} options - Lazy loading options
 * @returns {React.ComponentType} Lazy loaded component
 */
export const useLazyComponent = (importFn, options = {}) => {
  return React.useMemo(() => withLazyLoading(importFn, options), [importFn, options]);
};

export default LazyWrapper;
