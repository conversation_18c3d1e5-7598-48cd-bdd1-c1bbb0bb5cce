import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { siteName } from '@/utils/variables';
import { logo } from '@/assets/images';
import { happy } from '@/assets/svgs';

const Onboard = () => {
	return (
		<div className='flex flex-col justify-center gap-3 items-center h-screen'>
			<div className='mb-4'>
				<div className='bg-white dark:bg-secondary p-6 rounded-sm'>
					<div className='w-full max-w-sm mx-auto'>
						<Image
							src={logo}
							alt={siteName}
							width={220}
							height={220}
							priority={true}
							className='object-contain w-full h-auto'
						/>
					</div>
				</div>
			</div>

			<h1 className='text-foreground-dark dark:text-foreground-dark text-2xl font-medium mb-6 text-center'>
				Change the Quality of your Thinking
			</h1>

			<div className='mb-6'>
				{/* Illustration of the happy person */}
				<div className='w-80 h-80 relative'>
					<Image
						src={happy}
						alt={siteName}
						fill
						priority={true}
						className='object-contain'
					/>
				</div>
			</div>

			<div className='flex gap-4 flex-col w-full max-w-xs px-4'>
				<Link
					href='auth/login'
					className='w-full'>
					<button className='w-full bg-white dark:bg-teal-600 border-2 border-black dark:border-transparent text-black dark:text-white py-4 px-8 rounded text-xl font-semibold'>
						Sign In
					</button>
				</Link>

				<Link
					href='auth/signup'
					className='w-full'>
					<button className='w-full bg-black dark:bg-white text-white dark:text-black py-4 px-8 rounded text-xl font-semibold'>
						Sign Up
					</button>
				</Link>
			</div>
		</div>
	);
};

export default Onboard;
