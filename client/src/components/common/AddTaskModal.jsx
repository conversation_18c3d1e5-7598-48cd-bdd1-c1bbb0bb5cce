'use client';
import React, { useState } from 'react';

const AddTaskModal = ({ onSave, onDiscard, taskType = null }) => {
	const [taskText, setTaskText] = useState('');
	const [selectedCategory, setSelectedCategory] = useState(taskType || 'amber');

	const handleSave = () => {
		if (!taskText.trim()) return;

		const taskData = {
			category: selectedCategory,
			text: taskText.trim(),
		};

		onSave(taskData); // send as one payload
		setTaskText('');
	};

	const handleDiscard = () => {
		setTaskText('');
		onDiscard();
	};

	const categories = [
		{
			value: 'red',
			label: 'High Priority',
			color: 'bg-red-500',
			borderColor: 'border-red-500',
			textColor: 'text-red-500',
			bgLight: 'bg-red-50',
			icon: '🔴',
		},
		{
			value: 'amber',
			label: 'Medium Priority',
			color: 'bg-amber-500',
			borderColor: 'border-amber-500',
			textColor: 'text-amber-500',
			bgLight: 'bg-amber-50',
			icon: '🟡',
		},
		// Green section removed - tasks automatically move here when completed
	];

	return (
		<div className='fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50 p-4'>
			<div className='bg-white dark:bg-slate-800 rounded-3xl shadow-2xl w-full max-w-lg mx-4 transform transition-all duration-300 scale-100'>
				{/* Header */}
				<div className='bg-gradient-to-r from-teal-400 to-teal-600 rounded-t-3xl p-6 text-center'>
					<h2 className='text-white text-2xl font-bold mb-2'>Add New Task</h2>
					<p className='text-blue-100 text-sm'>
						Create a task and assign its priority level
					</p>
				</div>

				{/* Content */}
				<div className='p-6 space-y-6'>
					{/* Task Input */}
					<div className='space-y-3'>
						<label className='text-gray-700 dark:text-gray-300 text-lg font-semibold block'>
							Task Description
						</label>
						<div className='relative'>
							<input
								type='text'
								value={taskText}
								onChange={(e) => setTaskText(e.target.value)}
								placeholder='Enter your task description...'
								className='w-full p-4 rounded-xl border-2 border-gray-200 dark:border-gray-600 dark:bg-slate-700 dark:text-white text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:outline-none transition-colors text-lg'
								autoFocus
							/>
							<div className='absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400'>
								<svg
									className='w-5 h-5'
									fill='none'
									stroke='currentColor'
									viewBox='0 0 24 24'>
									<path
										strokeLinecap='round'
										strokeLinejoin='round'
										strokeWidth={2}
										d='M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z'
									/>
								</svg>
							</div>
						</div>
					</div>

					{/* Category Selection */}
					<div className='space-y-4'>
						<label className='text-gray-700 dark:text-gray-300 text-lg font-semibold block'>
							Priority Level
						</label>

						<div className='grid grid-cols-1 gap-3'>
							{categories.map((category) => (
								<label
									key={category.value}
									className={`relative flex items-center p-2 rounded-xl border-2 cursor-pointer transition-all duration-200 hover:shadow-md ${
										selectedCategory === category.value
											? `${category.borderColor} ${category.bgLight} dark:bg-slate-700 dark:border-opacity-50`
											: 'border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-slate-700 hover:border-gray-300'
									}`}>
									<input
										type='radio'
										name='category'
										value={category.value}
										checked={selectedCategory === category.value}
										onChange={() => setSelectedCategory(category.value)}
										className='sr-only'
									/>

									{/* Custom Radio Button */}
									<div
										className={`w-5 h-5 rounded-full border-2 mr-4 flex items-center justify-center ${
											selectedCategory === category.value
												? `${category.borderColor} ${category.color}`
												: 'border-gray-300 dark:border-gray-500'
										}`}>
										{selectedCategory === category.value && (
											<div className='w-2 h-2 bg-white rounded-full'></div>
										)}
									</div>

									{/* Icon and Label */}
									<div className='flex items-center gap-3 flex-1'>
										<span className='text-2xl'>{category.icon}</span>
										<div>
											<div
												className={`font-semibold ${
													selectedCategory === category.value
														? category.textColor
														: 'text-gray-700 dark:text-gray-300'
												}`}>
												{category.label}
											</div>
												<div className='text-sm text-gray-500 dark:text-gray-400'>
													{category.value === 'red' &&
														'Urgent and important tasks'}
													{category.value === 'amber' &&
														'Important but not urgent'}
											</div>
										</div>
									</div>

									{/* Selected Indicator */}
									{selectedCategory === category.value && (
										<div
											className={`w-6 h-6 rounded-full ${category.color} flex items-center justify-center`}>
											<svg
												className='w-3 h-3 text-white'
												fill='currentColor'
												viewBox='0 0 24 24'>
												<path d='M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z' />
											</svg>
										</div>
									)}
								</label>
							))}
						</div>
					</div>
				</div>

				{/* Footer */}
				<div className='bg-gray-50 dark:bg-slate-700 rounded-b-3xl p-6'>
					<div className='flex gap-4'>
						<button
							onClick={handleDiscard}
							className='flex-1 bg-white dark:bg-slate-600 text-gray-700 dark:text-gray-200 py-3 px-6 rounded-xl text-lg font-semibold border-2 border-gray-200 dark:border-gray-500 hover:bg-gray-100 dark:hover:bg-slate-500 transition-colors'>
							Cancel
						</button>

						<button
							onClick={handleSave}
							disabled={!taskText.trim()}
							className='flex-1 bg-gradient-to-r from-teal-600 to-teal-600 text-white py-3 px-6 rounded-xl text-lg font-semibold hover:from-teal-700 hover:to-teal-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:from-teal-600 disabled:hover:to-teal-600 flex items-center justify-center gap-2'>
							<svg
								className='w-5 h-5'
								fill='none'
								stroke='currentColor'
								viewBox='0 0 24 24'>
								<path
									strokeLinecap='round'
									strokeLinejoin='round'
									strokeWidth={2}
									d='M5 13l4 4L19 7'
								/>
							</svg>
							Save Task
						</button>
					</div>
				</div>
			</div>
		</div>
	);
};

export default AddTaskModal;
