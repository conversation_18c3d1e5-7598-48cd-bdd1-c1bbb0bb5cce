/**
 * Reusable Loading Component
 * Various loading indicators for different use cases
 */
import React from 'react';
import { cn } from '@/src/lib/utils';

/**
 * Spinner loading component
 * @param {Object} props - Component props
 * @param {'sm'|'md'|'lg'} props.size - Spinner size
 * @param {string} props.className - Additional CSS classes
 * @returns {React.ReactElement} Spinner component
 */
export const Spinner = ({ size = 'md', className = '' }) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
  };

  return (
    <div
      className={cn(
        'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',
        sizeClasses[size],
        className
      )}
    />
  );
};

/**
 * Dots loading component
 * @param {Object} props - Component props
 * @param {'sm'|'md'|'lg'} props.size - Dots size
 * @param {string} props.className - Additional CSS classes
 * @returns {React.ReactElement} Dots component
 */
export const Dots = ({ size = 'md', className = '' }) => {
  const sizeClasses = {
    sm: 'h-1 w-1',
    md: 'h-2 w-2',
    lg: 'h-3 w-3',
  };

  return (
    <div className={cn('flex space-x-1', className)}>
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className={cn(
            'bg-blue-600 rounded-full animate-pulse',
            sizeClasses[size]
          )}
          style={{
            animationDelay: `${i * 0.2}s`,
            animationDuration: '1s',
          }}
        />
      ))}
    </div>
  );
};

/**
 * Skeleton loading component
 * @param {Object} props - Component props
 * @param {string} props.className - Additional CSS classes
 * @param {number} props.lines - Number of skeleton lines
 * @returns {React.ReactElement} Skeleton component
 */
export const Skeleton = ({ className = '', lines = 1 }) => {
  return (
    <div className={cn('animate-pulse', className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <div
          key={i}
          className={cn(
            'bg-gray-300 dark:bg-gray-600 rounded',
            i === 0 ? 'h-4' : 'h-3 mt-2',
            i === lines - 1 && lines > 1 ? 'w-3/4' : 'w-full'
          )}
        />
      ))}
    </div>
  );
};

/**
 * Full page loading component
 * @param {Object} props - Component props
 * @param {string} props.message - Loading message
 * @param {boolean} props.overlay - Show overlay background
 * @returns {React.ReactElement} Loading page component
 */
export const LoadingPage = ({ message = 'Loading...', overlay = true }) => {
  return (
    <div
      className={cn(
        'flex flex-col items-center justify-center min-h-screen',
        overlay && 'fixed inset-0 bg-white dark:bg-gray-900 z-50'
      )}
    >
      <Spinner size="lg" />
      <p className="mt-4 text-gray-600 dark:text-gray-400">{message}</p>
    </div>
  );
};

/**
 * Inline loading component
 * @param {Object} props - Component props
 * @param {string} props.message - Loading message
 * @param {'spinner'|'dots'} props.type - Loading type
 * @param {'sm'|'md'|'lg'} props.size - Loading size
 * @param {string} props.className - Additional CSS classes
 * @returns {React.ReactElement} Inline loading component
 */
export const LoadingInline = ({ 
  message = 'Loading...', 
  type = 'spinner', 
  size = 'md',
  className = '' 
}) => {
  const LoadingComponent = type === 'dots' ? Dots : Spinner;

  return (
    <div className={cn('flex items-center space-x-2', className)}>
      <LoadingComponent size={size} />
      <span className="text-gray-600 dark:text-gray-400">{message}</span>
    </div>
  );
};

/**
 * Button loading component
 * @param {Object} props - Component props
 * @param {string} props.message - Loading message
 * @returns {React.ReactElement} Button loading component
 */
export const LoadingButton = ({ message = 'Loading...' }) => {
  return (
    <div className="flex items-center justify-center space-x-2">
      <Spinner size="sm" />
      <span>{message}</span>
    </div>
  );
};

/**
 * Card loading component
 * @param {Object} props - Component props
 * @param {string} props.className - Additional CSS classes
 * @returns {React.ReactElement} Card loading component
 */
export const LoadingCard = ({ className = '' }) => {
  return (
    <div className={cn('p-6 bg-white dark:bg-gray-800 rounded-lg shadow', className)}>
      <div className="animate-pulse">
        <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mb-4" />
        <div className="space-y-2">
          <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded" />
          <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-5/6" />
          <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-4/6" />
        </div>
      </div>
    </div>
  );
};

// Default export
const Loading = {
  Spinner,
  Dots,
  Skeleton,
  Page: LoadingPage,
  Inline: LoadingInline,
  Button: LoadingButton,
  Card: LoadingCard,
};

export default Loading;
