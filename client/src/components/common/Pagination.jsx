'use client';
import React from 'react';

const Pagination = ({ currentPage, totalPages, onPageChange }) => {
	return (
		<div className='flex justify-between items-center text-sm text-gray-700 mt-4'>
			<p>
				Pg {currentPage} of {totalPages}
			</p>
			<div className='space-x-2'>
				<button
					disabled={currentPage === 1}
					onClick={() => onPageChange(currentPage - 1)}
					className='text-blue-600 hover:underline disabled:text-gray-400'>
					Previous
				</button>
				<button
					disabled={currentPage === totalPages}
					onClick={() => onPageChange(currentPage + 1)}
					className='text-blue-600 hover:underline disabled:text-gray-400'>
					Next
				</button>
			</div>
		</div>
	);
};

export default Pagination;
