import { book, city, grad, scope } from '@/assets/svgs';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

const List = () => {
	const userList = [
		{
			id: 1,
			topic: 'YOU LIFE ACADEMY',
			logo: grad,
			link: '/lessons',
		},
		{
			id: 2,
			topic: 'SELF AWARENESS LOG',
			logo: book,
			link: '/reflection',
		},
		{
			id: 3,
			topic: 'LIFE MISSION',
			link: '/life-mission',
			logo: scope,
		},
		{
			id: 4,
			topic: 'COMMUNITY',
			logo: city,
		},
	];
	return (
		<div className='px-4'>
			{userList.map((user) => (
				<Link
					href={user.link || '#'}
					key={user.id}
					className='flex items-center bg-gray-200 dark:bg-white rounded-2xl px-6 py-4 mb-6 shadow-md'>
					<div className='w-20 h-20 flex items-center justify-center'>
						<Image
							width={80}
							height={80}
							src={user.logo}
							alt={user.topic}
							className='object-contain'
						/>
					</div>
					<p className='ml-6 text-center text-2xl font-bold text-black'>
						{user.topic}
					</p>
				</Link>
			))}
		</div>
	);
};

export default List;
