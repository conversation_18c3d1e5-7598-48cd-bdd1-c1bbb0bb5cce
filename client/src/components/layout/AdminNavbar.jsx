import Link from 'next/link';
import { CustomImage } from '../common';
import { logo } from '@/assets/images';

const AdminNavbar = () => {
	const title = 'Overview';
	return (
		<div className='flex items-center justify-between px-14 text-black'>
			<div className='flex items-center justify-center gap-10'>
				<Link href='/'>
					<CustomImage
						src={logo}
						width={100}
						alt={title}
						height={100}
						className='cursor-pointer w-20 h-20'
					/>
				</Link>
				<div>
					<h3 className='text-4xl font-semibold'>{title}</h3>
				</div>
			</div>
			<div className='flex items-center justify-center gap-7 text-xl'>
				<Link href='/'>Home</Link>
				<Link href='/'>Support</Link>
				<Link href='/'>My Account</Link>
			</div>
		</div>
	);
};

export default AdminNavbar;
