import React from 'react';
import SidebarMenuItem from './SidebarMenuItem';

// Create a SubMenuItem component for dropdown items
const SubMenuItem = ({ icon, label, href }) => (
	<li>
		<a
			href={href}
			className='flex items-center p-3 hover:bg-teal-700 rounded-lg'>
			{icon}
			<span className='ml-3'>{label}</span>
		</a>
	</li>
);

const SidebarNav = ({ navLinks, pathname }) => {
	return (
		<nav className='mt-4 px-4'>
			<ul className='space-y-2'>
				{navLinks.map((link, index) => {
					const active = pathname === link.href;
					return (
						<SidebarMenuItem
							key={index}
							icon={link.icon}
							label={link.label}
							href={link.href}
							isActive={active}
							hasDropdown={link.hasDropdown}>
							{link.hasDropdown &&
								link.subItems?.map((subItem, subIndex) => (
									<SubMenuItem
										key={subIndex}
										icon={subItem.icon}
										label={subItem.label}
										href={subItem.href}
									/>
								))}
						</SidebarMenuItem>
					);
				})}
			</ul>
		</nav>
	);
};

export default SidebarNav;
