import React from 'react';

class AuthErrorBoundary extends React.Component {
	constructor(props) {
		super(props);
		this.state = { hasError: false, error: null };
	}

	static getDerivedStateFromError(error) {
		// Update state so the next render will show the fallback UI
		return { hasError: true, error };
	}

	componentDidCatch(error, errorInfo) {
		console.error('Authentication error:', error, errorInfo);

		// If it's an authentication error, clear tokens and redirect
		if (error?.response?.status === 401 || error?.message?.includes('401')) {
			localStorage.removeItem('accessToken');
			localStorage.removeItem('refreshToken');
			window.dispatchEvent(new Event('auth-change'));
		}
	}

	render() {
		if (this.state.hasError) {
			return (
				<div className='min-h-screen flex items-center justify-center bg-gray-50'>
					<div className='max-w-md w-full bg-white shadow-lg rounded-lg p-6 text-center'>
						<div className='mb-4'>
							<div className='mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center'>
								<svg
									className='w-8 h-8 text-red-600'
									fill='none'
									stroke='currentColor'
									viewBox='0 0 24 24'>
									<path
										strokeLinecap='round'
										strokeLinejoin='round'
										strokeWidth={2}
										d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z'
									/>
								</svg>
							</div>
						</div>
						<h3 className='text-lg font-medium text-gray-900 mb-2'>
							Something went wrong
						</h3>
						<p className='text-gray-600 mb-4'>
							We encountered an error while loading your data. Please try
							refreshing the page.
						</p>
						<button
							onClick={() => window.location.reload()}
							className='bg-teal-600 text-white px-4 py-2 rounded-md hover:bg-teal-700 transition-colors'>
							Refresh Page
						</button>
					</div>
				</div>
			);
		}

		return this.props.children;
	}
}

export default AuthErrorBoundary;
