'use client';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { isAuthenticated, useUserProfile } from '@/src/lib/hooks';

const ProtectedRoute = ({ children, requiredRoles = [] }) => {
	const router = useRouter();
	const [isChecking, setIsChecking] = useState(true);
	const { data: userProfile, isLoading: userLoading, error } = useUserProfile();
	// console.log(userProfile?.user?.roles);

	useEffect(() => {
		// Check authentication first
		if (!isAuthenticated()) {
			router.push('/auth/login');
			return;
		}

		// If there's an error (like 401), the interceptor will handle it
		if (error?.response?.status === 401) {
			return; // Let the interceptor handle the redirect
		}

		// If no specific roles required, just check authentication
		if (requiredRoles.length === 0) {
			setIsChecking(false);
			return;
		}

		// If roles are required, check user profile
		if (!userLoading && userProfile) {
			const userRoles =
				userProfile?.user?.roles || userProfile?.data?.roles || [];
			const hasRequiredRole = requiredRoles.some((role) =>
				userRoles.includes(role),
			);

			if (!hasRequiredRole) {
				router.push('/lessons'); // Redirect to lessons page if no permission
				return;
			}

			setIsChecking(false);
		}
	}, [router, userProfile, userLoading, requiredRoles, error]);

	// Show loading while checking
	if (isChecking || userLoading) {
		return (
			<div className='flex items-center justify-center min-h-screen'>
				<div className='animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900'></div>
			</div>
		);
	}

	return children;
};

export default ProtectedRoute;
