import React, { useState } from 'react';

const SidebarMenuItem = ({
	icon,
	label,
	href,
	isActive,
	hasDropdown,
	children,
}) => {
	const [isOpen, setIsOpen] = useState(false);

	return (
		<li>
			<div className='relative'>
				<a
					href={href}
					className={`flex items-center p-3 hover:bg-teal-700 rounded-lg ${
						isActive ? 'bg-teal-700' : ''
					}`}
					onClick={
						hasDropdown
							? (e) => {
									e.preventDefault();
									setIsOpen(!isOpen);
							  }
							: null
					}>
					{icon}
					<span className='ml-3'>{label}</span>
					{hasDropdown && <span className='ml-auto'>{isOpen ? '▼' : '►'}</span>}
				</a>
				{hasDropdown && isOpen && (
					<ul className='ml-4 mt-1 space-y-1'>{children}</ul>
				)}
			</div>
		</li>
	);
};

export default SidebarMenuItem;
