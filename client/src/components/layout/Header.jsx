import React from 'react';
import { FaBars, FaBell, FaEnvelope } from 'react-icons/fa';
import { CustomImage, SearchBar } from '../common';

const Header = ({ onMenuToggle, userProfile, isUserLoading }) => {
	return (
		<>
			{/* 🟢 Always Fixed Header */}
			<div
				className={`fixed top-0 left-0 right-0 z-20 px-6 py-4 h-20 flex backdrop-blur-xl justify-between items-center transition-all duration-300 shadow-sm`}>
				<div className='flex items-center space-x-4'>
					<button
						onClick={onMenuToggle}
						className='dark:text-white text-gray-700 hover:text-teal-600 dark:hover:text-teal-400 transition-colors duration-200 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800'>
						<FaBars className='w-6 h-6 text-gray-800 dark:text-white' />
					</button>
				</div>
				<div className='flex items-center space-x-3'>
					{/* Notification Icons - Hidden on mobile, visible on desktop */}
					<div className='hidden md:flex items-center space-x-2'>
						<button className='p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:text-teal-600 dark:hover:text-teal-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200 relative'>
							<FaBell className='w-5 h-5' />
							<span className='absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full'></span>
						</button>
						<button className='p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:text-teal-600 dark:hover:text-teal-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200'>
							<FaEnvelope className='w-5 h-5' />
						</button>
					</div>
				</div>
			</div>

			<header
				className=''
				style={{ paddingTop: '5rem' }}>
				{/* 📱 Mobile Header */}
				<div className='lg:hidden px-6 py-8'>
					<div className='flex justify-between items-start'>
						<div className='flex-1'>
							<div className='mb-6'>
								{isUserLoading ? (
									<div className='space-y-3'>
										<div className='h-8 bg-gray-200 dark:bg-gray-700 rounded-lg w-48 animate-pulse'></div>
										<div className='h-5 bg-gray-200 dark:bg-gray-700 rounded-lg w-32 animate-pulse'></div>
									</div>
								) : (
									<div>
										<h1 className='text-3xl font-bold text-gray-900 dark:text-white mb-2'>
											Hey, {userProfile?.user?.name?.split(' ')[0] || 'there'}!
											👋
										</h1>
										<div className='space-y-1'>
											<p className='text-lg text-gray-700 dark:text-gray-300 font-medium'>
												Let's Start Learning!
											</p>
											<p className='text-base text-gray-600 dark:text-gray-400'>
												What would you like to focus on today?
											</p>
										</div>
									</div>
								)}
							</div>
						</div>

						<div className='ml-4 flex-shrink-0'>
							<div className='relative'>
								{isUserLoading ? (
									<div className='w-20 h-20 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse' />
								) : (
									<div className='w-20 h-20 rounded-full overflow-hidden shadow-lg ring-4 ring-white dark:ring-gray-800'>
										<CustomImage
											src={
												userProfile?.user?.avatar ||
												'/assets/images/placeholder.png'
											}
											alt={userProfile?.user?.name || 'Profile'}
											width={80}
											height={80}
											className='w-full h-full object-cover'
										/>
									</div>
								)}
								<div className='absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white dark:border-gray-800'></div>
							</div>
						</div>
					</div>

					<div className='mt-6'>
						<SearchBar />
					</div>
				</div>

				{/* 💻 Desktop Header */}
				<div className='hidden lg:block px-8 py-12'>
					<div className='max-w-7xl mx-auto'>
						<div className='text-center mb-8'>
							{isUserLoading ? (
								<div className='flex flex-col items-center space-y-4'>
									<div className='h-12 bg-gray-200 dark:bg-gray-700 rounded-lg w-96 animate-pulse'></div>
									<div className='h-6 bg-gray-200 dark:bg-gray-700 rounded-lg w-48 animate-pulse'></div>
									<div className='h-6 bg-gray-200 dark:bg-gray-700 rounded-lg w-80 animate-pulse'></div>
								</div>
							) : (
								<div>
									<h1 className='text-5xl font-bold text-gray-900 dark:text-white mb-4'>
										Hey, {userProfile?.user?.name?.split(' ')[0] || 'there'}! 👋
									</h1>
									<div className='space-y-2'>
										<p className='text-2xl text-gray-700 dark:text-gray-300 font-semibold'>
											Let's Start Learning!
										</p>
										<p className='text-xl text-gray-600 dark:text-gray-400'>
											What would you like to focus on today?
										</p>
									</div>
								</div>
							)}
						</div>

						<div className='flex justify-center'>
							<div className='w-full max-w-2xl'>
								<SearchBar />
							</div>
						</div>

						{/* Profile section for desktop - positioned in corner */}
						<div className='absolute top-24 right-8'>
							<div className='relative'>
								{isUserLoading ? (
									<div className='w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse' />
								) : (
									<div className='w-16 h-16 rounded-full overflow-hidden shadow-lg ring-4 ring-white dark:ring-gray-800 hover:ring-teal-200 dark:hover:ring-teal-800 transition-all duration-200 cursor-pointer'>
										<CustomImage
											src={
												userProfile?.user?.avatar ||
												'/assets/images/placeholder.png'
											}
											alt={userProfile?.user?.name || 'Profile'}
											width={64}
											height={64}
											className='w-full h-full object-cover hover:scale-110 transition-transform duration-200'
										/>
									</div>
								)}
								<div className='absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white dark:border-gray-800'></div>
							</div>
						</div>
					</div>
				</div>
			</header>
		</>
	);
};

export default Header;
