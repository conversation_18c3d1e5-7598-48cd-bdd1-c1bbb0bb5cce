'use client';
import React from 'react';

const AddGoalModal = ({ newGoal, onChange, onSave, onDiscard }) => {
	return (
		<div className='fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4'>
			<div className='bg-white dark:bg-slate-800 rounded-3xl w-full max-w-md mx-auto shadow-2xl border border-gray-100 dark:border-slate-700 transform animate-in fade-in-0 zoom-in-95 duration-300'>
				{/* Header */}
				<div className='flex items-center justify-between p-6 border-b border-gray-100 dark:border-slate-700'>
					<h2 className='text-2xl font-bold text-gray-900 dark:text-white'>
						Create New Goal
					</h2>
					<button
						onClick={onDiscard}
						className='w-8 h-8 rounded-full bg-gray-100 dark:bg-slate-700 hover:bg-gray-200 dark:hover:bg-slate-600 flex items-center justify-center transition-colors'>
						<svg
							className='w-4 h-4 text-gray-500 dark:text-gray-400'
							fill='none'
							stroke='currentColor'
							viewBox='0 0 24 24'>
							<path
								strokeLinecap='round'
								strokeLinejoin='round'
								strokeWidth={2}
								d='M6 18L18 6M6 6l12 12'
							/>
						</svg>
					</button>
				</div>

				{/* Form Content */}
				<div className='p-6 space-y-6'>
					{/* Goal Title */}
					<div className='space-y-2'>
						<label className='block text-sm font-semibold text-gray-700 dark:text-gray-300'>
							Goal Title *
						</label>
						<input
							name='title'
							type='text'
							value={newGoal.title}
							onChange={onChange}
							placeholder='What do you want to achieve?'
							className='w-full px-4 py-3 bg-gray-50 dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white transition-all duration-200'
							autoFocus
						/>
					</div>

					{/* Goal Description */}
					<div className='space-y-2'>
						<label className='block text-sm font-semibold text-gray-700 dark:text-gray-300'>
							Description (Optional)
						</label>
						<textarea
							name='description'
							value={newGoal.description}
							onChange={onChange}
							placeholder='Add more details about your goal...'
							className='w-full px-4 py-3 bg-gray-50 dark:bg-slate-700 border border-gray-200 dark:border-slate-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white transition-all duration-200 resize-none'
							rows='4'
						/>
					</div>

					{/* Motivational Message */}
					<div className='bg-blue-50 dark:bg-blue-900/20 rounded-2xl p-4 border border-blue-200 dark:border-blue-800'>
						<div className='flex items-center gap-2 text-blue-700 dark:text-blue-300'>
							<svg
								className='w-5 h-5'
								fill='currentColor'
								viewBox='0 0 24 24'>
								<path d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z' />
							</svg>
							<span className='text-sm font-medium'>
								Every great achievement starts with a single step!
							</span>
						</div>
					</div>
				</div>

				{/* Footer Actions */}
				<div className='flex gap-3 p-6 border-t border-gray-100 dark:border-slate-700'>
					<button
						onClick={onDiscard}
						className='flex-1 px-4 py-3 bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300 rounded-xl font-semibold hover:bg-gray-200 dark:hover:bg-slate-600 transition-colors'>
						Cancel
					</button>
					<button
						onClick={onSave}
						disabled={!newGoal.title.trim()}
						className='flex-1 px-4 py-3 bg-gradient-to-r from-teal-600 to-teal-600 text-white rounded-xl font-semibold hover:from-blue-teal hover:to-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl'>
						Create Goal
					</button>
				</div>
			</div>
		</div>
	);
};

export default AddGoalModal;
