'use client';
import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import {
	FaSave,
	FaArrowLeft,
	FaTrash,
	FaCheckCircle,
	FaPlus,
	FaMinus,
} from 'react-icons/fa';
import {
	useDeleteLesson,
	useLesson,
	useLessonCategories,
	useUpdateLesson,
} from '@/src/lib/hooks';
import AdminLayout from '../../layout/AdminLayout';
import CloudinaryUpload from '../../common/CloudinaryUpload';
// import { getLessonById } from '@/utils/lessonData';

export default function LessonEditForm({ lessonId }) {
	const router = useRouter();
	const { data: categories } = useLessonCategories();
	const {
		data: lessonData,
		isLoading: lessonLoading,
		error: lessonError,
	} = useLesson(lessonId);
	const updateLessonMutation = useUpdateLesson();
	const deleteLessonMutation = useDeleteLesson();
	console.log(lessonData);

	const [formData, setFormData] = useState({
		title: '',
		instructor: '',
		category: '',
		description: '',
	});

	const [newThumbnailUrl, setNewThumbnailUrl] = useState(null);
	const [isUpdating, setIsUpdating] = useState(false);
	const [updateSuccess, setUpdateSuccess] = useState(false);
	const [currentMedia, setCurrentMedia] = useState({
		thumbnail: '',
		videoUrl: '',
	});
	const [reflectionQuestions, setReflectionQuestions] = useState(['']);

	// Populate form when lesson data is loaded
	useEffect(() => {
		if (lessonData?.data) {
			const lesson = lessonData?.data;
			// console.log('Lesson', lessonData);

			setFormData({
				title: lesson.title || '',
				instructor: lesson.instructor || '',
				category: lesson.category || '',
				description: lesson.description || '',
			});
			setCurrentMedia({
				thumbnail: lesson.thumbnail || '',
				videoUrl: lesson.videoUrl || '',
			});
			setReflectionQuestions(
				lesson.reflectionQuestions && lesson.reflectionQuestions.length > 0
					? lesson.reflectionQuestions
					: [''],
			);
		}
	}, [lessonData]);

	const handleInputChange = (e) => {
		const { name, value } = e.target;
		setFormData((prev) => ({
			...prev,
			[name]: value,
		}));
	};

	const handleThumbnailUploadSuccess = useCallback((uploadResult) => {
		if (uploadResult) {
			setNewThumbnailUrl(uploadResult.url);
		} else {
			setNewThumbnailUrl(null);
		}
	}, []);

	const handleUploadError = useCallback((error) => {
		console.error('Upload error:', error);
		alert(error?.message || 'Upload failed');
	}, []);

	// Reflection question handlers
	const handleReflectionQuestionChange = useCallback((index, value) => {
		setReflectionQuestions((prev) => {
			const newQuestions = [...prev];
			newQuestions[index] = value;
			return newQuestions;
		});
	}, []);

	const addReflectionQuestion = useCallback(() => {
		setReflectionQuestions((prev) => [...prev, '']);
	}, []);

	const removeReflectionQuestion = useCallback((index) => {
		setReflectionQuestions((prev) => prev.filter((_, i) => i !== index));
	}, []);

	const handleSubmit = async (e) => {
		e.preventDefault();
		setIsUpdating(true);
		setUpdateSuccess(false);

		try {
			// Filter out empty reflection questions
			const validReflectionQuestions = reflectionQuestions.filter(
				(q) => q.trim() !== '',
			);

			// Prepare update data
			const updateData = {
				...formData,
				...(newThumbnailUrl && { thumbnail: newThumbnailUrl }),
				reflectionQuestions: validReflectionQuestions,
			};

			// Update lesson with new data
			await updateLessonMutation.mutateAsync({
				id: lessonId,
				data: updateData,
			});

			setUpdateSuccess(true);
			setTimeout(() => {
				router.push('/lessons');
			}, 2000);
		} catch (error) {
			console.error('Update error:', error);
			alert(error?.response?.data?.message || 'Failed to update lesson');
		} finally {
			setIsUpdating(false);
		}
	};

	const handleDelete = async () => {
		if (
			!confirm(
				'Are you sure you want to delete this lesson? This action cannot be undone.',
			)
		) {
			return;
		}

		try {
			await deleteLessonMutation.mutateAsync(lessonId);
			alert('Lesson deleted successfully!');
			router.push('/lessons');
		} catch (error) {
			console.error('Delete error:', error);
			alert(error?.response?.data?.message || 'Failed to delete lesson');
		}
	};

	if (lessonLoading) {
		return (
			<AdminLayout>
				<div className='flex items-center justify-center min-h-screen'>
					<div className='animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900'></div>
				</div>
			</AdminLayout>
		);
	}

	if (lessonError || !lessonData?.data) {
		return (
			<AdminLayout>
				<div className='flex items-center justify-center min-h-screen'>
					<div className='text-center'>
						<h2 className='text-2xl font-bold mb-2'>Lesson not found</h2>
						<p className='text-gray-600 mb-4'>
							The lesson you're trying to edit doesn't exist.
						</p>
						<button
							onClick={() => router.push('/lessons')}
							className='bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded'>
							Back to Lessons
						</button>
					</div>
				</div>
			</AdminLayout>
		);
	}

	return (
		<AdminLayout>
			<div className='min-h-screen bg-gray-50 py-6'>
				<div className='max-w-4xl mx-auto px-6'>
					<div className='flex items-center justify-between mb-6'>
						<button
							onClick={() => router.back()}
							disabled={isUpdating}
							className='flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed'>
							<FaArrowLeft />
							<span className='text-gray-800'>Back</span>
						</button>

						<button
							onClick={handleDelete}
							disabled={deleteLessonMutation.isLoading || isUpdating}
							className='flex items-center gap-2 bg-red-600 hover:bg-red-700 disabled:bg-red-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg transition-colors'>
							<FaTrash />
							{deleteLessonMutation.isLoading ? 'Deleting...' : 'Delete Lesson'}
						</button>
					</div>

					<div className='bg-white rounded-lg shadow-lg p-8'>
						<h1 className='text-3xl font-bold text-gray-900 mb-8'>
							Edit Lesson
						</h1>

						{/* Current Media Preview */}
						<div className='mb-8 p-6 bg-gray-50 rounded-lg'>
							<h3 className='text-lg font-semibold mb-4 text-gray-800'>
								Current Media
							</h3>
							<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
								{currentMedia.thumbnail && (
									<div>
										<p className='text-sm font-medium text-gray-700 mb-2'>
											Current Thumbnail
										</p>
										<img
											src={currentMedia.thumbnail}
											alt='Current thumbnail'
											className='w-full h-48 object-cover rounded-lg border shadow-sm'
										/>
									</div>
								)}
								{currentMedia.videoUrl && (
									<div>
										<p className='text-sm font-medium text-gray-700 mb-2'>
											Current Video
										</p>
										<video
											src={currentMedia.videoUrl}
											className='w-full h-48 object-cover rounded-lg border shadow-sm'
											controls
										/>
										<p className='text-xs text-gray-500 mt-2'>
											Video cannot be changed after upload
										</p>
									</div>
								)}
							</div>
						</div>

						<form
							onSubmit={handleSubmit}
							className='space-y-6'>
							{/* Title */}
							<div>
								<label
									htmlFor='title'
									className='block text-sm font-medium text-gray-700 mb-2'>
									Title *
								</label>
								<input
									type='text'
									id='title'
									name='title'
									value={formData.title}
									onChange={handleInputChange}
									disabled={isUpdating}
									required
									className='w-full px-4 py-3 text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed'
									placeholder='Enter lesson title'
								/>
							</div>

							{/* Instructor */}
							<div>
								<label
									htmlFor='instructor'
									className='block text-sm font-medium text-gray-700 mb-2'>
									Instructor *
								</label>
								<input
									type='text'
									id='instructor'
									name='instructor'
									value={formData.instructor}
									onChange={handleInputChange}
									disabled={isUpdating}
									required
									className='w-full px-4 py-3 text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed'
									placeholder='Enter instructor name'
								/>
							</div>

							{/* Category */}
							<div>
								<label
									htmlFor='category'
									className='block text-sm font-medium text-gray-700 mb-2'>
									Category *
								</label>
								<select
									id='category'
									name='category'
									value={formData.category}
									onChange={handleInputChange}
									disabled={isUpdating}
									required
									className='w-full px-4 py-3 text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed'>
									<option value=''>Select a category</option>
									{categories?.map((category) => (
										<option
											key={category}
											value={category}>
											{category}
										</option>
									))}
								</select>
							</div>

							{/* Description */}
							<div>
								<label
									htmlFor='description'
									className='block text-sm font-medium text-gray-700 mb-2'>
									Description *
								</label>
								<textarea
									id='description'
									name='description'
									value={formData.description}
									onChange={handleInputChange}
									disabled={isUpdating}
									required
									rows={4}
									className='w-full px-4 py-3 text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed resize-none'
									placeholder='Enter lesson description'
								/>
							</div>

							{/* Reflection Questions */}
							<div>
								<div className='flex items-center justify-between mb-4'>
									<label className='block text-sm font-medium text-gray-700'>
										Reflection Questions (Optional)
									</label>
									<button
										type='button'
										onClick={addReflectionQuestion}
										disabled={isUpdating}
										className='flex items-center gap-2 bg-teal-600 hover:bg-teal-700 text-white px-3 py-2 rounded-lg text-sm transition-colors disabled:opacity-50'>
										<FaPlus size={12} />
										Add Question
									</button>
								</div>
								<div className='space-y-3'>
									{reflectionQuestions.map((question, index) => (
										<div
											key={index}
											className='flex items-center gap-3'>
											<div className='flex-1'>
												<input
													type='text'
													value={question}
													onChange={(e) =>
														handleReflectionQuestionChange(
															index,
															e.target.value,
														)
													}
													disabled={isUpdating}
													className='w-full px-4 py-3 text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed'
													placeholder={`Reflection question ${index + 1}`}
												/>
											</div>
											{reflectionQuestions.length > 1 && (
												<button
													type='button'
													onClick={() => removeReflectionQuestion(index)}
													disabled={isUpdating}
													className='p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50'>
													<FaMinus size={14} />
												</button>
											)}
										</div>
									))}
								</div>
								<p className='text-sm text-gray-500 mt-2'>
									Add reflection questions to help users think deeper about the
									lesson content.
								</p>
							</div>

							{/* Thumbnail Upload Area */}
							<div>
								<CloudinaryUpload
									type='image'
									folder='lesson-thumbnails'
									label='Update Thumbnail (Optional)'
									onUploadSuccess={handleThumbnailUploadSuccess}
									onUploadError={handleUploadError}
									disabled={isUpdating}
									value={newThumbnailUrl}
								/>
							</div>

							{/* Submit Button */}
							<div className='pt-6'>
								<button
									type='submit'
									disabled={isUpdating || updateSuccess}
									className='w-full flex items-center justify-center gap-3 bg-teal-600 hover:bg-teal-700 disabled:bg-teal-400 disabled:cursor-not-allowed text-white font-semibold py-4 px-6 rounded-lg transition-all duration-200 transform hover:scale-[1.02] disabled:hover:scale-100'>
									{isUpdating ? (
										<>
											<div className='animate-spin rounded-full h-5 w-5 border-b-2 border-white'></div>
											<span>Updating...</span>
										</>
									) : updateSuccess ? (
										<>
											<FaCheckCircle className='text-lg' />
											<span>Update Complete!</span>
										</>
									) : (
										<>
											<FaSave className='text-lg' />
											<span>Update Lesson</span>
										</>
									)}
								</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</AdminLayout>
	);
}
