import React from 'react';
import { Fa<PERSON><PERSON>, <PERSON>a<PERSON>lock, FaBook } from 'react-icons/fa';
import { useRouter } from 'next/navigation';
import { CustomImage } from '../../common';

const OngoingVideoCard = ({ userProgress }) => {
	const router = useRouter();

	if (!userProgress || !userProgress.lessonId) {
		return null;
	}

	const lesson = userProgress.lessonId;
	const progress = Math.round(userProgress.videoProgress * 100);

	console.log(lesson);

	const getProgressColor = (progress) => {
		if (progress < 30) return 'from-red-400 to-red-500';
		if (progress < 70) return 'from-yellow-400 to-orange-500';
		return 'from-green-400 to-green-500';
	};

	const handleContinueWatching = () => {
		router.push(`/lessons/${lesson._id}`);
	};

	const formatWatchTime = (seconds) => {
		const minutes = Math.floor(seconds / 60);
		const hours = Math.floor(minutes / 60);

		if (hours > 0) {
			return `${hours}h ${minutes % 60}m`;
		}
		return `${minutes}m`;
	};

	return (
		<div className='relative w-full h-80 rounded-2xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-[1.02] group cursor-pointer'>
			{/* Background Image */}
			<div className='absolute inset-0'>
				<CustomImage
					src={lesson.thumbnail}
					alt={lesson.title}
					fill
					className='object-cover transition-transform duration-500 group-hover:scale-110'
				/>
				{/* Dark overlay */}
				<div className='absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-black/20' />
			</div>

			{/* Play button overlay */}
			<div className='absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300'>
				<div className='bg-white/20 backdrop-blur-sm rounded-full p-4 border border-white/30 hover:bg-white/30 transition-all duration-200'>
					<FaPlay className='text-white text-2xl ml-1' />
				</div>
			</div>

			{/* Progress indicator at top */}
			<div className='absolute top-4 left-4 right-4'>
				<div className='flex items-center justify-between mb-2'>
					<div className='bg-black/50 backdrop-blur-sm rounded-full px-3 py-1 border border-white/20'>
						<div className='flex items-center gap-2 text-white text-xs'>
							<FaClock className='w-3 h-3' />
							<span>{formatWatchTime(userProgress.watchTime)}</span>
						</div>
					</div>
					<div className='bg-black/50 backdrop-blur-sm rounded-full px-3 py-1 border border-white/20'>
						<span className='text-white text-xs font-semibold'>
							{progress}%
						</span>
					</div>
				</div>

				{/* Progress bar */}
				<div className='relative w-full bg-white/20 backdrop-blur-sm rounded-full h-1.5 overflow-hidden border border-white/30'>
					<div
						className={`absolute top-0 left-0 h-full bg-gradient-to-r ${getProgressColor(
							progress,
						)} rounded-full transition-all duration-700 ease-out`}
						style={{ width: `${progress}%` }}
					/>
					<div className='absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-full animate-pulse' />
				</div>
			</div>

			{/* Content overlay at bottom */}
			<div className='absolute bottom-0 left-0 right-0 p-6'>
				<div className='bg-black/60 backdrop-blur-md rounded-2xl p-4 border border-white/20 shadow-2xl'>
					{/* Header */}
					<div className='flex items-start justify-between mb-3'>
						<div className='flex-1 min-w-0'>
							<h3 className='text-lg font-bold text-white mb-1 line-clamp-2 leading-tight'>
								{lesson.title}
							</h3>
							<p className='text-sm text-white/80 mb-2'>
								by {lesson.instructor}
							</p>
						</div>
						<div className='bg-white/10 backdrop-blur-sm p-2 rounded-lg border border-white/20'>
							<FaBook className='w-4 h-4 text-white/80' />
						</div>
					</div>

					{/* Last watched info */}
					<div className='flex items-center justify-between mb-4'>
						<div className='text-xs text-white/70'>
							Last watched:{' '}
							{new Date(userProgress.lastWatchedAt).toLocaleDateString()}
						</div>
						<div className='text-xs text-white/70 bg-white/10 backdrop-blur-sm px-2 py-1 rounded-full border border-white/20'>
							Continue Learning
						</div>
					</div>

					{/* Action Button */}
					<button
						onClick={handleContinueWatching}
						className='w-full bg-gradient-to-r from-teal-500/90 to-teal-600/90 hover:from-teal-600/90 hover:to-teal-700/90 backdrop-blur-sm text-white font-semibold py-3 px-4 rounded-xl transition-all duration-300 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] flex items-center justify-center gap-2 group border border-white/20'>
						<FaPlay className='w-4 h-4 group-hover:translate-x-1 transition-transform duration-200' />
						<span>Continue Watching</span>
					</button>
				</div>
			</div>

			{/* Decorative light streaks */}
			<div className='absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-white/10 to-transparent rounded-full -translate-y-16 translate-x-16 pointer-events-none opacity-50' />
			<div className='absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-teal-400/20 to-transparent rounded-full translate-y-12 -translate-x-12 pointer-events-none opacity-70' />
		</div>
	);
};

export default OngoingVideoCard;
