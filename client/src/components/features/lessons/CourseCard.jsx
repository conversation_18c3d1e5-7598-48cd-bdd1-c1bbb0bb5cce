import React from 'react';
import { useRouter } from 'next/navigation';
import { CustomImage } from '../../common';

const CourseCard = ({ title, imageSrc, course }) => {
	const router = useRouter();

	return (
		<div
			onClick={() => {
				// console.log(course);
				router.push(`/lessons/${course?._id}`);
			}}
			className='group cursor-pointer transform transition-all duration-300 hover:scale-[1.02]'>
			{/* Mobile Card - Only visible on mobile */}
			<div className='lg:hidden bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-sm border border-gray-100 dark:border-gray-700 hover:shadow-lg transition-all duration-300'>
				<div className='relative h-36 w-full overflow-hidden'>
					<CustomImage
						src={imageSrc}
						alt='Course Image'
						fill
						className='object-cover group-hover:scale-105 transition-transform duration-300'
					/>
					<div className='absolute inset-0 bg-gradient-to-t from-black/20 to-transparent'></div>
					<div className='absolute top-3 right-3 bg-gradient-to-r from-teal-600 to-teal-700 text-white text-xs font-medium px-3 py-1.5 rounded-full shadow-lg backdrop-blur-sm'>
						New
					</div>
				</div>

				<div className='p-4'>
					<h3 className='text-sm font-semibold text-gray-800 dark:text-white line-clamp-2 leading-relaxed'>
						{title}
					</h3>
				</div>
			</div>

			{/* Desktop Card - Only visible on desktop */}
			<div className='hidden lg:block bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-sm border border-gray-100 dark:border-gray-700 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group-hover:border-teal-200 dark:group-hover:border-teal-700'>
				<div className='relative h-52 w-full overflow-hidden'>
					<CustomImage
						src={imageSrc}
						alt='Course Image'
						fill
						className='object-cover group-hover:scale-110 transition-transform duration-500'
					/>
					<div className='absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300'></div>
					<div className='absolute top-4 right-4 bg-gradient-to-r from-teal-600 to-teal-700 text-white text-xs font-medium px-3 py-1.5 rounded-full shadow-lg backdrop-blur-sm'>
						New
					</div>
					{/* Subtle overlay gradient */}
					<div className='absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-black/10 to-transparent'></div>
				</div>

				<div className='p-6'>
					<h3 className='text-lg font-bold text-gray-800 dark:text-white mb-3 line-clamp-2 leading-tight group-hover:text-teal-700 dark:group-hover:text-teal-400 transition-colors duration-200'>
						{title}
					</h3>

					{/* Enhanced stats section - uncomment if needed */}
					{/* 
					<div className='flex items-center text-yellow-400 mb-3'>
						{[...Array(4)].map((_, i) => (
							<FaStar key={i} className='w-4 h-4 mr-0.5' />
						))}
						<FaStar className='w-4 h-4 mr-0.5 text-gray-300 dark:text-gray-600' />
						<span className='text-gray-600 dark:text-gray-400 text-sm ml-2 font-medium'>(4.0)</span>
					</div>

					<div className='flex justify-between items-center text-sm text-gray-600 dark:text-gray-400 pt-3 border-t border-gray-100 dark:border-gray-700'>
						<div className='flex items-center space-x-1'>
							<FaUser className='w-3 h-3 text-teal-600' />
							<span className='font-medium'>2.5k students</span>
						</div>
						<div className='flex items-center space-x-1'>
							<FaClock className='w-3 h-3 text-teal-600' />
							<span className='font-medium'>3h 45m</span>
						</div>
					</div>
					*/}

					{/* Course preview indicator */}
					<div className='mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300'>
						<div className='text-teal-600 dark:text-teal-400 text-sm font-medium flex items-center'>
							<span>View Course</span>
							<svg
								className='w-4 h-4 ml-2 transform translate-x-0 group-hover:translate-x-1 transition-transform duration-200'
								fill='none'
								stroke='currentColor'
								viewBox='0 0 24 24'>
								<path
									strokeLinecap='round'
									strokeLinejoin='round'
									strokeWidth={2}
									d='M9 5l7 7-7 7'
								/>
							</svg>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default CourseCard;
