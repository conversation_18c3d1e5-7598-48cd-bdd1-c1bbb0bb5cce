'use client';
import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { VideoOff } from 'lucide-react';
import debounce from 'lodash.debounce';
import { CustomImage, Pagination } from '../../common';
import { useLessons } from '@/src/lib/hooks';
import FixedHeader from '../../layout/FixedHeader';
import Sidebar from '../../layout/Sidebar';

// Skeleton component for loading state
const LessonSkeleton = () => (
	<div className='dark:bg-white bg-gray-200 rounded-lg overflow-hidden shadow-md animate-pulse'>
		<div className='relative h-48 w-full bg-gray-300 dark:bg-gray-400'></div>
		<div className='p-4'>
			<div className='h-6 bg-gray-300 dark:bg-gray-400 rounded mb-2'></div>
			<div className='h-4 bg-gray-300 dark:bg-gray-400 rounded mb-1'></div>
			<div className='h-4 bg-gray-300 dark:bg-gray-400 rounded w-3/4 mb-3'></div>
			<div className='flex items-center justify-between'>
				<div className='flex items-center'>
					<div className='w-8 h-8 rounded-full bg-gray-300 dark:bg-gray-400 mr-2'></div>
					<div className='h-4 bg-gray-300 dark:bg-gray-400 rounded w-20'></div>
				</div>
				<div className='h-8 bg-gray-300 dark:bg-gray-400 rounded w-16'></div>
			</div>
		</div>
	</div>
);

// Empty state component
const EmptyState = ({ category }) => (
	<div className='flex flex-col items-center justify-center py-16'>
		<div className='mb-4 p-6 rounded-full bg-gray-100 dark:bg-gray-800'>
			<VideoOff
				size={48}
				className='text-gray-400 dark:text-gray-500'
			/>
		</div>
		<h3 className='text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2'>
			No videos for this category
		</h3>
		<p className='text-gray-500 dark:text-gray-400 text-center max-w-md'>
			There are currently no lessons available in the "{category}" category.
			Check back later or explore other categories.
		</p>
	</div>
);

export default function Lessons() {
	const router = useRouter();
	const [currentPage, setCurrentPage] = useState(1);
	const [searchTerm, setSearchTerm] = useState('');
	const [categoryFilter, setCategoryFilter] = useState('YOU'); // Start with "YOU"
	const [windowWidth, setWindowWidth] = useState(0);
	const [isSidebarOpen, setIsSidebarOpen] = useState(false);

	const {
		data: lessonsData,
		isLoading,
		error,
		isFetching,
	} = useLessons({
		page: currentPage,
		limit: 9,
		category: categoryFilter,
		title: searchTerm,
	});

	useEffect(() => {
		const handleResize = () => setWindowWidth(window.innerWidth);
		handleResize();
		window.addEventListener('resize', handleResize);
		return () => window.removeEventListener('resize', handleResize);
	}, []);

	const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);

	const tabs = ['YOU', '=', 'LIFE'];

	const debouncedSearch = useCallback(
		debounce((term) => {
			setSearchTerm(term);
			setCurrentPage(1);
		}, 300),
		[],
	);

	const handleSearch = (term) => {
		debouncedSearch(term);
	};

	const handleCategoryFilter = (category) => {
		setCategoryFilter(category);
		setCurrentPage(1);
	};

	const handlePageChange = (page) => {
		setCurrentPage(page);
	};

	// Fix: Change this line to match your actual data structure
	const lessons = lessonsData?.data || [];
	const pagination = lessonsData?.pagination || {};
	// console.log(lessons);

	if (error) {
		return (
			<div className='flex items-center justify-center min-h-screen'>
				<div className='text-red-500 text-center'>
					<h2 className='text-2xl font-bold mb-2'>Error Loading Lessons</h2>
					<p>{error?.response?.data?.message || 'Something went wrong'}</p>
				</div>
			</div>
		);
	}

	return (
		<div className='flex flex-col min-h-screen'>
			<FixedHeader
				toggleSidebar={toggleSidebar}
				title='Lessons'
				showBackButton={false}
			/>

			{/* Tabs */}
			<div className='flex justify-around px-4 py-2'>
				{tabs.map((tab) => (
					<button
						key={tab}
						className={`text-2xl py-2 px-4 rounded-lg font-extrabold transition-colors ${
							categoryFilter === tab
								? 'bg-gray-700 dark:bg-white dark:text-gray-900 text-white'
								: 'dark:bg-teal-600 bg-gray-300 text-black dark:text-white hover:bg-gray-400 dark:hover:bg-teal-700'
						}`}
						onClick={() => handleCategoryFilter(tab)}>
						{tab}
					</button>
				))}
			</div>

			{/* Main Content */}
			<div className='flex-1 overflow-y-auto p-4'>
				{/* Loading Skeletons */}
				{isLoading && (
					<div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3'>
						{Array.from({ length: 6 }).map((_, index) => (
							<LessonSkeleton key={index} />
						))}
					</div>
				)}

				{lessons.length === 0 && !isLoading && (
					<EmptyState category={categoryFilter} />
				)}

				{/* Lessons Grid */}
				{lessons.length > 0 && !isLoading && (
					<div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3'>
						{lessons.map((lesson) => (
							<div
								key={lesson._id}
								className='relative rounded-lg overflow-hidden shadow-md cursor-pointer hover:shadow-lg transition-shadow group h-80'
								onClick={() => router.push(`/lessons/${lesson._id}`)}>
								{/* Full-size thumbnail background */}
								<div className='absolute inset-0 w-full h-full'>
									<CustomImage
										src={lesson.thumbnail}
										alt={lesson.title}
										fill
										className='object-cover group-hover:scale-105 transition-transform duration-300'
									/>
								</div>

								{/* Small frosted glass card with content */}
								<div className='absolute bottom-4 left-4'>
									<div className='backdrop-blur-md bg-black/40 rounded-xl py-2 px-4 border border-white/20 shadow-lg'>
										<h3 className='text-lg font-bold mb-2 line-clamp-2 text-gray-300 bg-gray-900 rounded-3xl px-2'>
											{lesson.title}
										</h3>

										<div className='flex items-center'>
											<div className='w-8 h-8 rounded-full overflow-hidden flex items-center justify-center mr-2 bg-white/20 backdrop-blur-sm'>
												<CustomImage
													src='/student-icon.svg'
													alt='Instructor'
													width={24}
													height={24}
													className='object-cover'
												/>
											</div>
											<span className='font-medium text-sm text-white'>
												{lesson.instructor}
											</span>
										</div>
									</div>
								</div>
							</div>
						))}
					</div>
				)}

				{/* Pagination */}
				{pagination.totalPages && pagination.totalPages > 1 && !isLoading ? (
					<div className='mt-8 flex justify-center'>
						<Pagination
							currentPage={pagination.page || 1}
							totalPages={pagination.totalPages}
							onPageChange={handlePageChange}
						/>
					</div>
				) : (
					''
				)}
			</div>

			<Sidebar
				isOpen={isSidebarOpen}
				onToggle={toggleSidebar}
			/>
		</div>
	);
}
