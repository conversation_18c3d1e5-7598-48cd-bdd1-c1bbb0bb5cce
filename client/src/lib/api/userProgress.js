/**
 * User Progress API service
 */
import { api } from './client';

export const userProgressService = {
  /**
   * Update user progress for a lesson
   * @param {Object} progressData - Progress data
   * @param {string} progressData.lessonId - Lesson ID
   * @param {number} progressData.currentTime - Current video time
   * @param {number} progressData.duration - Total video duration
   * @param {boolean} progressData.completed - Whether lesson is completed
   * @param {string} progressData.notes - User notes
   * @returns {Promise} API response
   */
  updateProgress: async (progressData) => {
    const response = await api.post('/api/user/progress', progressData);
    return response.data;
  },

  /**
   * Get user progress for a specific lesson
   * @param {string} lessonId - Lesson ID
   * @returns {Promise} API response
   */
  getProgress: async (lessonId) => {
    const response = await api.get(`/api/user/progress/${lessonId}`);
    return response.data;
  },

  /**
   * Get all user progress (for ongoing videos)
   * @returns {Promise} API response
   */
  getAllProgress: async () => {
    const response = await api.get('/api/user/progress/all');
    return response.data;
  },

  /**
   * Update reflection answer
   * @param {Object} reflectionData - Reflection data
   * @param {string} reflectionData.lessonId - Lesson ID
   * @param {number} reflectionData.questionIndex - Question index
   * @param {string} reflectionData.answer - User's answer
   * @returns {Promise} API response
   */
  updateReflection: async (reflectionData) => {
    const response = await api.post('/api/user/reflection', reflectionData);
    return response.data;
  },

  /**
   * Delete reflection answer
   * @param {string} lessonId - Lesson ID
   * @param {number} questionIndex - Question index
   * @returns {Promise} API response
   */
  deleteReflection: async (lessonId, questionIndex) => {
    const response = await api.delete(`/api/user/reflection/${lessonId}/${questionIndex}`);
    return response.data;
  },

  /**
   * Get user's reflection answers for a lesson
   * @param {string} lessonId - Lesson ID
   * @returns {Promise} API response
   */
  getReflections: async (lessonId) => {
    const response = await api.get(`/api/user/reflection/${lessonId}`);
    return response.data;
  },

  /**
   * Get user's overall learning statistics
   * @returns {Promise} API response
   */
  getLearningStats: async () => {
    const response = await api.get('/api/user/progress/stats');
    return response.data;
  },

  /**
   * Mark lesson as completed
   * @param {string} lessonId - Lesson ID
   * @returns {Promise} API response
   */
  markLessonCompleted: async (lessonId) => {
    const response = await api.post(`/api/user/progress/${lessonId}/complete`);
    return response.data;
  },

  /**
   * Reset lesson progress
   * @param {string} lessonId - Lesson ID
   * @returns {Promise} API response
   */
  resetProgress: async (lessonId) => {
    const response = await api.delete(`/api/user/progress/${lessonId}`);
    return response.data;
  },

  /**
   * Get user's completed lessons
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number
   * @param {number} params.limit - Items per page
   * @returns {Promise} API response
   */
  getCompletedLessons: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `/api/user/progress/completed?${queryString}` : '/api/user/progress/completed';
    const response = await api.get(url);
    return response.data;
  },
};
