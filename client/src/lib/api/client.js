/**
 * Consolidated API client with improved error handling and interceptors
 */
import axios from 'axios';

const baseURL = process?.env?.NEXT_PUBLIC_BASE_URL || 'http://localhost:5000';

// Create axios instance
const apiClient = axios.create({
	baseURL,
	timeout: 30000, // 30 seconds timeout
	headers: {
		'Content-Type': 'application/json',
	},
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
	(config) => {
		// Add auth token if available
		if (typeof window !== 'undefined') {
			const token = localStorage.getItem('accessToken');
			if (token) {
				config.headers.Authorization = `Bearer ${token}`;
			}
		}

		// Log request in development
		if (process.env.NODE_ENV === 'development') {
			console.log(
				`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`,
			);
		}

		return config;
	},
	(error) => {
		console.error('Request interceptor error:', error);
		return Promise.reject(error);
	},
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
	(response) => {
		// Log successful response in development
		if (process.env.NODE_ENV === 'development') {
			console.log(
				`✅ API Response: ${response.config.method?.toUpperCase()} ${
					response.config.url
				}`,
				response.status,
			);
		}
		return response;
	},
	(error) => {
		// Handle different error scenarios
		if (error.response) {
			const { status, data } = error.response;

			// Handle authentication errors
			if (status === 401) {
				// Clear tokens and redirect to login
				if (typeof window !== 'undefined') {
					localStorage.removeItem('accessToken');
					localStorage.removeItem('refreshToken');
					window.dispatchEvent(new Event('auth-change'));

					// Only redirect if not already on auth pages
					if (!window.location.pathname.startsWith('/auth')) {
						window.location.href = '/auth/login';
					}
				}
			}

			// Handle rate limiting
			if (status === 429) {
				console.warn('Rate limit exceeded');
			}

			// Log error in development
			if (process.env.NODE_ENV === 'development') {
				console.error(
					`❌ API Error: ${error.config?.method?.toUpperCase()} ${
						error.config?.url
					}`,
					{
						status,
						message: data?.message || error.message,
						data,
					},
				);
			}
		} else if (error.request) {
			// Network error
			console.error('Network error:', error.message);
		} else {
			// Other error
			console.error('API error:', error.message);
		}

		return Promise.reject(error);
	},
);

// Helper function to set authorization header
export const setAuthToken = (token) => {
	if (token) {
		apiClient.defaults.headers.common.Authorization = `Bearer ${token}`;
	} else {
		delete apiClient.defaults.headers.common.Authorization;
	}
};

// Helper function to create query string
export const createQueryString = (params) => {
	const searchParams = new URLSearchParams();

	Object.entries(params).forEach(([key, value]) => {
		if (value !== undefined && value !== null && value !== '') {
			searchParams.append(key, value.toString());
		}
	});

	return searchParams.toString();
};

// Generic API methods
export const api = {
	get: (url, config = {}) => apiClient.get(url, config),
	post: (url, data, config = {}) => apiClient.post(url, data, config),
	put: (url, data, config = {}) => apiClient.put(url, data, config),
	patch: (url, data, config = {}) => apiClient.patch(url, data, config),
	delete: (url, config = {}) => apiClient.delete(url, config),
};

// Initialize auth token on app startup
if (typeof window !== 'undefined') {
	const storedToken = localStorage.getItem('accessToken');
	if (storedToken) {
		setAuthToken(storedToken);
	}
}
