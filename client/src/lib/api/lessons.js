import { api, createQueryString } from './client';

export const lessonsService = {
	getLessons: async (params = {}) => {
		const queryString = createQueryString(params);
		const url = queryString
			? `/api/lesson/lessons?${queryString}`
			: '/api/lesson/lessons';
		const response = await api.get(url);
		return response.data;
	},

	getLessonById: async (id) => {
		const response = await api.get(`/api/lesson/${id}`);
		return response.data;
	},

	createLesson: async (lessonData) => {
		const response = await api.post('/api/lesson/lessons', lessonData);
		return response.data;
	},

	updateLesson: async (id, lessonData) => {
		const response = await api.put(`/api/lesson/${id}`, lessonData);
		return response.data;
	},

	deleteLesson: async (id) => {
		const response = await api.delete(`/api/lesson/${id}`);
		return response.data;
	},

	uploadVideo: async (formData) => {
		const response = await api.post('/api/lesson/upload-video', formData, {
			headers: {
				'Content-Type': 'multipart/form-data',
			},
		});
		return response.data;
	},

	getCategories: async () => {
		return ['YOU', '=', 'LIFE'];
	},

	getFeaturedLessons: async (limit = 6) => {
		const response = await api.get(`/api/lesson/featured?limit=${limit}`);
		return response.data;
	},

	getRecentLessons: async (limit = 10) => {
		const response = await api.get(`/api/lesson/recent?limit=${limit}`);
		return response.data;
	},

	searchLessons: async (query, filters = {}) => {
		const params = { q: query, ...filters };
		const queryString = createQueryString(params);
		const response = await api.get(`/api/lesson/search?${queryString}`);
		return response.data;
	},
};
