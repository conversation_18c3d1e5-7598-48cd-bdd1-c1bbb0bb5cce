/**
 * Authentication API service
 */
import { api } from './client';

export const authService = {
  /**
   * Register a new user
   * @param {Object} userData - User registration data
   * @param {string} userData.name - User's full name
   * @param {string} userData.email - User's email
   * @param {string} userData.password - User's password
   * @returns {Promise} API response
   */
  register: async (userData) => {
    const response = await api.post('/api/auth/register', userData);
    return response.data;
  },

  /**
   * Verify email with OTP
   * @param {Object} verificationData - Email verification data
   * @param {string} verificationData.email - User's email
   * @param {string} verificationData.otp - OTP code
   * @returns {Promise} API response
   */
  verifyEmail: async (verificationData) => {
    const response = await api.post('/api/auth/verify-email', verificationData);
    return response.data;
  },

  /**
   * Login user
   * @param {Object} credentials - Login credentials
   * @param {string} credentials.email - User's email
   * @param {string} credentials.password - User's password
   * @returns {Promise} API response
   */
  login: async (credentials) => {
    const response = await api.post('/api/auth/login', credentials);
    return response.data;
  },

  /**
   * Google OAuth login
   * @param {Object} googleData - Google OAuth data
   * @param {string} googleData.idToken - Google ID token
   * @returns {Promise} API response
   */
  googleLogin: async (googleData) => {
    const response = await api.post('/api/auth/google-login', googleData);
    return response.data;
  },

  /**
   * Request password reset
   * @param {Object} emailData - Email data
   * @param {string} emailData.email - User's email
   * @returns {Promise} API response
   */
  forgotPassword: async (emailData) => {
    const response = await api.post('/api/auth/forgot-password', emailData);
    return response.data;
  },

  /**
   * Reset password with OTP
   * @param {Object} resetData - Password reset data
   * @param {string} resetData.email - User's email
   * @param {string} resetData.otp - OTP code
   * @param {string} resetData.newPassword - New password
   * @returns {Promise} API response
   */
  resetPassword: async (resetData) => {
    const response = await api.post('/api/auth/reset-password', resetData);
    return response.data;
  },

  /**
   * Resend OTP
   * @param {Object} emailData - Email data
   * @param {string} emailData.email - User's email
   * @returns {Promise} API response
   */
  resendOTP: async (emailData) => {
    const response = await api.post('/api/auth/resend-otp', emailData);
    return response.data;
  },

  /**
   * Refresh access token
   * @param {Object} tokenData - Token data
   * @param {string} tokenData.refreshToken - Refresh token
   * @returns {Promise} API response
   */
  refreshToken: async (tokenData) => {
    const response = await api.post('/api/auth/refresh-token', tokenData);
    return response.data;
  },

  /**
   * Logout user
   * @returns {Promise} API response
   */
  logout: async () => {
    const response = await api.post('/api/auth/logout');
    return response.data;
  },
};
