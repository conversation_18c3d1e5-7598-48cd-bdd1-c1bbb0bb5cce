/**
 * Values API service
 */
import { api, createQueryString } from './client';

export const valuesService = {
  /**
   * Get user's values
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number
   * @param {number} params.limit - Items per page
   * @returns {Promise} API response
   */
  getUserValues: async (params = {}) => {
    const queryString = createQueryString(params);
    const url = queryString ? `/api/values?${queryString}` : '/api/values';
    const response = await api.get(url);
    return response.data;
  },

  /**
   * Get public values
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number
   * @param {number} params.limit - Items per page
   * @returns {Promise} API response
   */
  getPublicValues: async (params = {}) => {
    const queryString = createQueryString(params);
    const url = queryString ? `/api/values/public?${queryString}` : '/api/values/public';
    const response = await api.get(url);
    return response.data;
  },

  /**
   * Get value by ID
   * @param {string} id - Value ID
   * @returns {Promise} API response
   */
  getValueById: async (id) => {
    const response = await api.get(`/api/values/${id}`);
    return response.data;
  },

  /**
   * Create new value
   * @param {Object} valueData - Value data
   * @param {string} valueData.title - Value title
   * @param {string} valueData.description - Value description
   * @param {string} valueData.category - Value category
   * @param {boolean} valueData.isPublic - Whether value is public
   * @returns {Promise} API response
   */
  createValue: async (valueData) => {
    const response = await api.post('/api/values', valueData);
    return response.data;
  },

  /**
   * Update value
   * @param {string} id - Value ID
   * @param {Object} valueData - Updated value data
   * @returns {Promise} API response
   */
  updateValue: async (id, valueData) => {
    const response = await api.put(`/api/values/${id}`, valueData);
    return response.data;
  },

  /**
   * Delete value
   * @param {string} id - Value ID
   * @returns {Promise} API response
   */
  deleteValue: async (id) => {
    const response = await api.delete(`/api/values/${id}`);
    return response.data;
  },

  /**
   * Get predefined values (system values)
   * @returns {Promise} API response
   */
  getPredefinedValues: async () => {
    const response = await api.get('/api/values/predefined');
    return response.data;
  },

  /**
   * Get value categories
   * @returns {Promise} API response
   */
  getValueCategories: async () => {
    const response = await api.get('/api/values/categories');
    return response.data;
  },

  /**
   * Search values
   * @param {string} query - Search query
   * @param {Object} filters - Additional filters
   * @returns {Promise} API response
   */
  searchValues: async (query, filters = {}) => {
    const params = { q: query, ...filters };
    const queryString = createQueryString(params);
    const response = await api.get(`/api/values/search?${queryString}`);
    return response.data;
  },
};
