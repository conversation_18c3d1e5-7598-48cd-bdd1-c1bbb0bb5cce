/**
 * User API service
 */
import { api, createQueryString } from './client';

export const userService = {
  /**
   * Get user profile
   * @returns {Promise} API response
   */
  getProfile: async () => {
    const response = await api.get('/api/user/profile');
    return response.data;
  },

  /**
   * Update user profile
   * @param {Object} profileData - Profile data to update
   * @param {string} profileData.name - User's name
   * @param {string} profileData.email - User's email
   * @param {string} profileData.bio - User's bio
   * @param {string} profileData.avatar - User's avatar URL
   * @returns {Promise} API response
   */
  updateProfile: async (profileData) => {
    const response = await api.put('/api/user/profile', profileData);
    return response.data;
  },

  /**
   * Get all users (admin only)
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number
   * @param {number} params.limit - Items per page
   * @param {string} params.search - Search query
   * @returns {Promise} API response
   */
  getAllUsers: async (params = {}) => {
    const queryString = createQueryString(params);
    const url = queryString ? `/api/user/allUser?${queryString}` : '/api/user/allUser';
    const response = await api.get(url);
    return response.data;
  },

  /**
   * Update user role (admin only)
   * @param {string} userId - User ID
   * @param {Object} roleData - Role data
   * @param {Array} roleData.roles - Array of roles
   * @returns {Promise} API response
   */
  updateUserRole: async (userId, roleData) => {
    const response = await api.patch(`/api/user/${userId}/role`, roleData);
    return response.data;
  },

  /**
   * Delete user (admin only)
   * @param {string} userId - User ID
   * @returns {Promise} API response
   */
  deleteUser: async (userId) => {
    const response = await api.delete(`/api/user/${userId}`);
    return response.data;
  },

  /**
   * Upload user avatar
   * @param {FormData} formData - Form data with avatar file
   * @returns {Promise} API response
   */
  uploadAvatar: async (formData) => {
    const response = await api.post('/api/user/upload-avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  /**
   * Change password
   * @param {Object} passwordData - Password change data
   * @param {string} passwordData.currentPassword - Current password
   * @param {string} passwordData.newPassword - New password
   * @returns {Promise} API response
   */
  changePassword: async (passwordData) => {
    const response = await api.post('/api/user/change-password', passwordData);
    return response.data;
  },

  /**
   * Get user statistics
   * @returns {Promise} API response
   */
  getUserStats: async () => {
    const response = await api.get('/api/user/stats');
    return response.data;
  },

  /**
   * Get user preferences
   * @returns {Promise} API response
   */
  getPreferences: async () => {
    const response = await api.get('/api/user/preferences');
    return response.data;
  },

  /**
   * Update user preferences
   * @param {Object} preferences - User preferences
   * @returns {Promise} API response
   */
  updatePreferences: async (preferences) => {
    const response = await api.put('/api/user/preferences', preferences);
    return response.data;
  },
};
