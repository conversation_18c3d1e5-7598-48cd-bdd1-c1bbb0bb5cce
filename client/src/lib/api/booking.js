import { api, createQueryString } from './client';

export const bookingService = {
	// Create a new booking
	createBooking: async (bookingData) => {
		try {
			const response = await api.post('/api/bookings/create', bookingData);
			return response.data;
		} catch (error) {
			throw {
				message: error.response?.data?.message || 'Failed to create booking',
				errors: error.response?.data?.errors || [],
				status: error.response?.status,
			};
		}
	},

	// Get all bookings with pagination and filtering (admin)
	getAllBookings: async (params = {}) => {
		try {
			const {
				page = 1,
				limit = 10,
				status,
				sessionType,
				email,
				bookedBy,
				startDate,
				endDate,
			} = params;

			const queryParams = createQueryString({
				page,
				limit,
				status,
				sessionType,
				email,
				bookedBy,
				startDate,
				endDate,
			});

			const url = `/api/bookings/admin/all${
				queryParams ? `?${queryParams}` : ''
			}`;
			const response = await api.get(url);
			return response.data;
		} catch (error) {
			throw {
				message: error.response?.data?.message || 'Failed to fetch bookings',
				status: error.response?.status,
			};
		}
	},

	// Get single booking by ID
	getBookingById: async (id) => {
		try {
			const response = await api.get(`/api/bookings/admin/${id}`);
			return response.data;
		} catch (error) {
			throw {
				message: error.response?.data?.message || 'Failed to fetch booking',
				status: error.response?.status,
			};
		}
	},

	// Update booking status
	updateBookingStatus: async (id, status) => {
		try {
			const response = await api.patch(`/api/bookings/admin/${id}/status`, {
				status,
			});
			return response.data;
		} catch (error) {
			throw {
				message:
					error.response?.data?.message || 'Failed to update booking status',
				errors: error.response?.data?.errors || [],
				status: error.response?.status,
			};
		}
	},

	// Cancel booking
	cancelBooking: async (id) => {
		try {
			const response = await api.patch(`/api/bookings/admin/${id}/cancel`);
			return response.data;
		} catch (error) {
			throw {
				message: error.response?.data?.message || 'Failed to cancel booking',
				status: error.response?.status,
			};
		}
	},

	// Delete booking (admin only)
	deleteBooking: async (id) => {
		try {
			const response = await api.delete(`/api/bookings/admin/${id}`);
			return response.data;
		} catch (error) {
			throw {
				message: error.response?.data?.message || 'Failed to delete booking',
				status: error.response?.status,
			};
		}
	},

	// Get available time slots
	getAvailableSlots: async (params = {}) => {
		try {
			const { date, startDate, endDate } = params;

			const queryParams = createQueryString({
				date,
				startDate,
				endDate,
			});

			const url = `/api/bookings/available${
				queryParams ? `?${queryParams}` : ''
			}`;
			const response = await api.get(url);
			return response.data;
		} catch (error) {
			throw {
				message:
					error.response?.data?.message || 'Failed to fetch available slots',
				status: error.response?.status,
			};
		}
	},

	// Get user's bookings by email
	getUserBookings: async (email) => {
		try {
			const response = await api.get(
				`/api/bookings/user/${encodeURIComponent(email)}`,
			);
			return response.data;
		} catch (error) {
			throw {
				message:
					error.response?.data?.message || 'Failed to fetch user bookings',
				status: error.response?.status,
			};
		}
	},

	// Utility methods for common booking operations

	// Get today's bookings
	getTodayBookings: async () => {
		try {
			const today = new Date();
			today.setHours(0, 0, 0, 0);
			const tomorrow = new Date(today);
			tomorrow.setDate(tomorrow.getDate() + 1);

			return await bookingService.getAvailableSlots({
				startDate: today.toISOString(),
				endDate: tomorrow.toISOString(),
			});
		} catch (error) {
			throw error;
		}
	},

	// Get bookings for a specific date range
	getBookingsByDateRange: async (startDate, endDate, additionalParams = {}) => {
		try {
			return await bookingService.getAllBookings({
				...additionalParams,
				startDate,
				endDate,
			});
		} catch (error) {
			throw error;
		}
	},

	// Get pending bookings (scheduled status)
	getPendingBookings: async (params = {}) => {
		try {
			return await bookingService.getAllBookings({
				...params,
				status: 'scheduled',
			});
		} catch (error) {
			throw error;
		}
	},

	// Get completed bookings
	getCompletedBookings: async (params = {}) => {
		try {
			return await bookingService.getAllBookings({
				...params,
				status: 'completed',
			});
		} catch (error) {
			throw error;
		}
	},

	// Get cancelled bookings
	getCancelledBookings: async (params = {}) => {
		try {
			return await bookingService.getAllBookings({
				...params,
				status: 'cancelled',
			});
		} catch (error) {
			throw error;
		}
	},

	// Mark booking as completed
	markAsCompleted: async (id) => {
		try {
			return await bookingService.updateBookingStatus(id, 'completed');
		} catch (error) {
			throw error;
		}
	},

	// Reschedule booking (update date)
	rescheduleBooking: async (id, newDate) => {
		try {
			// Note: You might need to add this endpoint to your backend
			const response = await api.patch(`/api/bookings/admin/${id}`, {
				date: newDate,
			});
			return response.data;
		} catch (error) {
			throw {
				message:
					error.response?.data?.message || 'Failed to reschedule booking',
				status: error.response?.status,
			};
		}
	},

	// Search bookings by multiple criteria
	searchBookings: async (searchTerm, params = {}) => {
		try {
			// Search by email or booked by name
			const searchPromises = [];

			if (searchTerm) {
				// Search by email
				searchPromises.push(
					bookingService.getAllBookings({
						...params,
						email: searchTerm,
					}),
				);

				// Search by name
				searchPromises.push(
					bookingService.getAllBookings({
						...params,
						bookedBy: searchTerm,
					}),
				);
			}

			if (searchPromises.length === 0) {
				return await bookingService.getAllBookings(params);
			}

			const results = await Promise.allSettled(searchPromises);
			const successfulResults = results
				.filter((result) => result.status === 'fulfilled')
				.map((result) => result.value);

			// Combine and deduplicate results
			const combinedBookings = [];
			const seenIds = new Set();

			successfulResults.forEach((result) => {
				if (result.data && Array.isArray(result.data)) {
					result.data.forEach((booking) => {
						if (!seenIds.has(booking._id)) {
							seenIds.add(booking._id);
							combinedBookings.push(booking);
						}
					});
				}
			});

			return {
				success: true,
				data: combinedBookings,
				pagination: successfulResults[0]?.pagination || {},
			};
		} catch (error) {
			throw {
				message: 'Failed to search bookings',
				status: error.status,
			};
		}
	},

	// Get booking statistics
	getBookingStats: async (params = {}) => {
		try {
			const allBookings = await bookingService.getAllBookings({
				...params,
				limit: 1000, // Get a large number to calculate stats
			});

			const bookings = allBookings.data || [];

			const stats = {
				total: bookings.length,
				scheduled: bookings.filter((b) => b.status === 'scheduled').length,
				completed: bookings.filter((b) => b.status === 'completed').length,
				cancelled: bookings.filter((b) => b.status === 'cancelled').length,
				sessionTypes: {
					Discovery: bookings.filter((b) => b.sessionType === 'Discovery')
						.length,
					Transformation: bookings.filter(
						(b) => b.sessionType === 'Transformation',
					).length,
					'Follow-up': bookings.filter((b) => b.sessionType === 'Follow-up')
						.length,
				},
			};

			return { success: true, data: stats };
		} catch (error) {
			throw {
				message: 'Failed to get booking statistics',
				status: error.status,
			};
		}
	},
};
