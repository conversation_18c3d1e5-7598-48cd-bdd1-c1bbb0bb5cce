/**
 * Notifications API service
 */
import { api } from './client';

export const notificationsService = {
  /**
   * Subscribe to push notifications
   * @param {Object} subscriptionData - Subscription data
   * @param {Object} subscriptionData.subscription - Push subscription object
   * @param {string} subscriptionData.userAgent - User agent string
   * @returns {Promise} API response
   */
  subscribe: async (subscriptionData) => {
    const response = await api.post('/api/notification/subscribe', subscriptionData);
    return response.data;
  },

  /**
   * Unsubscribe from push notifications
   * @param {Object} unsubscribeData - Unsubscribe data
   * @param {string} unsubscribeData.endpoint - Subscription endpoint
   * @returns {Promise} API response
   */
  unsubscribe: async (unsubscribeData) => {
    const response = await api.post('/api/notification/unsubscribe', unsubscribeData);
    return response.data;
  },

  /**
   * Send notification to specific user (admin only)
   * @param {Object} notificationData - Notification data
   * @param {string} notificationData.userId - Target user ID
   * @param {string} notificationData.title - Notification title
   * @param {string} notificationData.body - Notification body
   * @param {string} notificationData.icon - Notification icon URL
   * @param {string} notificationData.url - Click action URL
   * @returns {Promise} API response
   */
  sendNotification: async (notificationData) => {
    const response = await api.post('/api/notification/send-notification', notificationData);
    return response.data;
  },

  /**
   * Broadcast notification to all subscribers (admin only)
   * @param {Object} notificationData - Notification data
   * @param {string} notificationData.title - Notification title
   * @param {string} notificationData.body - Notification body
   * @param {string} notificationData.icon - Notification icon URL
   * @param {string} notificationData.url - Click action URL
   * @returns {Promise} API response
   */
  broadcastNotification: async (notificationData) => {
    const response = await api.post('/api/notification/broadcast', notificationData);
    return response.data;
  },

  /**
   * Get user's notification history
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number
   * @param {number} params.limit - Items per page
   * @returns {Promise} API response
   */
  getNotificationHistory: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `/api/notification/history?${queryString}` : '/api/notification/history';
    const response = await api.get(url);
    return response.data;
  },

  /**
   * Mark notification as read
   * @param {string} notificationId - Notification ID
   * @returns {Promise} API response
   */
  markAsRead: async (notificationId) => {
    const response = await api.patch(`/api/notification/${notificationId}/read`);
    return response.data;
  },

  /**
   * Get notification settings
   * @returns {Promise} API response
   */
  getSettings: async () => {
    const response = await api.get('/api/notification/settings');
    return response.data;
  },

  /**
   * Update notification settings
   * @param {Object} settings - Notification settings
   * @param {boolean} settings.pushEnabled - Enable push notifications
   * @param {boolean} settings.emailEnabled - Enable email notifications
   * @param {Array} settings.categories - Enabled notification categories
   * @returns {Promise} API response
   */
  updateSettings: async (settings) => {
    const response = await api.put('/api/notification/settings', settings);
    return response.data;
  },
};
