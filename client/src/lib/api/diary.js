/**
 * Diary API service
 */
import { api, createQueryString } from './client';

export const diaryService = {
  /**
   * Create a new diary entry
   * @param {Object} entryData - Diary entry data
   * @param {string} entryData.title - Entry title
   * @param {string} entryData.content - Entry content
   * @param {string} entryData.mood - User's mood
   * @param {Array} entryData.images - Array of image URLs
   * @param {string} entryData.date - Entry date
   * @returns {Promise} API response
   */
  createEntry: async (entryData) => {
    const response = await api.post('/api/diary', entryData);
    return response.data;
  },

  /**
   * Get all diary entries for user
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number
   * @param {number} params.limit - Items per page
   * @param {string} params.startDate - Start date filter
   * @param {string} params.endDate - End date filter
   * @returns {Promise} API response
   */
  getEntries: async (params = {}) => {
    const queryString = createQueryString(params);
    const url = queryString ? `/api/diary?${queryString}` : '/api/diary';
    const response = await api.get(url);
    return response.data;
  },

  /**
   * Get recent diary entries
   * @param {number} limit - Number of entries to fetch
   * @returns {Promise} API response
   */
  getRecentEntries: async (limit = 5) => {
    const response = await api.get(`/api/diary/recent?limit=${limit}`);
    return response.data;
  },

  /**
   * Get diary entry by ID
   * @param {string} id - Entry ID
   * @returns {Promise} API response
   */
  getEntryById: async (id) => {
    const response = await api.get(`/api/diary/${id}`);
    return response.data;
  },

  /**
   * Get diary entry by date
   * @param {string} date - Date in YYYY-MM-DD format
   * @returns {Promise} API response or null if not found
   */
  getEntryByDate: async (date) => {
    try {
      const response = await api.get(`/api/diary/date/${date}`);
      return response.data;
    } catch (error) {
      // Return null if no entry found for this date (404)
      if (error.response?.status === 404) {
        return null;
      }
      throw error;
    }
  },

  /**
   * Update diary entry
   * @param {string} id - Entry ID
   * @param {Object} entryData - Updated entry data
   * @returns {Promise} API response
   */
  updateEntry: async (id, entryData) => {
    const response = await api.put(`/api/diary/${id}`, entryData);
    return response.data;
  },

  /**
   * Delete diary entry
   * @param {string} id - Entry ID
   * @returns {Promise} API response
   */
  deleteEntry: async (id) => {
    const response = await api.delete(`/api/diary/${id}`);
    return response.data;
  },

  /**
   * Upload image for diary entry
   * @param {FormData} formData - Form data with image file
   * @returns {Promise} API response
   */
  uploadImage: async (formData) => {
    const response = await api.post('/api/diary/upload-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  /**
   * Get diary statistics
   * @param {Object} params - Query parameters
   * @param {string} params.startDate - Start date for stats
   * @param {string} params.endDate - End date for stats
   * @returns {Promise} API response
   */
  getStats: async (params = {}) => {
    const queryString = createQueryString(params);
    const url = queryString ? `/api/diary/stats?${queryString}` : '/api/diary/stats';
    const response = await api.get(url);
    return response.data;
  },

  /**
   * Search diary entries
   * @param {string} query - Search query
   * @param {Object} filters - Additional filters
   * @returns {Promise} API response
   */
  searchEntries: async (query, filters = {}) => {
    const params = { q: query, ...filters };
    const queryString = createQueryString(params);
    const response = await api.get(`/api/diary/search?${queryString}`);
    return response.data;
  },
};
