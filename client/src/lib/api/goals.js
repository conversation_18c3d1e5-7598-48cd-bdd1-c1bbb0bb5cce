/**
 * Goals API service
 */
import { api, createQueryString } from './client';

export const goalsService = {
  /**
   * Get user's goals
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number
   * @param {number} params.limit - Items per page
   * @param {string} params.status - Filter by status (active, completed, paused)
   * @param {string} params.category - Filter by category
   * @returns {Promise} API response
   */
  getUserGoals: async (params = {}) => {
    const queryString = createQueryString(params);
    const url = queryString ? `/api/goals?${queryString}` : '/api/goals';
    const response = await api.get(url);
    return response.data;
  },

  /**
   * Get public goals
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number
   * @param {number} params.limit - Items per page
   * @returns {Promise} API response
   */
  getPublicGoals: async (params = {}) => {
    const queryString = createQueryString(params);
    const url = queryString ? `/api/goals/public?${queryString}` : '/api/goals/public';
    const response = await api.get(url);
    return response.data;
  },

  /**
   * Get goal by ID
   * @param {string} id - Goal ID
   * @returns {Promise} API response
   */
  getGoalById: async (id) => {
    const response = await api.get(`/api/goals/${id}`);
    return response.data;
  },

  /**
   * Create new goal
   * @param {Object} goalData - Goal data
   * @param {string} goalData.title - Goal title
   * @param {string} goalData.description - Goal description
   * @param {string} goalData.category - Goal category
   * @param {string} goalData.targetDate - Target completion date
   * @param {boolean} goalData.isPublic - Whether goal is public
   * @returns {Promise} API response
   */
  createGoal: async (goalData) => {
    const response = await api.post('/api/goals', goalData);
    return response.data;
  },

  /**
   * Update goal
   * @param {string} id - Goal ID
   * @param {Object} goalData - Updated goal data
   * @returns {Promise} API response
   */
  updateGoal: async (id, goalData) => {
    const response = await api.put(`/api/goals/${id}`, goalData);
    return response.data;
  },

  /**
   * Delete goal
   * @param {string} id - Goal ID
   * @returns {Promise} API response
   */
  deleteGoal: async (id) => {
    const response = await api.delete(`/api/goals/${id}`);
    return response.data;
  },

  /**
   * Add task to goal
   * @param {string} goalId - Goal ID
   * @param {Object} taskData - Task data
   * @param {string} taskData.title - Task title
   * @param {string} taskData.description - Task description
   * @param {string} taskData.dueDate - Task due date
   * @returns {Promise} API response
   */
  addTask: async (goalId, taskData) => {
    const response = await api.post(`/api/goals/${goalId}/tasks`, taskData);
    return response.data;
  },

  /**
   * Update task
   * @param {string} goalId - Goal ID
   * @param {string} taskId - Task ID
   * @param {Object} taskData - Updated task data
   * @returns {Promise} API response
   */
  updateTask: async (goalId, taskId, taskData) => {
    const response = await api.put(`/api/goals/${goalId}/tasks/${taskId}`, taskData);
    return response.data;
  },

  /**
   * Delete task
   * @param {string} goalId - Goal ID
   * @param {string} taskId - Task ID
   * @returns {Promise} API response
   */
  deleteTask: async (goalId, taskId) => {
    const response = await api.delete(`/api/goals/${goalId}/tasks/${taskId}`);
    return response.data;
  },

  /**
   * Mark task as completed
   * @param {string} goalId - Goal ID
   * @param {string} taskId - Task ID
   * @returns {Promise} API response
   */
  completeTask: async (goalId, taskId) => {
    const response = await api.patch(`/api/goals/${goalId}/tasks/${taskId}/complete`);
    return response.data;
  },

  /**
   * Get goal statistics
   * @returns {Promise} API response
   */
  getGoalStats: async () => {
    const response = await api.get('/api/goals/stats');
    return response.data;
  },
};
