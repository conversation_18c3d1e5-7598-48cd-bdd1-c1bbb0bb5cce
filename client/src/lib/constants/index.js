/**
 * Application constants
 */

// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:5000',
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
};

// Authentication
export const AUTH_CONFIG = {
  TOKEN_STORAGE_KEY: 'accessToken',
  REFRESH_TOKEN_STORAGE_KEY: 'refreshToken',
  GOOGLE_CLIENT_ID: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
};

// Cache Times (in milliseconds)
export const CACHE_TIMES = {
  SHORT: 30 * 1000, // 30 seconds
  MEDIUM: 5 * 60 * 1000, // 5 minutes
  LONG: 30 * 60 * 1000, // 30 minutes
  VERY_LONG: 60 * 60 * 1000, // 1 hour
};

// Query Keys
export const QUERY_KEYS = {
  USER: 'user',
  USER_PROFILE: ['user', 'profile'],
  USERS_ALL: 'users',
  LESSONS: 'lessons',
  LESSON: 'lesson',
  LESSON_CATEGORIES: ['lesson-categories'],
  USER_PROGRESS: 'userProgress',
  USER_PROGRESS_ALL: ['userProgress', 'all'],
  DIARY: 'diary',
  DIARY_RECENT: ['diary', 'recent'],
  GOALS: 'goals',
  VALUES: 'values',
  NOTIFICATIONS: 'notifications',
};

// User Roles
export const USER_ROLES = {
  USER: 'user',
  TUTOR: 'tutor',
  ADMIN: 'admin',
  SUPER_ADMIN: 'superadmin',
};

// Lesson Categories
export const LESSON_CATEGORIES = [
  'Personal Development',
  'Career Growth',
  'Health & Wellness',
  'Relationships',
  'Financial Literacy',
  'Mindfulness',
  'Leadership',
  'Communication',
  'Goal Setting',
  'Time Management',
];

// Mood Options for Diary
export const MOOD_OPTIONS = [
  { value: 'excellent', label: 'Excellent', emoji: '😄', color: '#10B981' },
  { value: 'good', label: 'Good', emoji: '😊', color: '#3B82F6' },
  { value: 'okay', label: 'Okay', emoji: '😐', color: '#F59E0B' },
  { value: 'bad', label: 'Bad', emoji: '😔', color: '#EF4444' },
  { value: 'terrible', label: 'Terrible', emoji: '😢', color: '#DC2626' },
];

// Notification Types
export const NOTIFICATION_TYPES = {
  INFO: 'info',
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error',
};

// File Upload
export const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_VIDEO_TYPES: ['video/mp4', 'video/webm', 'video/ogg'],
  CLOUDINARY_UPLOAD_PRESET: process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET,
  CLOUDINARY_CLOUD_NAME: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
};

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 10,
  MAX_LIMIT: 100,
};

// Local Storage Keys
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'accessToken',
  REFRESH_TOKEN: 'refreshToken',
  USER_PREFERENCES: 'userPreferences',
  THEME: 'theme',
  NOTIFICATION_SUBSCRIBED: 'notification-subscribed',
  NOTIFICATION_ENDPOINT: 'notification-endpoint',
  VIDEO_PROGRESS: 'videoProgress',
};

// Routes
export const ROUTES = {
  HOME: '/',
  LOGIN: '/auth/login',
  SIGNUP: '/auth/signup',
  FORGOT_PASSWORD: '/auth/forgot-password',
  RESET_PASSWORD: '/auth/reset-password',
  PROFILE: '/profile',
  LESSONS: '/lessons',
  DIARY: '/diary',
  GOALS: '/goals',
  VALUES: '/values',
  REFLECTION: '/reflection',
  ADMIN: '/admin',
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'Access denied.',
  NOT_FOUND: 'The requested resource was not found.',
  SERVER_ERROR: 'Server error. Please try again later.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  RATE_LIMIT: 'Too many requests. Please try again later.',
};

// Success Messages
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Successfully logged in!',
  LOGOUT_SUCCESS: 'Successfully logged out!',
  REGISTRATION_SUCCESS: 'Account created successfully!',
  UPDATE_SUCCESS: 'Updated successfully!',
  DELETE_SUCCESS: 'Deleted successfully!',
  UPLOAD_SUCCESS: 'Upload completed successfully!',
};
