# Authentication Hooks Usage

This document provides examples of how to use the authentication hooks with TanStack Query.

## Setup

1. Make sure TanStack Query is properly configured in your app layout
2. Import the hooks from `@/hooks/useAuth`

## Available Hooks

### 1. useRegister
Register a new user account:

```jsx
import { useRegister } from '@/hooks/useAuth';

const SignupComponent = () => {
  const { mutate: register, isPending, error } = useRegister();

  const handleSignup = (formData) => {
    register({
      name: formData.fullName,
      email: formData.email,
      password: formData.password
    }, {
      onSuccess: (data) => {
        console.log('Registration successful:', data);
        // Redirect to email verification
        router.push(`/auth/signup/verify-otp?email=${formData.email}`);
      },
      onError: (error) => {
        console.error('Registration failed:', error);
      }
    });
  };

  return (
    <form onSubmit={(e) => {
      e.preventDefault();
      handleSignup(formData);
    }}>
      {/* Form fields */}
      <button type="submit" disabled={isPending}>
        {isPending ? 'Creating Account...' : 'Sign Up'}
      </button>
      {error && <div className="error">{error.response?.data?.message}</div>}
    </form>
  );
};
```

### 2. useVerifyEmail
Verify email with OTP:

```jsx
import { useVerifyEmail } from '@/hooks/useAuth';

const VerifyEmailComponent = () => {
  const { mutate: verifyEmail, isPending, error } = useVerifyEmail();

  const handleVerification = (email, otp) => {
    verifyEmail({ email, otp }, {
      onSuccess: (data) => {
        console.log('Email verified:', data);
        // Tokens are automatically stored
        router.push('/dashboard');
      },
      onError: (error) => {
        console.error('Verification failed:', error);
      }
    });
  };

  return (
    <form onSubmit={(e) => {
      e.preventDefault();
      handleVerification(email, otpValue);
    }}>
      {/* OTP input fields */}
      <button type="submit" disabled={isPending}>
        {isPending ? 'Verifying...' : 'Verify'}
      </button>
      {error && <div className="error">{error.response?.data?.message}</div>}
    </form>
  );
};
```

### 3. useLogin
Login user:

```jsx
import { useLogin } from '@/hooks/useAuth';

const LoginComponent = () => {
  const { mutate: login, isPending, error } = useLogin();

  const handleLogin = (formData) => {
    login({
      email: formData.email,
      password: formData.password
    }, {
      onSuccess: (data) => {
        console.log('Login successful:', data);
        // Tokens are automatically stored
        router.push('/dashboard');
      },
      onError: (error) => {
        console.error('Login failed:', error);
      }
    });
  };

  return (
    <form onSubmit={(e) => {
      e.preventDefault();
      handleLogin(formData);
    }}>
      {/* Form fields */}
      <button type="submit" disabled={isPending}>
        {isPending ? 'Signing in...' : 'Sign In'}
      </button>
      {error && <div className="error">{error.response?.data?.message}</div>}
    </form>
  );
};
```

### 4. useForgotPassword
Initiate password reset:

```jsx
import { useForgotPassword } from '@/hooks/useAuth';

const ForgotPasswordComponent = () => {
  const { mutate: forgotPassword, isPending, error } = useForgotPassword();

  const handleForgotPassword = (email) => {
    forgotPassword({ email }, {
      onSuccess: (data) => {
        console.log('Password reset code sent:', data);
        router.push(`/auth/reset-password?email=${email}`);
      },
      onError: (error) => {
        console.error('Failed to send reset code:', error);
      }
    });
  };

  return (
    <form onSubmit={(e) => {
      e.preventDefault();
      handleForgotPassword(email);
    }}>
      {/* Email input */}
      <button type="submit" disabled={isPending}>
        {isPending ? 'Sending...' : 'Send Reset Code'}
      </button>
      {error && <div className="error">{error.response?.data?.message}</div>}
    </form>
  );
};
```

### 5. useVerifyPasswordResetOTP
Verify password reset OTP:

```jsx
import { useVerifyPasswordResetOTP } from '@/hooks/useAuth';

const VerifyResetOTPComponent = () => {
  const { mutate: verifyResetOTP, isPending, error } = useVerifyPasswordResetOTP();

  const handleVerifyResetOTP = (email, otp) => {
    verifyResetOTP({ email, otp }, {
      onSuccess: (data) => {
        console.log('OTP verified:', data);
        // Store reset token if needed
        router.push(`/auth/new-password?email=${email}&token=${data.data.resetToken}`);
      },
      onError: (error) => {
        console.error('OTP verification failed:', error);
      }
    });
  };

  return (
    <form onSubmit={(e) => {
      e.preventDefault();
      handleVerifyResetOTP(email, otpValue);
    }}>
      {/* OTP input */}
      <button type="submit" disabled={isPending}>
        {isPending ? 'Verifying...' : 'Verify OTP'}
      </button>
      {error && <div className="error">{error.response?.data?.message}</div>}
    </form>
  );
};
```

### 6. useResetPassword
Reset password with new password:

```jsx
import { useResetPassword } from '@/hooks/useAuth';

const ResetPasswordComponent = () => {
  const { mutate: resetPassword, isPending, error } = useResetPassword();

  const handleResetPassword = (email, newPassword) => {
    resetPassword({ email, newPassword }, {
      onSuccess: (data) => {
        console.log('Password reset successful:', data);
        router.push('/auth/login');
      },
      onError: (error) => {
        console.error('Password reset failed:', error);
      }
    });
  };

  return (
    <form onSubmit={(e) => {
      e.preventDefault();
      handleResetPassword(email, newPassword);
    }}>
      {/* Password input fields */}
      <button type="submit" disabled={isPending}>
        {isPending ? 'Resetting...' : 'Reset Password'}
      </button>
      {error && <div className="error">{error.response?.data?.message}</div>}
    </form>
  );
};
```

## Utility Functions

### Authentication Status
```jsx
import { isAuthenticated, getStoredTokens, logout } from '@/hooks/useAuth';

// Check if user is authenticated
if (isAuthenticated()) {
  console.log('User is logged in');
}

// Get stored tokens
const { accessToken, refreshToken } = getStoredTokens();

// Logout user
const handleLogout = () => {
  logout(); // Clears tokens and redirects to login
};
```

## Error Handling

All hooks return an `error` object that contains the error response from the API. You can access the error message via:

```jsx
error.response?.data?.message || 'Default error message'
```

## Loading States

All mutation hooks return an `isPending` boolean that indicates if the request is in progress. Use this to show loading states and disable form submissions.

## Token Management

Tokens are automatically stored in localStorage when login or email verification succeeds. The authorization header is also automatically set for subsequent API requests.
