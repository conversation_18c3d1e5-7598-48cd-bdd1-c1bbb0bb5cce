import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { isAuthenticated, logout } from './useAuth';
import { useCallback, useRef } from 'react';
import { setAuthToken } from '../api/client';
import { userProgressService } from '../api';

// Get user progress for a specific lesson
export const useUserProgress = (lessonId) => {
	return useQuery({
		queryKey: ['userProgress', lessonId],
		queryFn: async () => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await userProgressService.getProgress(lessonId);
		},
		enabled: isAuthenticated() && !!lessonId,
		staleTime: 30 * 1000, // 30 seconds
		gcTime: 5 * 60 * 1000, // 5 minutes
		retry: (failureCount, error) => {
			if (error?.response?.status === 401) {
				return false;
			}
			return failureCount < 3;
		},
		meta: {
			onError: (error) => {
				console.error('Error fetching user progress:', error);
				if (error?.response?.status === 401) {
					logout();
					if (typeof window !== 'undefined') {
						window.dispatchEvent(new Event('auth-change'));
					}
				}
			},
		},
	});
};

// Get all user progress (for ongoing videos)
export const useAllUserProgress = () => {
	return useQuery({
		queryKey: ['userProgress', 'all'],
		queryFn: async () => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await userProgressService.getAllProgress();
		},
		enabled: isAuthenticated(),
		staleTime: 60 * 1000, // 1 minute
		gcTime: 10 * 60 * 1000, // 10 minutes
		retry: (failureCount, error) => {
			if (error?.response?.status === 401) {
				return false;
			}
			return failureCount < 3;
		},
		meta: {
			onError: (error) => {
				console.error('Error fetching all user progress:', error);
				if (error?.response?.status === 401) {
					logout();
					if (typeof window !== 'undefined') {
						window.dispatchEvent(new Event('auth-change'));
					}
				}
			},
		},
	});
};

// Optimized progress update hook with smart batching
export const useUpdateUserProgress = () => {
	const queryClient = useQueryClient();
	const pendingUpdatesRef = useRef(new Map());
	const timeoutRef = useRef(null);

	const mutation = useMutation({
		mutationFn: async (progressData) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await userProgressService.updateProgress(progressData);
		},
		onSuccess: (data, variables) => {
			queryClient.invalidateQueries({ queryKey: ['userProgress'] });
			console.log('User progress updated successfully:', data);
		},
		onError: (error) => {
			console.error('Error updating user progress:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});

	// Smart progress updater that batches updates
	const updateProgress = useCallback(
		(progressData) => {
			const { lessonId } = progressData;

			// Store the latest progress data for this lesson
			pendingUpdatesRef.current.set(lessonId, progressData);

			// Always update localStorage immediately for instant feedback
			if (progressData.videoProgress !== undefined) {
				localStorage.setItem(
					`lesson_progress_${lessonId}`,
					progressData.videoProgress.toString(),
				);
			}

			// Clear existing timeout
			if (timeoutRef.current) {
				clearTimeout(timeoutRef.current);
			}

			// Set new timeout to batch the server update
			timeoutRef.current = setTimeout(() => {
				const updates = Array.from(pendingUpdatesRef.current.values());
				pendingUpdatesRef.current.clear();

				// Send all pending updates to server
				updates.forEach((update) => {
					mutation.mutate(update);
				});
			}, 30000); // Wait 30 seconds before sending to server
		},
		[mutation],
	);

	// Force immediate server update (for critical moments)
	const forceUpdateProgress = useCallback(
		(progressData) => {
			// Clear any pending timeouts
			if (timeoutRef.current) {
				clearTimeout(timeoutRef.current);
			}

			// Update localStorage immediately
			if (progressData.videoProgress !== undefined) {
				localStorage.setItem(
					`lesson_progress_${progressData.lessonId}`,
					progressData.videoProgress.toString(),
				);
			}

			// Send to server immediately
			mutation.mutate(progressData);

			// Clear any pending updates for this lesson
			pendingUpdatesRef.current.delete(progressData.lessonId);
		},
		[mutation],
	);

	// Cleanup function to flush pending updates
	const flushPendingUpdates = useCallback(() => {
		if (timeoutRef.current) {
			clearTimeout(timeoutRef.current);
		}

		const updates = Array.from(pendingUpdatesRef.current.values());
		pendingUpdatesRef.current.clear();

		updates.forEach((update) => {
			mutation.mutate(update);
		});
	}, [mutation]);

	return {
		updateProgress,
		forceUpdateProgress,
		flushPendingUpdates,
		isLoading: mutation.isLoading,
		error: mutation.error,
	};
};

// Update reflection mutation
export const useUpdateReflection = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (reflectionData) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await userProgressService.updateReflection(reflectionData);
		},
		onSuccess: (data, variables) => {
			// Invalidate user progress queries
			queryClient.invalidateQueries({
				queryKey: ['userProgress', variables.lessonId],
			});
			console.log('Reflection updated successfully:', data);
		},
		onError: (error) => {
			console.error('Error updating reflection:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};

// Delete reflection mutation
export const useDeleteReflection = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ lessonId, questionIndex }) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await userProgressService.deleteReflection(
				lessonId,
				questionIndex,
			);
		},
		onSuccess: (data, variables) => {
			// Invalidate user progress queries
			queryClient.invalidateQueries({
				queryKey: ['userProgress', variables.lessonId],
			});
			console.log('Reflection deleted successfully:', data);
		},
		onError: (error) => {
			console.error('Error deleting reflection:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};
