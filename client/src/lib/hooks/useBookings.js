import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { bookingService } from '../api/booking';

// Hook to get bookings with pagination and filters
export const useBookings = (params = {}) => {
	return useQuery({
		queryKey: ['bookings', params],
		queryFn: () => bookingService.getAllBookings(params),
		staleTime: 2 * 60 * 1000, // 2 minutes (bookings change more frequently)
		cacheTime: 5 * 60 * 1000, // 5 minutes
		keepPreviousData: true, // Keep previous data while loading new data
	});
};

// Hook to get single booking by ID
export const useBooking = (id) => {
	return useQuery({
		queryKey: ['booking', id],
		queryFn: () => bookingService.getBookingById(id),
		enabled: !!id, // Only fetch if id exists
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};

// Hook to get user's bookings by email
export const useUserBookings = (email) => {
	return useQuery({
		queryKey: ['user-bookings', email],
		queryFn: () => bookingService.getUserBookings(email),
		enabled: !!email, // Only fetch if email exists
		staleTime: 2 * 60 * 1000, // 2 minutes
	});
};

// Hook to get available slots
export const useAvailableSlots = (params = {}) => {
	return useQuery({
		queryKey: ['available-slots', params],
		queryFn: () => bookingService.getAvailableSlots(params),
		staleTime: 1 * 60 * 1000, // 1 minute (availability changes frequently)
		cacheTime: 2 * 60 * 1000, // 2 minutes
		enabled: !!(params.date || (params.startDate && params.endDate)), // Only fetch if date params exist
	});
};

// Hook to get booking statistics
export const useBookingStats = (params = {}) => {
	return useQuery({
		queryKey: ['booking-stats', params],
		queryFn: () => bookingService.getBookingStats(params),
		staleTime: 5 * 60 * 1000, // 5 minutes
		cacheTime: 10 * 60 * 1000, // 10 minutes
	});
};

// Hook to create new booking
export const useCreateBooking = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (bookingData) => {
			try {
				const response = await bookingService.createBooking(bookingData);
				return response;
			} catch (error) {
				// Re-throw the original error
				throw error;
			}
		},
		retry: (failureCount, error) => {
			// Don't retry on client errors (4xx) or specific server errors
			if (error?.response?.status >= 400 && error?.response?.status < 500) {
				return false;
			}

			// Don't retry on timeout errors
			if (error?.message?.includes('timeout') || error?.name === 'AbortError') {
				return false;
			}

			// Retry up to 2 times for other errors
			return failureCount < 2;
		},
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
		onSuccess: (data, variables) => {
			// Invalidate bookings queries to refetch data
			queryClient.invalidateQueries({ queryKey: ['bookings'] });

			// Invalidate available slots for the booked date
			const bookingDate = new Date(variables.date);
			queryClient.invalidateQueries({
				queryKey: ['available-slots'],
				predicate: (query) => {
					const params = query.queryKey[1];
					if (params?.date) {
						const queryDate = new Date(params.date);
						return queryDate.toDateString() === bookingDate.toDateString();
					}
					return true; // Invalidate all if no specific date
				},
			});

			// Invalidate user bookings
			if (variables.email) {
				queryClient.invalidateQueries({
					queryKey: ['user-bookings', variables.email],
				});
			}

			// Invalidate stats
			queryClient.invalidateQueries({ queryKey: ['booking-stats'] });
		},
		onError: (error) => {
			console.error('Error creating booking:', error);
		},
	});
};

// Hook to update booking status
export const useUpdateBookingStatus = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({ id, status }) => {
			return bookingService.updateBookingStatus(id, status);
		},
		retry: (failureCount, error) => {
			// Don't retry on client errors
			if (error?.response?.status >= 400 && error?.response?.status < 500) {
				return false;
			}
			return failureCount < 2;
		},
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
		onSuccess: (data, variables) => {
			// Update specific booking cache optimistically
			queryClient.setQueryData(['booking', variables.id], (oldData) => {
				if (oldData?.data) {
					return {
						...oldData,
						data: {
							...oldData.data,
							status: variables.status,
						},
					};
				}
				return oldData;
			});

			// Invalidate bookings queries
			queryClient.invalidateQueries({ queryKey: ['bookings'] });

			// Invalidate specific booking
			queryClient.invalidateQueries({ queryKey: ['booking', variables.id] });

			// Invalidate stats
			queryClient.invalidateQueries({ queryKey: ['booking-stats'] });
		},
		onError: (error) => {
			console.error('Error updating booking status:', error);
		},
	});
};

// Hook to cancel booking
export const useCancelBooking = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (id) => {
			return bookingService.cancelBooking(id);
		},
		retry: (failureCount, error) => {
			// Don't retry on client errors
			if (error?.response?.status >= 400 && error?.response?.status < 500) {
				return false;
			}
			return failureCount < 2;
		},
		onSuccess: (data, variables) => {
			// Update specific booking cache optimistically
			queryClient.setQueryData(['booking', variables], (oldData) => {
				if (oldData?.data) {
					return {
						...oldData,
						data: {
							...oldData.data,
							status: 'cancelled',
						},
					};
				}
				return oldData;
			});

			// Invalidate bookings queries
			queryClient.invalidateQueries({ queryKey: ['bookings'] });

			// Invalidate specific booking
			queryClient.invalidateQueries({ queryKey: ['booking', variables] });

			// Invalidate available slots (cancelled booking becomes available)
			queryClient.invalidateQueries({ queryKey: ['available-slots'] });

			// Invalidate stats
			queryClient.invalidateQueries({ queryKey: ['booking-stats'] });
		},
		onError: (error) => {
			console.error('Error cancelling booking:', error);
		},
	});
};

// Hook to delete booking
export const useDeleteBooking = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: bookingService.deleteBooking,
		retry: (failureCount, error) => {
			// Don't retry on client errors
			if (error?.response?.status >= 400 && error?.response?.status < 500) {
				return false;
			}
			return failureCount < 2;
		},
		onSuccess: (data, variables) => {
			// Remove from bookings cache optimistically
			queryClient.setQueriesData({ queryKey: ['bookings'] }, (oldData) => {
				if (oldData?.data) {
					return {
						...oldData,
						data: oldData.data.filter((booking) => booking._id !== variables),
					};
				}
				return oldData;
			});

			// Invalidate bookings queries to refetch data
			queryClient.invalidateQueries({ queryKey: ['bookings'] });

			// Remove specific booking from cache
			queryClient.removeQueries({ queryKey: ['booking', variables] });

			// Invalidate available slots
			queryClient.invalidateQueries({ queryKey: ['available-slots'] });

			// Invalidate stats
			queryClient.invalidateQueries({ queryKey: ['booking-stats'] });
		},
		onError: (error) => {
			console.error('Error deleting booking:', error);
		},
	});
};

// Hook to reschedule booking
export const useRescheduleBooking = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({ id, newDate }) => {
			return bookingService.rescheduleBooking(id, newDate);
		},
		retry: (failureCount, error) => {
			// Don't retry on client errors
			if (error?.response?.status >= 400 && error?.response?.status < 500) {
				return false;
			}
			return failureCount < 2;
		},
		onSuccess: (data, variables) => {
			// Invalidate bookings queries
			queryClient.invalidateQueries({ queryKey: ['bookings'] });

			// Invalidate specific booking
			queryClient.invalidateQueries({ queryKey: ['booking', variables.id] });

			// Invalidate available slots for both old and new dates
			queryClient.invalidateQueries({ queryKey: ['available-slots'] });
		},
		onError: (error) => {
			console.error('Error rescheduling booking:', error);
		},
	});
};

// Hook to search bookings
export const useSearchBookings = (searchTerm, params = {}) => {
	return useQuery({
		queryKey: ['search-bookings', searchTerm, params],
		queryFn: () => bookingService.searchBookings(searchTerm, params),
		enabled: !!searchTerm && searchTerm.length >= 2, // Only search if term is at least 2 characters
		staleTime: 1 * 60 * 1000, // 1 minute
		cacheTime: 5 * 60 * 1000, // 5 minutes
	});
};

// Utility hooks for specific booking queries

// Hook to get today's bookings
export const useTodayBookings = () => {
	const today = new Date();
	today.setHours(0, 0, 0, 0);
	const tomorrow = new Date(today);
	tomorrow.setDate(tomorrow.getDate() + 1);

	return useAvailableSlots({
		startDate: today.toISOString(),
		endDate: tomorrow.toISOString(),
	});
};

// Hook to get pending bookings
export const usePendingBookings = (params = {}) => {
	return useBookings({
		...params,
		status: 'scheduled',
	});
};

// Hook to get completed bookings
export const useCompletedBookings = (params = {}) => {
	return useBookings({
		...params,
		status: 'completed',
	});
};

// Hook to get cancelled bookings
export const useCancelledBookings = (params = {}) => {
	return useBookings({
		...params,
		status: 'cancelled',
	});
};

// Hook to mark booking as completed (convenience wrapper)
export const useMarkAsCompleted = () => {
	const updateStatusMutation = useUpdateBookingStatus();

	return useMutation({
		mutationFn: (id) =>
			updateStatusMutation.mutateAsync({ id, status: 'completed' }),
		onSuccess: updateStatusMutation.onSuccess,
		onError: updateStatusMutation.onError,
	});
};
