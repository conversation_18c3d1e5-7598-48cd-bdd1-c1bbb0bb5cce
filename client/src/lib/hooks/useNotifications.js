import { useMutation } from '@tanstack/react-query';
import { notificationAPI } from '../api';
import { isAuthenticated } from './useAuth';
import { logo } from '@/assets/images';

// Get VAPID public key from environment or use a default for development
const VAPID_PUBLIC_KEY = process.env.NEXT_PUBLIC_VAPID_KEY;
// Convert VAPID key to Uint8Array
function urlBase64ToUint8Array(base64String) {
	const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
	const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/');

	const rawData = window.atob(base64);
	const outputArray = new Uint8Array(rawData.length);

	for (let i = 0; i < rawData.length; ++i) {
		outputArray[i] = rawData.charCodeAt(i);
	}
	return outputArray;
}

// Check if notifications are supported
export const isNotificationSupported = () => {
	return (
		'serviceWorker' in navigator &&
		'PushManager' in window &&
		'Notification' in window
	);
};

// Check if notifications are granted
export const isNotificationGranted = () => {
	return Notification.permission === 'granted';
};

// Check if user has already subscribed
export const checkExistingSubscription = async () => {
	try {
		if (!isNotificationSupported()) return false;

		const registration = await navigator.serviceWorker.ready;
		const subscription = await registration.pushManager.getSubscription();

		// Check if subscription exists and is stored in localStorage
		const isSubscribed =
			localStorage.getItem('notification-subscribed') === 'true';

		return subscription && isSubscribed;
	} catch (error) {
		console.error('Error checking existing subscription:', error);
		return false;
	}
};

// Register service worker
const registerServiceWorker = async () => {
	try {
		if (!('serviceWorker' in navigator)) {
			throw new Error('Service Worker not supported');
		}

		const registration = await navigator.serviceWorker.register('/sw.js');
		console.log('Service Worker registered successfully');
		return registration;
	} catch (error) {
		console.error('Service Worker registration failed:', error);
		throw error;
	}
};

// Subscribe to push notifications
// Subscribe to push notifications
export const useSubscribeToNotifications = (userData) => {
	return useMutation({
		mutationFn: async () => {
			if (!isAuthenticated()) {
				console.warn('[🔔] Not authenticated');
				throw new Error(
					'User must be authenticated to subscribe to notifications',
				);
			}

			// Check if notifications are supported
			if (!isNotificationSupported()) {
				console.warn('[🔔] Notification not supported in this browser');
				throw new Error('Push notifications are not supported in this browser');
			}

			// Request notification permission
			const permission = await Notification.requestPermission();
			if (permission !== 'granted') {
				throw new Error('Notification permission denied');
			}

			// Register service worker
			await registerServiceWorker();
			const registration = await navigator.serviceWorker.ready;
			console.log('[🔔] Service worker ready');

			// Check if already subscribed
			let subscription = await registration.pushManager.getSubscription();

			if (!subscription) {
				subscription = await registration.pushManager.subscribe({
					userVisibleOnly: true,
					applicationServerKey: urlBase64ToUint8Array(VAPID_PUBLIC_KEY),
				});
			} else {
				console.log('[🔔] Already subscribed');
			}

			// Extract keys using getKey() method
			const p256dhKey = subscription.getKey('p256dh');
			const authKey = subscription.getKey('auth');

			// Check if keys exist
			if (!p256dhKey || !authKey) {
				console.error('[🔔] Subscription keys missing or undefined');
				throw new Error('Push subscription keys are missing');
			}

			// Convert ArrayBuffer to base64url
			const arrayBufferToBase64 = (buffer) => {
				const bytes = new Uint8Array(buffer);
				const binaryString = Array.from(bytes, (byte) =>
					String.fromCharCode(byte),
				).join('');
				return btoa(binaryString)
					.replace(/\+/g, '-')
					.replace(/\//g, '_')
					.replace(/=/g, '');
			};

			if (!userData || !userData._id) {
				console.warn('[🔔] User data missing');
				throw new Error('User data not found. Please log in again.');
			}

			// Construct payload
			const subscriptionData = {
				uid: userData._id,
				subscription: {
					endpoint: subscription.endpoint,
					keys: {
						p256dh: arrayBufferToBase64(p256dhKey),
						auth: arrayBufferToBase64(authKey),
					},
				},
			};

			console.log('[🔔] Sending subscription data to server:');

			const response = await notificationAPI.subscribe(subscriptionData);

			// Save to localStorage
			localStorage.setItem('notification-subscribed', 'true');
			localStorage.setItem('notification-endpoint', subscription.endpoint);

			console.log('[🔔] Successfully subscribed and saved to localStorage');
			return response;
		},
		onSuccess: (data) => {
			console.log('[✅] Successfully subscribed to notifications');
		},
		onError: (error) => {
			console.error('[❌] Failed to subscribe to notifications:', error);
			localStorage.removeItem('notification-subscribed');
			localStorage.removeItem('notification-endpoint');
		},
	});
};

// Unsubscribe from push notifications
export const useUnsubscribeFromNotifications = (userData) => {
	return useMutation({
		mutationFn: async () => {
			if (!isAuthenticated()) {
				throw new Error(
					'User must be authenticated to unsubscribe from notifications',
				);
			}

			const registration = await navigator.serviceWorker.ready;
			const subscription = await registration.pushManager.getSubscription();

			if (subscription) {
				// Check if we have user data
				if (!userData || !userData._id) {
					throw new Error('User data not found. Please log in again.');
				}

				// Unsubscribe from server
				const unsubscribeData = {
					uid: userData._id,
					endpoint: subscription.endpoint,
				};

				await notificationAPI.unsubscribe(unsubscribeData);

				// Unsubscribe from browser
				await subscription.unsubscribe();
			}

			// Remove subscription status from localStorage
			localStorage.removeItem('notification-subscribed');
			localStorage.removeItem('notification-endpoint');

			return { message: 'Successfully unsubscribed from notifications' };
		},
		onSuccess: (data) => {
			console.log('Successfully unsubscribed from notifications:', data);
		},
		onError: (error) => {
			console.error('Failed to unsubscribe from notifications:', error);
		},
	});
};

// Send notification (admin only)
export const useSendNotification = () => {
	return useMutation({
		mutationFn: async ({ recipientUid, senderName, messageContent }) => {
			if (!isAuthenticated()) {
				throw new Error('User must be authenticated to send notifications');
			}

			const notificationData = {
				recipientUid,
				senderName,
				messageContent,
			};

			return await notificationAPI.sendNotification(notificationData);
		},
		onSuccess: (data) => {
			console.log('Notification sent successfully:', data);
		},
		onError: (error) => {
			console.error('Failed to send notification:', error);
		},
	});
};

// Broadcast notification to all users (admin only)
export const useBroadcastNotification = () => {
	return useMutation({
		mutationFn: async ({ title, messageContent }) => {
			if (!isAuthenticated()) {
				throw new Error(
					'User must be authenticated to broadcast notifications',
				);
			}

			const notificationData = {
				title,
				messageContent,
			};

			return await notificationAPI.broadcastNotification(notificationData);
		},
		onSuccess: (data) => {
			console.log('Broadcast notification sent successfully:', data);
		},
		onError: (error) => {
			console.error('Failed to broadcast notification:', error);
		},
	});
};

// Utility function to show local notification
export const showLocalNotification = (title, body, options = {}) => {
	if (!isNotificationSupported() || !isNotificationGranted()) {
		console.warn('Notifications not supported or not granted');
		return;
	}

	const notification = new Notification(title, {
		body,
		icon: `${logo}`,
		badge: `${logo}`,
		tag: 'youlife-local',
		requireInteraction: true,
		...options,
	});

	// Auto-close after 5 seconds
	setTimeout(() => {
		notification.close();
	}, 5000);

	return notification;
};
