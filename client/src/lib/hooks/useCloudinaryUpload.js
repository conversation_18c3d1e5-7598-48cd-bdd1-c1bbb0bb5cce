import { useCallback, useRef } from 'react';

/**
 * Custom hook for Cloudinary uploads using the upload widget
 * This handles direct frontend uploads to Cloudinary without going through the server
 */
export const useCloudinaryUpload = () => {
  const cloudinaryRef = useRef();
  const widgetRef = useRef();

  const initializeWidget = useCallback((options = {}) => {
    const {
      cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
      uploadPreset = process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET,
      folder = 'lesson-videos',
      resourceType = 'auto',
      multiple = false,
      maxFileSize = 2000000000, // 2GB in bytes
      sources = ['local', 'camera'],
      onSuccess,
      onError,
      onProgress,
    } = options;

    if (!cloudName || !uploadPreset) {
      console.error('Cloudinary cloud name and upload preset are required');
      return null;
    }

    if (!cloudinaryRef.current && typeof window !== 'undefined') {
      cloudinaryRef.current = window.cloudinary;
    }

    if (!cloudinaryRef.current) {
      console.error('Cloudinary script not loaded');
      return null;
    }

    const widget = cloudinaryRef.current.createUploadWidget(
      {
        cloudName,
        uploadPreset,
        folder,
        resourceType,
        multiple,
        maxFileSize,
        sources,
        clientAllowedFormats: resourceType === 'video' 
          ? ['mp4', 'mov', 'avi', 'wmv', 'flv', 'webm']
          : resourceType === 'image'
          ? ['png', 'jpg', 'jpeg', 'gif', 'webp']
          : undefined,
        cropping: resourceType === 'image',
        showAdvancedOptions: true,
        showUploadMoreButton: multiple,
        showPoweredBy: false,
        theme: 'minimal',
        styles: {
          palette: {
            window: '#FFFFFF',
            windowBorder: '#90A0B3',
            tabIcon: '#0078FF',
            menuIcons: '#5A616A',
            textDark: '#000000',
            textLight: '#FFFFFF',
            link: '#0078FF',
            action: '#FF620C',
            inactiveTabIcon: '#0E2F5A',
            error: '#F44235',
            inProgress: '#0078FF',
            complete: '#20B832',
            sourceBg: '#E4EBF1'
          },
          fonts: {
            default: null,
            "'IBM Plex Sans', sans-serif": {
              url: null,
              active: true
            }
          }
        }
      },
      (error, result) => {
        if (error) {
          console.error('Cloudinary upload error:', error);
          onError?.(error);
          return;
        }

        if (result.event === 'success') {
          console.log('Upload successful:', result.info);
          onSuccess?.(result.info);
        } else if (result.event === 'progress') {
          onProgress?.(result.info);
        }
      }
    );

    return widget;
  }, []);

  const openWidget = useCallback((options = {}) => {
    const widget = initializeWidget(options);
    if (widget) {
      widgetRef.current = widget;
      widget.open();
    }
    return widget;
  }, [initializeWidget]);

  const closeWidget = useCallback(() => {
    if (widgetRef.current) {
      widgetRef.current.close();
    }
  }, []);

  return {
    openWidget,
    closeWidget,
    initializeWidget,
  };
};

export default useCloudinaryUpload;
