# Frontend Refactoring Plan

## Current Issues Identified

### 1. Project Structure Issues
- Components are flat in one directory without logical grouping
- Mixed API approaches (`client/api/` vs `client/services/api.js`)
- Assets scattered across different locations
- No clear separation between business logic and UI components

### 2. Code Quality Issues
- Duplicate API implementations
- Inconsistent naming conventions
- Missing abstractions for common patterns
- No proper error boundaries
- Inconsistent state management patterns

### 3. Performance Issues
- No code splitting implementation
- Large bundle size potential
- Missing lazy loading for components
- No optimization for images and assets

### 4. Developer Experience Issues
- Missing comprehensive documentation
- No consistent coding standards
- Limited TypeScript/JSDoc usage
- No component storybook or style guide

## Proposed New Structure

```
client/
├── src/
│   ├── app/                    # Next.js 13+ app directory (keep existing)
│   ├── components/
│   │   ├── ui/                 # Reusable UI components (buttons, inputs, etc.)
│   │   ├── common/             # Common components used across features
│   │   ├── features/           # Feature-specific components
│   │   │   ├── auth/
│   │   │   ├── lessons/
│   │   │   ├── diary/
│   │   │   ├── goals/
│   │   │   └── admin/
│   │   └── layout/             # Layout components (Header, Sidebar, etc.)
│   ├── lib/
│   │   ├── api/                # Consolidated API layer
│   │   ├── hooks/              # Custom React hooks
│   │   ├── utils/              # Utility functions
│   │   ├── constants/          # App constants
│   │   ├── types/              # Type definitions (JSDoc)
│   │   └── validations/        # Form validation schemas
│   ├── styles/                 # Global styles and theme
│   ├── assets/                 # Static assets
│   │   ├── images/
│   │   ├── icons/
│   │   └── svgs/
│   └── providers/              # Context providers
├── public/                     # Static files
├── docs/                       # Documentation
└── config files...
```

## Implementation Steps

### Phase 1: Reorganize Project Structure
1. Create new folder structure
2. Move existing files to appropriate locations
3. Update import paths
4. Test that everything still works

### Phase 2: Consolidate API Layer
1. Merge duplicate API implementations
2. Create consistent error handling
3. Implement proper request/response interceptors
4. Add request caching and optimization

### Phase 3: Refactor Components
1. Extract reusable UI components
2. Create feature-specific component groups
3. Implement proper component composition
4. Add proper prop validation

### Phase 4: Performance Optimization
1. Implement code splitting
2. Add lazy loading for components
3. Optimize bundle size
4. Implement image optimization

### Phase 5: Developer Experience
1. Add comprehensive documentation
2. Create component style guide
3. Implement consistent coding standards
4. Add development tooling improvements

## Benefits Expected

1. **Better Organization**: Clear separation of concerns and logical grouping
2. **Improved Maintainability**: Easier to find and modify code
3. **Enhanced Reusability**: Reusable components reduce duplication
4. **Better Performance**: Optimized loading and bundle size
5. **Improved Developer Experience**: Better tooling and documentation
6. **Easier Onboarding**: New developers can understand structure quickly
7. **Scalability**: Structure supports future feature additions
