/** @type {import('tailwindcss').Config} */
module.exports = {
	content: [
		'./app/**/*.{js,ts,jsx,tsx,mdx}',
		'./components/**/*.{js,ts,jsx,tsx,mdx}',
	],
	theme: {
		extend: {
			colors: {
				primary: {
					DEFAULT: '#008080', // Teal color for Sign In button
					dark: '#00A3A3',
					light: '#00A3A3',
				},
				secondary: {
					DEFAULT: '#0C142A', // Dark blue background in dark mode
					dark: '#0C142A',
					light: '#FFFFFF',
				},
				accent: {
					DEFAULT: '#6C63FF', // Purple color for the illustrated character
					light: '#6C63FF',
					dark: '#6C63FF',
				},
				background: {
					DEFAULT: '#FFFFFF',
					light: '#FFFFFF', // White background in light mode
					dark: '#0C142A', // Dark blue background in dark mode
				},
				foreground: {
					DEFAULT: '#171717',
					light: '#171717', // Dark text in light mode
					dark: '#FFFFFF', // White text in dark mode
				},
				button: {
					primary: {
						bg: '#00A3A3', // Teal sign in button
						text: '#FFFFFF',
					},
					secondary: {
						light: {
							bg: '#FFFFFF',
							text: '#171717',
						},
						dark: {
							bg: '#171717',
							text: '#FFFFFF',
						},
					},
				},
			},
			fontFamily: {
				sans: ['Inter', 'sans-serif'],
				heading: ['Poppins', 'sans-serif'],
			},
		},
	},
	plugins: [],
	darkMode: 'class', // Changed to class-based for more control
};
