// Service Worker for Push Notifications

const LOGO_URL = '/logo.png';

const CACHE_NAME = 'youlife-v1';
const urlsToCache = ['/'];

// Install event
self.addEventListener('install', (event) => {
	event.waitUntil(
		caches.open(CACHE_NAME).then((cache) => cache.addAll(urlsToCache)),
	);
});

// Fetch event
self.addEventListener('fetch', (event) => {
	event.respondWith(
		caches.match(event.request).then((response) => {
			// Return cached version or fetch from network
			return response || fetch(event.request);
		}),
	);
});

// Push event - handles incoming push notifications
self.addEventListener('push', (event) => {
	console.log('[SW] Push event received');

	let notificationData = {
		title: 'YouLife',
		body: 'You have a new notification',
		icon: LOGO_URL,
		badge: LOGO_URL,
		tag: 'youlife-notification',
		requireInteraction: true,
		data: {
			url: '/',
			type: 'general',
			timestamp: Date.now(),
		},
		actions: [
			{
				action: 'view',
				title: 'View',
				icon: LOGO_URL,
			},
			{
				action: 'dismiss',
				title: 'Dismiss',
				icon: LOGO_URL,
			},
		],
	};

	// Parse the push payload if it exists
	if (event.data) {
		try {
			const payloadData = event.data.json();
			notificationData = {
				...notificationData,
				...payloadData,
				data: {
					...notificationData.data,
					...payloadData.data,
				},
			};
		} catch (error) {
			console.error('[SW] Error parsing push payload:', error);
			notificationData.body = event.data.text() || notificationData.body;
		}
	}

	// Customize notification based on type
	if (notificationData.data?.type === 'new_lesson') {
		notificationData.tag = 'new-lesson';
		notificationData.actions = [
			{
				action: 'view',
				title: 'View Course',
				icon: LOGO_URL,
			},
			{
				action: 'dismiss',
				title: 'Later',
				icon: LOGO_URL,
			},
		];
		// Add vibration for new lessons
		notificationData.vibrate = [200, 100, 200];
	} else if (notificationData.data?.type === 'message') {
		notificationData.tag = 'new-message';
		notificationData.actions = [
			{
				action: 'view',
				title: 'View Message',
				icon: LOGO_URL,
			},
			{
				action: 'dismiss',
				title: 'Dismiss',
				icon: LOGO_URL,
			},
		];
	}

	// Show the notification
	const options = {
		body: notificationData.body,
		icon: notificationData.icon,
		badge: notificationData.badge,
		tag: notificationData.tag,
		requireInteraction: notificationData.requireInteraction,
		actions: notificationData.actions,
		data: notificationData.data,
		vibrate: notificationData.vibrate || [200, 100, 200],
		silent: false,
	};

	event.waitUntil(
		self.registration.showNotification(notificationData.title, options),
	);
});

// Notification click event
self.addEventListener('notificationclick', (event) => {
	console.log('[SW] Notification clicked:', event);

	const notification = event.notification;
	const action = event.action;
	const data = notification.data || {};

	// Close the notification
	notification.close();

	// Handle dismiss action
	if (action === 'dismiss') {
		return; // Just close the notification
	}

	// Determine the URL to open
	let urlToOpen = data.url || '/';

	if (data.type === 'new_lesson' && data.lessonId) {
		urlToOpen = `/lessons/${data.lessonId}`;
	} else if (data.type === 'message') {
		urlToOpen = '/messages'; // Adjust this path based on your app structure
	}

	// Open or focus the app
	if (action === 'view' || !action) {
		event.waitUntil(
			clients
				.matchAll({
					type: 'window',
					includeUncontrolled: true,
				})
				.then((clientList) => {
					// Check if the app is already open
					for (const client of clientList) {
						const clientUrl = new URL(client.url);
						const targetUrl = new URL(urlToOpen, self.location.origin);

						// If the app is open and it's the same origin
						if (clientUrl.origin === targetUrl.origin) {
							if (client.url !== targetUrl.href) {
								// Navigate to the target URL by posting a message
								client.postMessage({
									type: 'NOTIFICATION_CLICK',
									url: urlToOpen,
									data: data,
								});
							}
							return client.focus();
						}
					}

					// If no client is open, open a new window
					if (clients.openWindow) {
						return clients.openWindow(urlToOpen);
					}
				})
				.catch((error) => {
					console.error('[SW] Error handling notification click:', error);
				}),
		);
	}
});

// Background sync event (for offline support)
self.addEventListener('sync', (event) => {
	if (event.tag === 'background-sync') {
		event.waitUntil(
			// Handle background sync logic here
			console.log('Background sync triggered'),
		);
	}
});

// Activate event
self.addEventListener('activate', (event) => {
	event.waitUntil(
		caches.keys().then((cacheNames) => {
			return Promise.all(
				cacheNames.map((cacheName) => {
					if (cacheName !== CACHE_NAME) {
						return caches.delete(cacheName);
					}
				}),
			);
		}),
	);
});
