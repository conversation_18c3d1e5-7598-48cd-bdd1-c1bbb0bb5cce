#!/usr/bin/env node

/**
 * Cleanup Old Components Script
 * This script removes old component files and directories that are no longer needed
 */

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  projectRoot: '/home/<USER>/Documents/Projects/You-Life/client',
  
  // Files and directories to remove
  toRemove: [
    // Old component files that have been moved to src/components/
    'components/VideoScreen.jsx',
    'components/NotificationBroadcast.jsx',
    'components/OngoingVideosSection.jsx',
    'components/AdminNavbar.jsx',
    'components/NotificationPrompt.jsx',
    'components/SidebarNav.jsx',
    'components/Pagination.jsx',
    'components/LessonUploadForm.jsx',
    'components/AddTaskModal.jsx',
    'components/Onboard.jsx',
    'components/AddReflection.jsx',
    'components/AdminSidebar.jsx',
    'components/AdminLayout.jsx',
    'components/SplashScreen.jsx',
    'components/CustomImage.jsx',
    'components/OptimizedImage.jsx',
    'components/SearchBar.jsx',
    'components/LazyWrapper.jsx',
    'components/Loading.jsx',
    'components/Modal.jsx',
    'components/Button.jsx',
    'components/Input.jsx',
    
    // Old services files
    'services/api.js',
    'services/auth.js',
    'services/lessons.js',
    'services/diary.js',
    'services/goals.js',
    'services/user.js',
    'services/notifications.js',
    'services/userProgress.js',
    'services/values.js',
    'services/index.js',
    
    // Old hooks files
    'hooks/useAuth.js',
    'hooks/useUser.js',
    'hooks/useLessons.js',
    'hooks/useGoals.js',
    'hooks/useDiary.js',
    'hooks/useNotifications.js',
    'hooks/useUserProgress.js',
    'hooks/useValues.js',
    'hooks/useCloudinaryUpload.js',
    'hooks/index.js',
    
    // Old API files
    'api/auth.js',
    'api/user.js',
    'api/lessons.js',
    'api/diary.js',
    'api/goals.js',
    'api/notifications.js',
    'api/userProgress.js',
    'api/values.js',
    'api/index.js',
    
    // Old utility files
    'utils/api.js',
    'utils/auth.js',
    'utils/constants.js',
    'utils/validation.js',
    'utils/storage.js',
    'utils/index.js',
    
    // Old context files
    'contexts/AuthContext.js',
    'contexts/UserContext.js',
    'contexts/ThemeContext.js',
    'contexts/NotificationContext.js',
    'contexts/index.js',
    
    // Old provider files
    'providers/AuthProvider.js',
    'providers/UserProvider.js',
    'providers/ThemeProvider.js',
    'providers/NotificationProvider.js',
    'providers/index.js',
    
    // Old lib files that might be duplicated
    'lib/auth.js',
    'lib/api.js',
    'lib/utils.js',
    'lib/constants.js',
    'lib/validation.js',
    'lib/storage.js',
    'lib/index.js',
    
    // Old local services
    'localservices/index.js',
  ],
  
  // Directories to remove if empty
  directoriesToCheck: [
    'components',
    'services',
    'hooks',
    'api',
    'utils',
    'contexts',
    'providers',
    'lib',
    'localservices',
  ],
  
  // Backup directory
  backupDir: 'backup_old_components',
};

// Utility functions
function createBackupDir() {
  const backupPath = path.join(config.projectRoot, config.backupDir);
  if (!fs.existsSync(backupPath)) {
    fs.mkdirSync(backupPath, { recursive: true });
  }
  return backupPath;
}

function backupFile(filePath) {
  const backupPath = createBackupDir();
  const relativePath = path.relative(config.projectRoot, filePath);
  const backupFilePath = path.join(backupPath, relativePath);
  const backupFileDir = path.dirname(backupFilePath);
  
  // Create backup directory structure
  if (!fs.existsSync(backupFileDir)) {
    fs.mkdirSync(backupFileDir, { recursive: true });
  }
  
  // Copy file to backup
  fs.copyFileSync(filePath, backupFilePath);
  console.log(`  📦 Backed up: ${relativePath}`);
}

function removeFile(filePath) {
  try {
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      
      if (stats.isFile()) {
        backupFile(filePath);
        fs.unlinkSync(filePath);
        console.log(`  ✓ Removed file: ${path.relative(config.projectRoot, filePath)}`);
        return true;
      }
    }
    return false;
  } catch (error) {
    console.error(`  ✗ Error removing file ${filePath}:`, error.message);
    return false;
  }
}

function removeDirectory(dirPath) {
  try {
    if (fs.existsSync(dirPath)) {
      const stats = fs.statSync(dirPath);
      
      if (stats.isDirectory()) {
        const files = fs.readdirSync(dirPath);
        
        // Only remove if empty
        if (files.length === 0) {
          fs.rmdirSync(dirPath);
          console.log(`  ✓ Removed empty directory: ${path.relative(config.projectRoot, dirPath)}`);
          return true;
        } else {
          console.log(`  - Directory not empty: ${path.relative(config.projectRoot, dirPath)}`);
        }
      }
    }
    return false;
  } catch (error) {
    console.error(`  ✗ Error removing directory ${dirPath}:`, error.message);
    return false;
  }
}

function cleanupFiles() {
  console.log('🧹 Cleaning up old component files...\n');
  
  let removedCount = 0;
  
  for (const fileToRemove of config.toRemove) {
    const filePath = path.join(config.projectRoot, fileToRemove);
    if (removeFile(filePath)) {
      removedCount++;
    }
  }
  
  console.log(`\n📁 Checking directories for cleanup...\n`);
  
  let removedDirCount = 0;
  
  for (const dirToCheck of config.directoriesToCheck) {
    const dirPath = path.join(config.projectRoot, dirToCheck);
    if (removeDirectory(dirPath)) {
      removedDirCount++;
    }
  }
  
  return { removedCount, removedDirCount };
}

function findRemainingImports() {
  console.log('\n🔍 Checking for remaining old imports...\n');
  
  const problematicImports = [];
  
  // Patterns to look for
  const patterns = [
    /from ['"]@\/components\//g,
    /from ['"]@\/services\//g,
    /from ['"]@\/hooks\//g,
    /from ['"]@\/api\//g,
    /from ['"]@\/utils\//g,
    /from ['"]@\/contexts\//g,
    /from ['"]@\/providers\//g,
    /from ['"]@\/lib\//g,
  ];
  
  const excludePatterns = [
    /from ['"]@\/src\/components\//g,
    /from ['"]@\/src\/lib\//g,
    /from ['"]@\/src\/providers\//g,
    /from ['"]@\/assets\//g,
  ];
  
  function searchInFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      for (const pattern of patterns) {
        const matches = content.match(pattern);
        if (matches) {
          // Check if it's an allowed import
          const isAllowed = excludePatterns.some(excludePattern => 
            matches.some(match => excludePattern.test(match))
          );
          
          if (!isAllowed) {
            problematicImports.push({
              file: path.relative(config.projectRoot, filePath),
              imports: matches
            });
          }
        }
      }
    } catch (error) {
      console.error(`Error reading file ${filePath}:`, error.message);
    }
  }
  
  function searchInDirectory(dir) {
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stats = fs.statSync(fullPath);
        
        if (stats.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          searchInDirectory(fullPath);
        } else if (stats.isFile() && /\.(js|jsx|ts|tsx)$/.test(item)) {
          searchInFile(fullPath);
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${dir}:`, error.message);
    }
  }
  
  searchInDirectory(path.join(config.projectRoot, 'app'));
  searchInDirectory(path.join(config.projectRoot, 'src'));
  
  if (problematicImports.length > 0) {
    console.log('⚠️  Found remaining old imports:');
    for (const { file, imports } of problematicImports) {
      console.log(`  ${file}:`);
      for (const imp of imports) {
        console.log(`    ${imp}`);
      }
    }
  } else {
    console.log('✅ No problematic imports found!');
  }
  
  return problematicImports;
}

function generateCleanupReport() {
  const results = cleanupFiles();
  const remainingImports = findRemainingImports();
  
  console.log('\n📊 Cleanup Summary:');
  console.log(`  Files removed: ${results.removedCount}`);
  console.log(`  Empty directories removed: ${results.removedDirCount}`);
  console.log(`  Remaining problematic imports: ${remainingImports.length}`);
  
  if (fs.existsSync(path.join(config.projectRoot, config.backupDir))) {
    console.log(`  Backup created: ${config.backupDir}/`);
  }
  
  console.log('\n✅ Cleanup complete!');
  
  if (remainingImports.length > 0) {
    console.log('\n⚠️  Next steps:');
    console.log('1. Review and fix the remaining imports listed above');
    console.log('2. Test the application to ensure everything works');
    console.log('3. Remove the backup directory if everything is working');
  } else {
    console.log('\n🎉 All done! Your project is now fully refactored.');
    console.log('Next steps:');
    console.log('1. Test the application thoroughly');
    console.log('2. Remove the backup directory if everything is working');
    console.log('3. Commit your changes');
  }
}

// Run the script
if (require.main === module) {
  console.log('🚀 Starting cleanup process...\n');
  generateCleanupReport();
}

module.exports = {
  cleanupFiles,
  findRemainingImports,
  generateCleanupReport,
  config
};
