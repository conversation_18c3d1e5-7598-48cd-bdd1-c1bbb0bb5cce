@import 'tailwindcss';

:root {
	--background: #ffffff;
	--foreground: #171717;
}

.dark {
	--background: #0c142a;
	--foreground: #ffffff;
}

@theme inline {
	--color-background: var(--background);
	--color-text: var(--foreground);
	--font-sans: var(--font-geist-sans);
	--font-mono: var(--font-geist-mono);
}

body {
	background: var(--background);
	color: var(--foreground);
	font-family: Arial, Helvetica, sans-serif;
	transition: background-color 0.3s ease, color 0.3s ease;
}

/* Video.js custom styling */
.video-player-container {
	position: relative;
	width: 100%;
	height: 100%;
}

.video-js {
	width: 100% !important;
	height: 100% !important;
}

.video-js .vjs-big-play-button {
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	border-radius: 50%;
	background-color: rgba(0, 0, 0, 0.7);
	border: none;
	width: 80px;
	height: 80px;
}

.video-js .vjs-big-play-button:hover {
	background-color: rgba(0, 0, 0, 0.9);
}

.video-js .vjs-control-bar {
	background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
}
