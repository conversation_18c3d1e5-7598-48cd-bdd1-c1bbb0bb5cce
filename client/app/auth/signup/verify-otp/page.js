'use client';
import React, {
	useState,
	useRef,
	useCallback,
	useEffect,
	Suspense,
} from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { FaArrowLeft } from 'react-icons/fa';
import { useVerifyEmail } from '@/src/lib/hooks';

// Create a separate component for the verification logic
const VerificationCodeContent = () => {
	const router = useRouter();
	const searchParams = useSearchParams();
	const email = searchParams.get('email');

	const OTP_LENGTH = 6; // Changed to 6 as your API example shows 6 digits
	const [otp, setOtp] = useState(Array(OTP_LENGTH).fill(''));
	const inputFieldsRef = useRef([]);
	const [canResend, setCanResend] = useState(true);
	const [countdown, setCountdown] = useState(0);

	// Use the verify email hook
	const { mutate: verifyEmail, isPending, error } = useVerifyEmail();

	useEffect(() => {
		let timer;
		if (countdown > 0) {
			timer = setTimeout(() => setCountdown(countdown - 1), 1000);
		} else if (countdown === 0) {
			setCanResend(true);
		}
		return () => clearTimeout(timer);
	}, [countdown]);

	const handlePaste = useCallback((e) => {
		e.preventDefault();
		const pastedData = e.clipboardData.getData('text/plain').trim();

		// Check if pasted content is a valid OTP
		if (pastedData.length === OTP_LENGTH && /^\d+$/.test(pastedData)) {
			setOtp(pastedData.split(''));

			// Focus the last input after paste
			if (inputFieldsRef.current[OTP_LENGTH - 1]) {
				inputFieldsRef.current[OTP_LENGTH - 1].focus();
			}
		}
	}, []);

	const inputOTPValue = useCallback((value, index) => {
		const valueToSave = value.slice(0, 1);
		setOtp((prev) => {
			const newArray = prev.map((item, i) => {
				if (i === index) {
					return valueToSave;
				}
				return item;
			});

			return newArray;
		});

		const previousElement = inputFieldsRef.current[index - 1];
		const nextElement = inputFieldsRef.current[index + 1];

		if (value.length > 0 && nextElement) {
			nextElement.focus();
		}

		if (value.length < 1 && previousElement) {
			previousElement.focus();
		}
	}, []);

	const handleKeyDown = (e, index) => {
		// Allow backspace to clear current field and move to previous
		if (e.key === 'Backspace' && !otp[index]) {
			const previousElement = inputFieldsRef.current[index - 1];
			if (previousElement) {
				previousElement.focus();
			}
		}
	};

	const handleResendCode = () => {
		if (!canResend) return;

		// Reset OTP fields
		setOtp(Array(OTP_LENGTH).fill(''));

		// Focus on first input
		if (inputFieldsRef.current[0]) {
			inputFieldsRef.current[0].focus();
		}

		// Simulate code resend
		console.log('Resending verification code...');

		// Disable resend button and start countdown
		setCanResend(false);
		setCountdown(30); // 30 seconds countdown
	};

	const handleSubmit = (e) => {
		e.preventDefault();
		const otpValue = otp.join('');

		if (otpValue.length !== OTP_LENGTH) {
			alert('Please enter the complete verification code');
			return;
		}

		if (!email) {
			alert('Email not found. Please go back and try again.');
			return;
		}

		// Call the verify email mutation
		verifyEmail(
			{
				email: email,
				otp: otpValue,
			},
			{
				onSuccess: (data) => {
					console.log('Email verification successful:', data);
					// Redirect to dashboard or home page on successful verification
					router.push('/');
				},
				onError: (error) => {
					console.error('Email verification failed:', error);
					// Clear OTP fields on error
					setOtp(Array(OTP_LENGTH).fill(''));
					if (inputFieldsRef.current[0]) {
						inputFieldsRef.current[0].focus();
					}
				},
			},
		);
	};

	return (
		<div className='flex flex-col min-h-screen px-6 py-8'>
			{/* Header with back button */}
			<div className='flex items-center mb-12'>
				<Link
					href='/'
					className='rounded-full bg-white p-3 mr-4 shadow-2xl'>
					<FaArrowLeft className='text-black text-lg' />
				</Link>
				<h2 className='text-2xl font-bold'>Verification Code</h2>
			</div>

			{/* Main content */}
			<div className='flex-1'>
				<h1 className='text-2xl font-bold mb-6'>
					Enter the code we sent your registered mail
				</h1>

				<form
					onSubmit={handleSubmit}
					className='mb-4'
					onPaste={handlePaste}>
					<div className='flex justify-center items-center mb-4'>
						{otp.map((value, index) => (
							<input
								key={index}
								type='text'
								inputMode='numeric'
								maxLength={1}
								value={value}
								className='w-16 h-16 mx-1 text-2xl text-center rounded-lg bg-[#D9D9D9] dark:bg-white text-gray-600 font-bold'
								onChange={(e) => {
									const inputtedValue = e.target.value;
									if (
										inputtedValue.length === OTP_LENGTH &&
										!isNaN(Number(inputtedValue))
									) {
										setOtp(inputtedValue.split(''));
										return;
									}
									if (isNaN(Number(inputtedValue))) {
										return;
									}
									inputOTPValue(inputtedValue, index);
								}}
								ref={(data) => {
									if (!data) {
										return;
									}
									inputFieldsRef.current[index] = data;
								}}
								onKeyDown={(e) => handleKeyDown(e, index)}
							/>
						))}
					</div>

					<div className='text-center mb-8'>
						<p className='text-lg mb-2'>Didn't receive a code?</p>
						<button
							type='button'
							onClick={handleResendCode}
							disabled={!canResend}
							className={`font-bold text-lg ${
								canResend ? 'text-white' : 'text-gray-400'
							}`}>
							{canResend ? 'Resend Code' : `Resend Code (${countdown}s)`}
						</button>
					</div>

					{/* Error display */}
					{error && (
						<div className='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4'>
							<p>
								{error.response?.data?.message ||
									'Verification failed. Please try again.'}
							</p>
						</div>
					)}

					{/* Add spacer */}
					<div className='h-32'></div>

					<button
						type='submit'
						disabled={isPending}
						className='w-full p-4 rounded-lg bg-black dark:bg-teal-600 text-white text-xl font-semibold mt-2 disabled:opacity-50 disabled:cursor-not-allowed'>
						{isPending ? 'Verifying...' : 'Continue'}
					</button>
				</form>
			</div>
		</div>
	);
};

// Loading fallback component
const LoadingFallback = () => (
	<div className='flex flex-col min-h-screen px-6 py-8'>
		<div className='flex items-center mb-12'>
			<div className='rounded-full bg-gray-200 p-3 mr-4 animate-pulse'>
				<div className='w-6 h-6'></div>
			</div>
			<div className='h-8 bg-gray-200 rounded w-48 animate-pulse'></div>
		</div>
		<div className='flex-1'>
			<div className='h-8 bg-gray-200 rounded w-3/4 mb-6 animate-pulse'></div>
			<div className='flex justify-center items-center mb-4'>
				{Array(6)
					.fill(0)
					.map((_, index) => (
						<div
							key={index}
							className='w-16 h-16 mx-1 bg-gray-200 rounded-lg animate-pulse'></div>
					))}
			</div>
		</div>
	</div>
);

// Main component with Suspense wrapper
const VerificationCode = () => {
	return (
		<Suspense fallback={<LoadingFallback />}>
			<VerificationCodeContent />
		</Suspense>
	);
};

export default VerificationCode;
