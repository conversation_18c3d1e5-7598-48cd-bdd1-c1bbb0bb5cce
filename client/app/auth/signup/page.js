'use client';
import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { FaArrowLeft, FaEye, FaEyeSlash } from 'react-icons/fa';
import { FcGoogle } from 'react-icons/fc';
import { AiFillApple } from 'react-icons/ai';
import { happy } from '@/assets/svgs';
import Image from 'next/image';
import { useRegister } from '@/src/lib/hooks';
import { logo } from '@/assets/images';

const SignUp = () => {
	const router = useRouter();
	const [formData, setFormData] = useState({
		fullName: '',
		email: '',
		password: '',
		confirmPassword: '',
	});
	const [showPassword, setShowPassword] = useState(false);
	const [showConfirmPassword, setShowConfirmPassword] = useState(false);
	const [errorMessage, setErrorMessage] = useState('');
	const [passwordErrors, setPasswordErrors] = useState([]);

	// Use the register hook
	const { mutate: register, isPending, error } = useRegister();

	const validatePassword = (password) => {
		const errors = [];
		if (password.length < 8) {
			errors.push('Password must be at least 8 characters long');
		}
		if (!/(?=.*[a-z])/.test(password)) {
			errors.push('Password must contain at least one lowercase letter');
		}
		if (!/(?=.*[A-Z])/.test(password)) {
			errors.push('Password must contain at least one uppercase letter');
		}
		if (!/(?=.*\d)/.test(password)) {
			errors.push('Password must contain at least one number');
		}
		if (!/(?=.*[@$!%*?&])/.test(password)) {
			errors.push('Password must contain at least one special character');
		}
		return errors;
	};

	const handleChange = (e) => {
		const { name, value } = e.target;
		setFormData((prev) => ({
			...prev,
			[name]: value,
		}));

		// Clear error message when user starts typing
		if (errorMessage) {
			setErrorMessage('');
		}

		// Validate password in real-time
		if (name === 'password') {
			const errors = validatePassword(value);
			setPasswordErrors(errors);
		}
	};

	const handleSubmit = (e) => {
		e.preventDefault();

		// Clear any existing error messages
		setErrorMessage('');

		// Basic validation
		if (
			!formData.fullName ||
			!formData.email ||
			!formData.password ||
			!formData.confirmPassword
		) {
			setErrorMessage('Please fill in all fields');
			return;
		}

		// Name validation
		if (formData.fullName.trim().length < 2) {
			setErrorMessage('Full name must be at least 2 characters long');
			return;
		}

		// Email validation
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(formData.email)) {
			setErrorMessage('Please enter a valid email address');
			return;
		}

		// Password validation
		const passwordValidationErrors = validatePassword(formData.password);
		if (passwordValidationErrors.length > 0) {
			setErrorMessage('Please fix the password requirements below');
			return;
		}

		// Check if passwords match
		if (formData.password !== formData.confirmPassword) {
			setErrorMessage('Passwords do not match');
			return;
		}

		// Prepare data for API (match API expected format)
		const registrationData = {
			name: formData.fullName.trim(),
			email: formData.email.toLowerCase().trim(),
			password: formData.password,
		};

		// Call the register mutation
		register(registrationData, {
			onSuccess: (data) => {
				console.log('Registration successful:', data);
				// Clear form and error on success
				setFormData({
					fullName: '',
					email: '',
					password: '',
					confirmPassword: '',
				});
				setErrorMessage('');
				setPasswordErrors([]);
				// Redirect to email verification page
				router.push(
					`/auth/signup/verify-otp?email=${encodeURIComponent(formData.email)}`,
				);
			},
			onError: (error) => {
				console.error('Registration failed:', error);

				// Handle different types of errors
				let message = 'Registration failed. Please try again.';

				if (error?.response?.status === 409) {
					message =
						'An account with this email already exists. Please sign in instead.';
				} else if (error?.response?.status === 422) {
					message = 'Please check your information and try again.';
				} else if (error?.response?.status === 429) {
					message = 'Too many registration attempts. Please try again later.';
				} else if (error?.response?.data?.message) {
					message = error.response.data.message;
				} else if (error?.message) {
					message = error.message;
				}

				setErrorMessage(message);
			},
		});
	};

	const getPasswordStrength = () => {
		const password = formData.password;
		if (!password) return { strength: 0, label: '' };

		let score = 0;
		if (password.length >= 8) score++;
		if (/(?=.*[a-z])/.test(password)) score++;
		if (/(?=.*[A-Z])/.test(password)) score++;
		if (/(?=.*\d)/.test(password)) score++;
		if (/(?=.*[@$!%*?&])/.test(password)) score++;

		const labels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
		const colors = [
			'bg-red-500',
			'bg-orange-500',
			'bg-yellow-500',
			'bg-blue-500',
			'bg-green-500',
		];

		return {
			strength: score,
			label: labels[score - 1] || '',
			color: colors[score - 1] || 'bg-gray-300',
		};
	};

	const passwordStrength = getPasswordStrength();

	return (
		<div className='min-h-screen bg-gray-50 dark:bg-gray-900'>
			<div className='flex flex-col min-h-screen px-6 py-8'>
				{/* Header with back button */}
				<div className='flex items-center mb-8'>
					<Link
						href='/'
						className='rounded-full bg-white dark:bg-gray-800 shadow-md hover:shadow-lg transition-shadow p-3 mr-4'>
						<FaArrowLeft className='text-gray-700 dark:text-gray-300 text-lg' />
					</Link>
					<h2 className='text-2xl font-bold text-gray-800 dark:text-white'>
						Sign Up
					</h2>
				</div>

				{/* Main content */}
				<div className='flex-1 flex flex-col lg:flex-row max-w-7xl mx-auto w-full'>
					{/* Image container - hidden on mobile, half width on lg+ */}
					<div className='hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-700 rounded-2xl mr-8 overflow-hidden'>
						<Image
							src={happy}
							alt='Create account illustration'
							className='w-full h-full object-cover'
							priority
						/>
					</div>

					{/* Form container - full width on mobile, half on lg+ */}
					<div className='w-full lg:w-1/2 flex flex-col justify-center'>
						<div className='bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 lg:p-12'>
							<h1 className='text-4xl lg:text-5xl font-bold mb-2 text-gray-800 dark:text-white'>
								Create Account!
							</h1>
							<p className='text-gray-600 dark:text-gray-400 mb-8 text-lg'>
								Join us today and get started
							</p>

							<form
								onSubmit={handleSubmit}
								className='space-y-6'>
								{/* Error display - moved to top of form */}
								{(errorMessage || error) && (
									<div className='bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg'>
										<p className='text-sm font-medium'>
											{errorMessage ||
												error?.response?.data?.message ||
												'An error occurred'}
										</p>
									</div>
								)}

								<div>
									<label
										htmlFor='fullName'
										className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>
										Full Name
									</label>
									<input
										id='fullName'
										type='text'
										name='fullName'
										placeholder='Enter your full name'
										value={formData.fullName}
										onChange={handleChange}
										className='w-full p-4 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all'
										required
									/>
								</div>

								<div>
									<label
										htmlFor='email'
										className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>
										Email Address
									</label>
									<input
										id='email'
										type='email'
										name='email'
										placeholder='Enter your email'
										value={formData.email}
										onChange={handleChange}
										className='w-full p-4 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all'
										required
									/>
								</div>

								<div>
									<label
										htmlFor='password'
										className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>
										Password
									</label>
									<div className='relative'>
										<input
											id='password'
											type={showPassword ? 'text' : 'password'}
											name='password'
											placeholder='Create a password'
											value={formData.password}
											onChange={handleChange}
											className='w-full p-4 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all pr-12'
											required
										/>
										<button
											type='button'
											className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
											onClick={() => setShowPassword(!showPassword)}>
											{showPassword ? (
												<FaEyeSlash size={20} />
											) : (
												<FaEye size={20} />
											)}
										</button>
									</div>

									{/* Password strength indicator */}
									{formData.password && (
										<div className='mt-2'>
											<div className='flex items-center space-x-2'>
												<div className='flex-1 bg-gray-200 dark:bg-gray-600 rounded-full h-2'>
													<div
														className={`h-2 rounded-full transition-all duration-300 ${passwordStrength.color}`}
														style={{
															width: `${
																(passwordStrength.strength / 5) * 100
															}%`,
														}}></div>
												</div>
												<span className='text-sm text-gray-600 dark:text-gray-400'>
													{passwordStrength.label}
												</span>
											</div>

											{/* Password requirements */}
											{passwordErrors.length > 0 && (
												<div className='mt-2'>
													<ul className='text-sm text-red-600 dark:text-red-400 space-y-1'>
														{passwordErrors.map((error, index) => (
															<li
																key={index}
																className='flex items-center'>
																<span className='mr-2'>•</span>
																{error}
															</li>
														))}
													</ul>
												</div>
											)}
										</div>
									)}
								</div>

								<div>
									<label
										htmlFor='confirmPassword'
										className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>
										Confirm Password
									</label>
									<div className='relative'>
										<input
											id='confirmPassword'
											type={showConfirmPassword ? 'text' : 'password'}
											name='confirmPassword'
											placeholder='Confirm your password'
											value={formData.confirmPassword}
											onChange={handleChange}
											className={`w-full p-4 rounded-lg border ${
												formData.confirmPassword &&
												formData.password !== formData.confirmPassword
													? 'border-red-300 dark:border-red-600'
													: 'border-gray-300 dark:border-gray-600'
											} bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all pr-12`}
											required
										/>
										<button
											type='button'
											className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
											onClick={() =>
												setShowConfirmPassword(!showConfirmPassword)
											}>
											{showConfirmPassword ? (
												<FaEyeSlash size={20} />
											) : (
												<FaEye size={20} />
											)}
										</button>
									</div>
									{formData.confirmPassword &&
										formData.password !== formData.confirmPassword && (
											<p className='mt-2 text-sm text-red-600 dark:text-red-400'>
												Passwords do not match
											</p>
										)}
								</div>

								<button
									type='submit'
									disabled={isPending}
									className='w-full p-4 rounded-lg bg-black dark:bg-teal-600 hover:bg-gray-800 dark:hover:bg-teal-700 text-white text-xl font-semibold transition-colors disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-black dark:disabled:hover:bg-teal-600'>
									{isPending ? (
										<span className='flex items-center justify-center'>
											<svg
												className='animate-spin -ml-1 mr-3 h-5 w-5 text-white'
												xmlns='http://www.w3.org/2000/svg'
												fill='none'
												viewBox='0 0 24 24'>
												<circle
													className='opacity-25'
													cx='12'
													cy='12'
													r='10'
													stroke='currentColor'
													strokeWidth='4'></circle>
												<path
													className='opacity-75'
													fill='currentColor'
													d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'></path>
											</svg>
											Creating Account...
										</span>
									) : (
										'Sign Up'
									)}
								</button>
							</form>

							<div className='mt-8 text-center'>
								<p className='text-lg text-gray-600 dark:text-gray-400'>
									Already have an account?{' '}
									<Link
										href='/auth/login'
										className='text-teal-600 dark:text-teal-400 font-semibold hover:text-teal-800 dark:hover:text-teal-300 hover:underline'>
										Sign In
									</Link>
								</p>
							</div>

							<div className='mt-8'>
								<div className='relative'>
									<div className='absolute inset-0 flex items-center'>
										<div className='w-full border-t border-gray-300 dark:border-gray-600'></div>
									</div>
									<div className='relative flex justify-center text-sm'>
										<span className='px-4 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400'>
											Or continue with
										</span>
									</div>
								</div>

								<div className='mt-6 grid grid-cols-2 gap-4'>
									<button
										type='button'
										className='flex items-center justify-center gap-3 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors'>
										<FcGoogle className='text-2xl' />
										<span className='text-lg font-medium'>Google</span>
									</button>

									<button
										type='button'
										className='flex items-center justify-center gap-3 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors'>
										<AiFillApple className='text-2xl' />
										<span className='text-lg font-medium'>Apple</span>
									</button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default SignUp;
