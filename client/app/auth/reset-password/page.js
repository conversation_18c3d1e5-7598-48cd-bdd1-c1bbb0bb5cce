'use client';
import React, { useState, Suspense } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import {
	FaArrowLeft,
	FaEye,
	FaEyeSlash,
	FaLock,
	FaCheckCircle,
} from 'react-icons/fa';

// Loading component for Suspense fallback
const LoadingSpinner = () => (
	<div className='min-h-screen flex items-center justify-center px-4'>
		<div className='max-w-md w-full text-center'>
			<div className='animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600 mx-auto mb-4'></div>
			<p className='text-gray-600 text-lg'>Loading...</p>
		</div>
	</div>
);

// Main component that uses useSearchParams
const ResetPasswordContent = () => {
	const router = useRouter();
	const searchParams = useSearchParams();
	const email = searchParams.get('email');
	const token = searchParams.get('token') || searchParams.get('otp');

	const [passwords, setPasswords] = useState({
		newPassword: '',
		confirmPassword: '',
	});
	const [showPassword, setShowPassword] = useState({
		newPassword: false,
		confirmPassword: false,
	});
	const [errors, setErrors] = useState({
		newPassword: '',
		confirmPassword: '',
		general: '',
	});
	const [isLoading, setIsLoading] = useState(false);
	const [isSuccess, setIsSuccess] = useState(false);

	const handleChange = (e) => {
		const { name, value } = e.target;
		setPasswords((prev) => ({
			...prev,
			[name]: value,
		}));

		// Clear errors when typing
		if (errors[name]) {
			setErrors((prev) => ({
				...prev,
				[name]: '',
				general: '',
			}));
		}
	};

	const togglePasswordVisibility = (field) => {
		setShowPassword((prev) => ({
			...prev,
			[field]: !prev[field],
		}));
	};

	const validateForm = () => {
		let valid = true;
		const newErrors = { newPassword: '', confirmPassword: '', general: '' };

		// Validate password length
		if (passwords.newPassword.length < 8) {
			newErrors.newPassword = 'Password must be at least 8 characters';
			valid = false;
		}

		// Check if passwords match
		if (passwords.newPassword !== passwords.confirmPassword) {
			newErrors.confirmPassword = 'Passwords do not match';
			valid = false;
		}

		// Check if email and token are present
		if (!email || !token) {
			newErrors.general =
				'Invalid reset link. Please request a new password reset.';
			valid = false;
		}

		setErrors(newErrors);
		return valid;
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		if (!validateForm()) return;

		setIsLoading(true);
		setErrors((prev) => ({ ...prev, general: '' }));

		try {
			await authService.resetPassword({
				email,
				token,
				newPassword: passwords.newPassword,
				confirmPassword: passwords.confirmPassword,
			});

			setIsSuccess(true);
			console.log('Password reset successful');

			// Redirect to login after success
			setTimeout(() => {
				router.push(
					'/auth/login?message=Password reset successful. Please login with your new password.',
				);
			}, 3000);
		} catch (error) {
			console.error('Reset password error:', error);
			setErrors((prev) => ({
				...prev,
				general:
					error.response?.data?.message ||
					error.message ||
					'Failed to reset password. Please try again or request a new reset link.',
			}));
		} finally {
			setIsLoading(false);
		}
	};

	if (isSuccess) {
		return (
			<div className='min-h-screen flex items-center justify-center px-4'>
				<div className='max-w-md w-full text-center'>
					<div className='mb-8'>
						<div className='w-24 h-24 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6'>
							<FaCheckCircle className='w-12 h-12 text-teal-600' />
						</div>
						<h1 className='text-3xl font-bold text-gray-900 mb-4'>
							Password Reset Successful!
						</h1>
						<p className='text-gray-600 text-lg leading-relaxed mb-8'>
							Your password has been reset successfully. You will be redirected
							to the login page shortly.
						</p>
						<Link
							href='/auth/login'
							className='inline-flex items-center justify-center w-full px-8 py-4 text-lg font-semibold text-white bg-teal-600 rounded-xl hover:bg-teal-700 transition-colors duration-200 shadow-lg hover:shadow-xl'>
							Go to Login
						</Link>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className='min-h-screen flex flex-col'>
			{/* Header */}
			<div className='flex items-center justify-between p-6 border-b border-gray-100'>
				<div className='flex items-center'>
					<Link
						href='/auth/forgot-password'
						className='flex items-center justify-center w-12 h-12 rounded-full border border-gray-200 hover:bg-gray-50 transition-colors duration-200 mr-4'>
						<FaArrowLeft className='text-gray-600 text-lg' />
					</Link>
					<h2 className='text-xl font-semibold text-gray-900'>
						Reset Password
					</h2>
				</div>
			</div>

			{/* Main Content */}
			<div className='flex-1 flex items-center justify-center px-6 py-12'>
				<div className='max-w-md w-full'>
					{/* Icon and Title */}
					<div className='text-center mb-8'>
						<div className='w-16 h-16 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6'>
							<FaLock className='w-8 h-8 text-teal-600' />
						</div>
						<h1 className='text-3xl font-bold text-gray-900 mb-3'>
							Create New Password
						</h1>
						<p className='text-gray-600 text-lg'>
							Enter your new password below to complete the reset process.
						</p>
					</div>

					<form
						onSubmit={handleSubmit}
						className='space-y-6'>
						{/* General error message */}
						{errors.general && (
							<div className='p-4 bg-red-50 border border-red-200 rounded-xl'>
								<p className='text-red-600 text-sm font-medium'>
									{errors.general}
								</p>
							</div>
						)}

						{/* New Password Field */}
						<div className='space-y-2'>
							<label className='block text-sm font-medium text-gray-700'>
								New Password
							</label>
							<div className='relative'>
								<input
									type={showPassword.newPassword ? 'text' : 'password'}
									name='newPassword'
									placeholder='Enter your new password'
									value={passwords.newPassword}
									onChange={handleChange}
									className='w-full px-4 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors duration-200 pr-12'
									required
								/>
								<button
									type='button'
									className='absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 transition-colors duration-200'
									onClick={() => togglePasswordVisibility('newPassword')}>
									{showPassword.newPassword ? (
										<FaEyeSlash size={20} />
									) : (
										<FaEye size={20} />
									)}
								</button>
							</div>
							{errors.newPassword && (
								<p className='text-red-500 text-sm font-medium'>
									{errors.newPassword}
								</p>
							)}
						</div>

						{/* Confirm Password Field */}
						<div className='space-y-2'>
							<label className='block text-sm font-medium text-gray-700'>
								Confirm Password
							</label>
							<div className='relative'>
								<input
									type={showPassword.confirmPassword ? 'text' : 'password'}
									name='confirmPassword'
									placeholder='Confirm your new password'
									value={passwords.confirmPassword}
									onChange={handleChange}
									className='w-full px-4 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors duration-200 pr-12'
									required
								/>
								<button
									type='button'
									className='absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 transition-colors duration-200'
									onClick={() => togglePasswordVisibility('confirmPassword')}>
									{showPassword.confirmPassword ? (
										<FaEyeSlash size={20} />
									) : (
										<FaEye size={20} />
									)}
								</button>
							</div>
							{errors.confirmPassword && (
								<p className='text-red-500 text-sm font-medium'>
									{errors.confirmPassword}
								</p>
							)}
						</div>

						{/* Password Requirements */}
						<div className='bg-gray-50 p-4 rounded-xl'>
							<h3 className='text-sm font-medium text-gray-700 mb-2'>
								Password Requirements:
							</h3>
							<ul className='text-sm text-gray-600 space-y-1'>
								<li className='flex items-center'>
									<span
										className={`w-2 h-2 rounded-full mr-2 ${
											passwords.newPassword.length >= 8
												? 'bg-teal-500'
												: 'bg-gray-300'
										}`}></span>
									At least 8 characters
								</li>
								<li className='flex items-center'>
									<span
										className={`w-2 h-2 rounded-full mr-2 ${
											passwords.newPassword === passwords.confirmPassword &&
											passwords.newPassword.length > 0
												? 'bg-teal-500'
												: 'bg-gray-300'
										}`}></span>
									Passwords match
								</li>
							</ul>
						</div>

						{/* Submit Button */}
						<button
							type='submit'
							disabled={
								isLoading ||
								!passwords.newPassword ||
								!passwords.confirmPassword
							}
							className='w-full px-8 py-4 text-lg font-semibold text-white bg-teal-600 rounded-xl hover:bg-teal-700 focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 shadow-lg hover:shadow-xl'>
							{isLoading ? (
								<div className='flex items-center justify-center'>
									<div className='w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2'></div>
									Processing...
								</div>
							) : (
								'Reset Password'
							)}
						</button>
					</form>

					{/* Footer */}
					<div className='mt-8 text-center'>
						<p className='text-sm text-gray-600'>
							Remember your password?{' '}
							<Link
								href='/auth/login'
								className='text-teal-600 hover:text-teal-700 font-medium'>
								Sign in instead
							</Link>
						</p>
					</div>
				</div>
			</div>
		</div>
	);
};

// Main component with Suspense wrapper
const ResetPassword = () => {
	return (
		<Suspense fallback={<LoadingSpinner />}>
			<ResetPasswordContent />
		</Suspense>
	);
};

export default ResetPassword;
