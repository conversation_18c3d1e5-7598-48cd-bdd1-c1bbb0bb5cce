'use client';
import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { FaArrowLeft } from 'react-icons/fa';
import { FcGoogle } from 'react-icons/fc';
import Image from 'next/image';

import { useLogin } from '@/src/lib/hooks';
import { authService } from '@/src/lib/api';
import { Button, Input } from '@/src/components/common';
import { happy } from '@/assets/svgs';

const SignIn = () => {
	const router = useRouter();
	const [formData, setFormData] = useState({
		email: '',
		password: '',
	});
	const [errorMessage, setErrorMessage] = useState('');
	const [isGoogleLoading, setIsGoogleLoading] = useState(false);

	// Use the login hook
	const { mutate: login, isPending, error } = useLogin();

	useEffect(() => {
		// Load Google Identity Services script
		const script = document.createElement('script');
		script.src = 'https://accounts.google.com/gsi/client';
		script.async = true;
		script.defer = true;
		document.body.appendChild(script);

		return () => {
			document.body.removeChild(script);
		};
	}, []);

	const handleChange = (e) => {
		const { name, value } = e.target;
		setFormData((prev) => ({
			...prev,
			[name]: value,
		}));

		// Clear error message when user starts typing
		if (errorMessage) {
			setErrorMessage('');
		}
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		e.stopPropagation();

		// Clear any existing error messages
		setErrorMessage('');

		// Basic validation
		if (!formData.email || !formData.password) {
			setErrorMessage('Please fill in all fields');
			return;
		}

		// Email validation
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(formData.email)) {
			setErrorMessage('Please enter a valid email address');
			return;
		}

		try {
			// Call the login mutation
			await login(formData, {
				onSuccess: (data) => {
					setFormData({ email: '', password: '' });
					setErrorMessage('');
					router.push('/');
				},
				onError: (error) => {
					console.error('Login error:', error);
					let message = 'Login failed. Please try again.';
					if (error?.response?.status === 401) {
						message =
							'Invalid email or password. Please check your credentials.';
					} else if (error?.response?.status === 422) {
						message = 'Please enter valid email and password.';
					} else if (error?.response?.status === 429) {
						message = 'Too many login attempts. Please try again later.';
					} else if (error?.response?.data?.message) {
						message = error.response.data.message;
					} else if (error?.message) {
						message = error.message;
					}

					setErrorMessage(message);
					if (error?.response?.status === 401) {
						setFormData((prev) => ({ ...prev, password: '' }));
					}
				},
			});
		} catch (error) {
			console.error('Unexpected error:', error);
			setErrorMessage('An unexpected error occurred. Please try again.');
		}
	};

	const handleGoogleSignIn = () => {
		setErrorMessage('');
		setIsGoogleLoading(true);

		/* global google */
		if (
			typeof google === 'undefined' ||
			!google.accounts ||
			!google.accounts.id
		) {
			setErrorMessage(
				'Google Sign-In is not available. Please try again later.',
			);
			setIsGoogleLoading(false);
			return;
		}

		google.accounts.id.initialize({
			client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
			callback: async (response) => {
				if (response.credential) {
					try {
						const res = await authService.googleLogin({
							idToken: response.credential,
						});
						if (res.success) {
							setErrorMessage('');
							// Store tokens in localStorage and set auth header
							if (res.data?.tokens) {
								localStorage.setItem(
									'accessToken',
									res.data.tokens.accessToken,
								);
								localStorage.setItem(
									'refreshToken',
									res.data.tokens.refreshToken,
								);
								// Set auth header for future requests
								const { setAuthToken } = await import('@/src/lib/api');
								setAuthToken(res.data.tokens.accessToken);
							}
							router.push('/');
						} else {
							setErrorMessage(res.message || 'Google login failed.');
						}
					} catch (error) {
						console.error('Google login error:', error);
						setErrorMessage('Google login failed. Please try again.');
					}
				} else {
					setErrorMessage('Google login failed. No credential received.');
				}
				setIsGoogleLoading(false);
			},
		});

		google.accounts.id.prompt();
	};

	return (
		<div className='min-h-screen bg-gray-50 dark:bg-gray-900'>
			<div className='flex flex-col min-h-screen px-6 py-8'>
				<div className='flex items-center mb-8'>
					<Link
						href='/'
						className='rounded-full bg-white dark:bg-gray-800 shadow-md hover:shadow-lg transition-shadow p-3 mr-4'>
						<FaArrowLeft className='text-gray-700 dark:text-gray-300 text-lg' />
					</Link>
					<h2 className='text-2xl font-bold text-gray-800 dark:text-white'>
						Sign In
					</h2>
				</div>

				<div className='flex-1 flex flex-col lg:flex-row max-w-7xl mx-auto w-full'>
					<div className='hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-700 rounded-2xl mr-8 overflow-hidden'>
						<Image
							src={happy}
							alt='Welcome back illustration'
							className='w-full h-full object-cover'
							priority
						/>
					</div>
					<div className='w-full lg:w-1/2 flex flex-col justify-center'>
						<div className='bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 lg:p-12'>
							<h1 className='text-4xl lg:text-5xl font-bold mb-2 text-gray-800 dark:text-white'>
								Welcome Back!
							</h1>
							<p className='text-gray-600 dark:text-gray-400 mb-8 text-lg'>
								Sign in to your account to continue
							</p>

							<form
								onSubmit={handleSubmit}
								className='space-y-6'>
								{(errorMessage || error) && (
									<div className='bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg'>
										<p className='text-sm font-medium'>
											{errorMessage ||
												error?.response?.data?.message ||
												'An error occurred'}
										</p>
									</div>
								)}

								<Input
									label='Email Address'
									type='email'
									name='email'
									placeholder='Enter your email'
									value={formData.email}
									onChange={handleChange}
									size='lg'
									required
								/>

								<div>
									<Input
										label='Password'
										type='password'
										name='password'
										placeholder='Enter your password'
										value={formData.password}
										onChange={handleChange}
										size='lg'
										required
									/>
									<div className='mt-2 text-right'>
										<Link
											href='/auth/forgot-password'
											className='text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:underline'>
											Forgot Password?
										</Link>
									</div>
								</div>

								<Button
									type='submit'
									variant='primary'
									size='lg'
									loading={isPending}
									className='w-full bg-black dark:bg-teal-600 hover:bg-gray-800 dark:hover:bg-teal-700 text-xl font-semibold'>
									{isPending ? 'Signing in...' : 'Sign In'}
								</Button>
							</form>

							<div className='mt-8 text-center'>
								<p className='text-lg text-gray-600 dark:text-gray-400'>
									Don't have an account?{' '}
									<Link
										href='/auth/signup'
										className='text-blue-600 dark:text-blue-400 font-semibold hover:text-blue-800 dark:hover:text-blue-300 hover:underline'>
										Sign up
									</Link>
								</p>
							</div>

							<div className='mt-8'>
								<div className='relative'>
									<div className='absolute inset-0 flex items-center'>
										<div className='w-full border-t border-gray-300 dark:border-gray-600'></div>
									</div>
									<div className='relative flex justify-center text-sm'>
										<span className='px-4 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400'>
											Or continue with
										</span>
									</div>
								</div>

								<div className='mt-6 grid'>
									<Button
										type='button'
										variant='outline'
										size='lg'
										onClick={handleGoogleSignIn}
										loading={isGoogleLoading}
										leftIcon={!isGoogleLoading ? <FcGoogle className='text-2xl' /> : null}
										className='w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'>
										{isGoogleLoading ? 'Signing in...' : 'Google'}
									</Button>

									{/* <button
										type='button'
										className='flex items-center justify-center gap-3 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors'>
										<AiFillApple className='text-2xl' />
										<span className='text-lg font-medium'>Apple</span>
									</button> */}
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default SignIn;
