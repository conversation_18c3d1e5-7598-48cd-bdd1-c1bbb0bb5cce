'use client';
import React, { useState, useEffect } from 'react';
import {
	Clock,
	User,
	Mail,
	Phone,
	ArrowLeft,
	ArrowRight,
	Check,
} from 'lucide-react';
import { Calendar } from '@/src/components/ui/calendar';
import toast, { Toaster } from 'react-hot-toast';
import {
	useAvailableSlots,
	useCreateBooking,
} from '@/src/lib/hooks/useBookings';

// Progress Indicator Component
const ProgressIndicator = ({ currentStep, totalSteps = 3 }) => {
	return (
		<div className='flex items-center justify-center mb-8'>
			{Array.from({ length: totalSteps }, (_, index) => {
				const stepNumber = index + 1;
				return (
					<React.Fragment key={stepNumber}>
						<div
							className={`w-10 h-10 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${
								currentStep >= stepNumber
									? 'bg-teal-500 border-teal-500 text-white'
									: 'border-gray-600 text-gray-400'
							}`}>
							{currentStep > stepNumber ? <Check size={20} /> : stepNumber}
						</div>
						{stepNumber < totalSteps && (
							<div
								className={`w-16 h-0.5 transition-all duration-300 ${
									currentStep > stepNumber ? 'bg-teal-500' : 'bg-gray-600'
								}`}
							/>
						)}
					</React.Fragment>
				);
			})}
		</div>
	);
};

// Coach Info Step Component
const CoachInfoStep = ({ coach, onNext }) => {
	return (
		<div className='text-center space-y-6'>
			<div className='dark:bg-gray-800/50 bg-white/50 backdrop-blur rounded-2xl p-8 border border-gray-700'>
				<div className='w-32 h-32 bg-gradient-to-br from-teal-500 to-teal-600 rounded-full mx-auto mb-6 flex items-center justify-center'>
					<User
						size={48}
						className='text-white'
					/>
				</div>
				<h2 className='text-3xl font-bold mb-2 bg-teal-400 bg-clip-text text-transparent'>
					Book a Session with {coach.name}
				</h2>
				<div className='space-y-4 text-left max-w-md mx-auto'>
					<div className='flex items-center space-x-3'>
						<div className='w-2 h-2 bg-teal-500 rounded-full'></div>
						<span>
							<strong>Coach:</strong> {coach.name}
						</span>
					</div>
					<div className='flex items-center space-x-3'>
						<div className='w-2 h-2 bg-teal-500 rounded-full'></div>
						<span>
							<strong>Title:</strong> {coach.title}
						</span>
					</div>
					<div className='flex items-start space-x-3'>
						<div className='w-2 h-2 bg-teal-500 rounded-full mt-2'></div>
						<div>
							<strong>About:</strong>
							<p className='text-gray-700 mt-1'>{coach.bio}</p>
						</div>
					</div>
				</div>
			</div>
			<div className='flex gap-4 justify-center'>
				<button
					onClick={onNext}
					className='bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white px-8 py-3 rounded-xl font-semibold transition-all transform hover:scale-105 flex items-center gap-2'>
					Continue <ArrowRight size={20} />
				</button>
			</div>
		</div>
	);
};

// Time Slot Component
const TimeSlotButton = ({ time, isSelected, isDisabled, onClick }) => {
	return (
		<button
			onClick={() => onClick(time)}
			disabled={isDisabled}
			className={`p-3 rounded-lg font-medium transition-all ${
				isSelected
					? 'bg-teal-500 text-white transform scale-105'
					: isDisabled
					? 'bg-gray-600 text-gray-500 cursor-not-allowed opacity-50'
					: 'bg-gray-700 hover:bg-gray-600 text-gray-300'
			}`}>
			{time}
		</button>
	);
};

// Date & Time Selection Step Component
const DateTimeStep = ({
	selectedDate,
	selectedTime,
	onDateSelect,
	onTimeSelect,
	onNext,
	onPrev,
	availableSlots,
	isLoadingSlots,
	slotsError,
}) => {
	const [selectedTimeOfDay, setSelectedTimeOfDay] = useState('morning');

	const timeSlots = {
		morning: ['09:00', '10:00', '11:00'],
		afternoon: ['13:00', '14:00', '15:00'],
		evening: ['18:00', '19:00', '20:00'],
	};

	// Get available times for selected date
	const getAvailableTimesForDate = () => {
		if (!selectedDate || !availableSlots?.data)
			return timeSlots[selectedTimeOfDay];

		const dateStr = selectedDate.toISOString().split('T')[0];
		const daySlots = availableSlots.data[dateStr] || [];

		return timeSlots[selectedTimeOfDay].filter((time) =>
			daySlots.some((slot) => slot.time === time && slot.available),
		);
	};

	const availableTimesForPeriod = getAvailableTimesForDate();

	const handleDateSelect = (date) => {
		// Prevent selecting past dates
		const today = new Date();
		today.setHours(0, 0, 0, 0);

		if (date < today) {
			toast.error('Cannot select past dates');
			return;
		}

		onDateSelect(date);
		// Reset time selection when date changes
		onTimeSelect(null);
	};

	const handleTimeSelect = (time) => {
		if (!availableTimesForPeriod.includes(time)) {
			toast.error('This time slot is not available');
			return;
		}
		onTimeSelect(time);
	};

	return (
		<div className='space-y-6'>
			<div className='text-center'>
				<h2 className='text-3xl font-bold mb-2 bg-teal-400 bg-clip-text text-transparent'>
					Choose Your Appointment
				</h2>
				<p className='text-gray-400'>Select your preferred date and time</p>
			</div>

			<div className='flex justify-center'>
				<Calendar
					mode='single'
					selected={selectedDate}
					onSelect={handleDateSelect}
					className='rounded-xl bg-[#D9D9D9] text-black shadow-lg'
					disabled={(date) => {
						const today = new Date();
						today.setHours(0, 0, 0, 0);
						return date < today;
					}}
					modifiers={{
						today: new Date(),
					}}
					modifiersStyles={{
						today: {
							backgroundColor: '#008080',
							color: '#ffff',
							fontWeight: 'bold',
							borderRadius: '8px',
						},
					}}
				/>
			</div>

			{selectedDate && (
				<div className='bg-gray-800/50 backdrop-blur rounded-2xl p-6 border border-gray-700'>
					<h3 className='text-xl font-semibold mb-4 flex items-center gap-2'>
						<Clock
							size={24}
							className='text-teal-400'
						/>
						Available Times
					</h3>

					{isLoadingSlots && (
						<div className='text-center py-4'>
							<div className='w-8 h-8 border-2 border-teal-500 border-t-transparent rounded-full animate-spin mx-auto mb-2' />
							<p className='text-gray-400'>Loading available slots...</p>
						</div>
					)}

					{slotsError && (
						<div className='text-center py-4'>
							<p className='text-red-400'>Failed to load available slots</p>
						</div>
					)}

					{!isLoadingSlots && !slotsError && (
						<>
							<div className='flex justify-center mb-4 bg-gray-700/50 rounded-lg p-1'>
								{Object.keys(timeSlots).map((period) => (
									<button
										key={period}
										onClick={() => setSelectedTimeOfDay(period)}
										className={`px-4 py-2 rounded-lg font-medium transition-all ${
											selectedTimeOfDay === period
												? 'bg-teal-500 text-white'
												: 'text-gray-300 hover:text-white'
										}`}>
										{period.charAt(0).toUpperCase() + period.slice(1)}
									</button>
								))}
							</div>

							<div className='grid grid-cols-3 gap-3'>
								{timeSlots[selectedTimeOfDay].map((time) => (
									<TimeSlotButton
										key={time}
										time={time}
										isSelected={selectedTime === time}
										isDisabled={!availableTimesForPeriod.includes(time)}
										onClick={handleTimeSelect}
									/>
								))}
							</div>

							{availableTimesForPeriod.length === 0 && (
								<div className='text-center py-4'>
									<p className='text-gray-400'>
										No available slots for this time period
									</p>
								</div>
							)}
						</>
					)}
				</div>
			)}

			<div className='flex gap-4 justify-center'>
				<button
					onClick={onPrev}
					className='bg-gray-700 hover:bg-gray-600 text-white px-6 py-3 rounded-xl font-semibold transition-all flex items-center gap-2'>
					<ArrowLeft size={20} /> Back
				</button>
				<button
					onClick={onNext}
					disabled={!selectedDate || !selectedTime}
					className='bg-gradient-to-r from-teal-500 to-teal-700 hover:from-teal-600 hover:to-teal-800 disabled:from-gray-600 disabled:to-gray-600 disabled:cursor-not-allowed text-white px-8 py-3 rounded-xl font-semibold transition-all flex items-center gap-2'>
					Continue <ArrowRight size={20} />
				</button>
			</div>
		</div>
	);
};

// Contact Information Step Component
const ContactInfoStep = ({
	formData,
	onChange,
	onBooking,
	onPrev,
	selectedDate,
	selectedTime,
	coach,
	isLoading,
	isFormValid,
}) => {
	return (
		<div className='space-y-6'>
			<div className='text-center'>
				<h2 className='text-3xl font-bold mb-2 bg-gradient-to-r from-teal-400 to-teal-500 bg-clip-text text-transparent'>
					Complete Your Booking
				</h2>
				<p className='text-gray-400'>
					We need your details to confirm the appointment
				</p>
			</div>

			<div className='dark:bg-gray-800/50 bg-white/50 backdrop-blur rounded-2xl p-6 border border-gray-700'>
				<h3 className='text-lg font-semibold mb-4 text-teal-400'>
					Appointment Summary
				</h3>
				<div className='grid grid-cols-2 gap-4 text-sm'>
					<div>
						<span className='text-gray-400'>Date:</span>
						<p className='font-medium'>
							{selectedDate?.toLocaleDateString('en-US', {
								weekday: 'long',
								year: 'numeric',
								month: 'long',
								day: 'numeric',
							})}
						</p>
					</div>
					<div>
						<span className='text-gray-400'>Time:</span>
						<p className='font-medium'>{selectedTime}</p>
					</div>
					<div>
						<span className='text-gray-400'>Coach:</span>
						<p className='font-medium'>{coach.name}</p>
					</div>
					<div>
						<span className='text-gray-400'>Session Type:</span>
						<p className='font-medium'>Life Coaching</p>
					</div>
				</div>
			</div>

			<div className='dark:bg-gray-800/50 bg-white/50 backdrop-blur rounded-2xl p-6 border border-gray-700'>
				<div className='space-y-4'>
					<div className='relative'>
						<User
							className='absolute left-3 top-3.5 text-gray-400'
							size={20}
						/>
						<input
							type='text'
							name='name'
							value={formData.name}
							onChange={onChange}
							placeholder='Enter your full name'
							className='w-full pl-12 pr-4 py-3 border border-gray-600 rounded-xl focus:border-teal-500 focus:ring-2 focus:ring-teal-500/20 outline-none transition-all'
							required
						/>
					</div>
					<div className='relative'>
						<Mail
							className='absolute left-3 top-3.5 text-gray-400'
							size={20}
						/>
						<input
							type='email'
							name='email'
							value={formData.email}
							onChange={onChange}
							placeholder='Enter your email'
							className='w-full pl-12 pr-4 py-3 border border-gray-600 rounded-xl focus:border-teal-500 focus:ring-2 focus:ring-teal-500/20 outline-none transition-all'
							required
						/>
					</div>
					<div className='relative'>
						<Phone
							className='absolute left-3 top-3.5 text-gray-400'
							size={20}
						/>
						<input
							type='tel'
							name='phone'
							value={formData.phone}
							onChange={onChange}
							placeholder='Enter your phone number'
							className='w-full pl-12 pr-4 py-3 border border-gray-600 rounded-xl focus:border-teal-500 focus:ring-2 focus:ring-teal-500/20 outline-none transition-all'
							required
						/>
					</div>
					<div className='relative'>
						<textarea
							name='notes'
							value={formData.notes}
							onChange={onChange}
							placeholder='Any additional notes (optional)'
							rows={3}
							className='w-full px-4 py-3 border border-gray-600 rounded-xl focus:border-teal-500 focus:ring-2 focus:ring-teal-500/20 outline-none transition-all resize-none'
						/>
					</div>
				</div>
			</div>

			<div className='flex gap-4 justify-center'>
				<button
					onClick={onPrev}
					disabled={isLoading}
					className='bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl font-semibold transition-all flex items-center gap-2'>
					<ArrowLeft size={20} /> Back
				</button>
				<button
					onClick={onBooking}
					disabled={!isFormValid || isLoading}
					className='bg-gradient-to-r from-teal-500 to-blue-500 hover:from-teal-600 hover:to-blue-600 disabled:from-gray-600 disabled:to-gray-600 disabled:cursor-not-allowed text-white px-8 py-3 rounded-xl font-semibold transition-all flex items-center gap-2'>
					{isLoading ? (
						<>
							<div className='w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin' />
							Booking...
						</>
					) : (
						<>
							<Check size={20} /> Book Appointment
						</>
					)}
				</button>
			</div>
		</div>
	);
};

// Main Session Component
const Session = () => {
	const [step, setStep] = useState(1);
	const [selectedDate, setSelectedDate] = useState(null);
	const [selectedTime, setSelectedTime] = useState(null);
	const [formData, setFormData] = useState({
		name: '',
		email: '',
		phone: '',
		notes: '',
	});

	const coach = {
		name: 'Craig',
		title: 'Life Coach',
		bio: "Craig is the creator of YOU = LIFE, empowering you to live with clarity and purpose. He's your champion for transformation.",
		image: '/api/placeholder/128/128',
	};

	// Get available slots for selected date
	const {
		data: availableSlots,
		isLoading: isLoadingSlots,
		error: slotsError,
		refetch: refetchSlots,
	} = useAvailableSlots(
		selectedDate ? { date: selectedDate.toISOString().split('T')[0] } : {},
		{ enabled: !!selectedDate },
	);

	// Create booking mutation
	const createBookingMutation = useCreateBooking();

	const nextStep = () => setStep(step + 1);
	const prevStep = () => setStep(step - 1);

	const handleChange = (e) => {
		setFormData({ ...formData, [e.target.name]: e.target.value });
	};

	const handleDateSelect = (date) => {
		setSelectedDate(date);
		setSelectedTime(null); // Reset time when date changes

		// Refetch available slots for the new date
		if (date) {
			refetchSlots();
		}
	};

	const handleTimeSelect = (time) => {
		setSelectedTime(time);
	};

	const handleBooking = async () => {
		if (!isFormValid()) {
			toast.error('Please fill in all required fields');
			return;
		}

		try {
			const [hours, minutes] = selectedTime.split(':');
			const bookingDateTime = new Date(selectedDate);
			bookingDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);

			const bookingData = {
				date: bookingDateTime,
				email: formData.email.toLowerCase(),
				bookedBy: formData.name,
				phone: formData.phone,
				notes: formData.notes,
				sessionType: 'Discovery', // Default session type
				status: 'scheduled',
			};

			await createBookingMutation.mutateAsync(bookingData);

			toast.success('Appointment booked successfully!', {
				duration: 5000,
				style: {
					background: '#10B981',
					color: '#fff',
				},
			});

			// Reset form or redirect
			setStep(1);
			setSelectedDate(null);
			setSelectedTime(null);
			setFormData({ name: '', email: '', phone: '', notes: '' });
		} catch (error) {
			console.error('Booking error:', error);

			let errorMessage = 'Failed to book appointment. Please try again.';

			if (error.message) {
				errorMessage = error.message;
			} else if (error.errors && error.errors.length > 0) {
				errorMessage = error.errors[0];
			}

			toast.error(errorMessage, {
				duration: 5000,
				style: {
					background: '#EF4444',
					color: '#fff',
				},
			});
		}
	};

	const isFormValid = () => {
		return (
			selectedDate &&
			selectedTime &&
			formData.name.trim() &&
			formData.email.trim() &&
			formData.phone.trim()
		);
	};

	return (
		<div className='min-h-screen '>
			<Toaster position='top-right' />

			<div className='max-w-2xl mx-auto p-6 pt-20'>
				<ProgressIndicator currentStep={step} />

				{step === 1 && (
					<CoachInfoStep
						coach={coach}
						onNext={nextStep}
					/>
				)}

				{step === 2 && (
					<DateTimeStep
						selectedDate={selectedDate}
						selectedTime={selectedTime}
						onDateSelect={handleDateSelect}
						onTimeSelect={handleTimeSelect}
						onNext={nextStep}
						onPrev={prevStep}
						availableSlots={availableSlots}
						isLoadingSlots={isLoadingSlots}
						slotsError={slotsError}
					/>
				)}

				{step === 3 && (
					<ContactInfoStep
						formData={formData}
						onChange={handleChange}
						onBooking={handleBooking}
						onPrev={prevStep}
						selectedDate={selectedDate}
						selectedTime={selectedTime}
						coach={coach}
						isLoading={createBookingMutation.isLoading}
						isFormValid={isFormValid}
					/>
				)}
			</div>
		</div>
	);
};

export default Session;
