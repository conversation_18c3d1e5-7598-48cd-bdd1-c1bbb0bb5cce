'use client';
import { SplashScreen } from '@/src/components/common';
import Onboard from '@/src/components/common/Onboard';
import HomePage from '@/src/components/Home/HomePage';
import AuthErrorBoundary from '@/src/components/layout/AuthErrorBoundary';
import { isAuthenticated, setHeaderAuthorization } from '@/src/lib/hooks';
import { useEffect, useState } from 'react';

export default function Home() {
	const [showSplash, setShowSplash] = useState(() => {
		if (typeof window !== 'undefined') {
			return localStorage.getItem('hasSeenSplash') !== 'true';
		}
		return true;
	});

	const [isUserAuthenticated, setIsUserAuthenticated] = useState(false);
	const [isCheckingAuth, setIsCheckingAuth] = useState(true);

	useEffect(() => {
		if (typeof window === 'undefined') return;

		const hasSeenSplash = localStorage.getItem('hasSeenSplash');

		if (!hasSeenSplash) {
			const timer = setTimeout(() => {
				setShowSplash(false);
				localStorage.setItem('hasSeenSplash', 'true');
			}, 3000);

			return () => clearTimeout(timer);
		} else {
			setShowSplash(false);
		}
	}, []);

	useEffect(() => {
		const checkAuthStatus = () => {
			setIsCheckingAuth(true);
			const authenticated = isAuthenticated();
			setIsUserAuthenticated(authenticated);

			if (authenticated) {
				const token = localStorage.getItem('accessToken');
				setHeaderAuthorization(token);
			} else {
				setHeaderAuthorization(null);
			}
			setIsCheckingAuth(false);
		};

		checkAuthStatus();

		const handleStorageChange = (e) => {
			if (e.key === 'accessToken' || e.key === 'refreshToken') {
				checkAuthStatus();
			}
		};

		const handleAuthChange = () => {
			checkAuthStatus();
		};

		// Listen for notification click messages from service worker
		const handleServiceWorkerMessage = (event) => {
			if (event.data?.type === 'NOTIFICATION_CLICK') {
				const { url } = event.data;
				if (url && typeof window !== 'undefined') {
					// Navigate to the URL
					window.location.href = url;
				}
			}
		};

		window.addEventListener('storage', handleStorageChange);
		window.addEventListener('auth-change', handleAuthChange);

		// Listen for service worker messages
		if ('serviceWorker' in navigator) {
			navigator.serviceWorker.addEventListener(
				'message',
				handleServiceWorkerMessage,
			);
		}

		return () => {
			window.removeEventListener('storage', handleStorageChange);
			window.removeEventListener('auth-change', handleAuthChange);
			if ('serviceWorker' in navigator) {
				navigator.serviceWorker.removeEventListener(
					'message',
					handleServiceWorkerMessage,
				);
			}
		};
	}, []);

	if (showSplash || isCheckingAuth) {
		return (
			<div className='w-screen h-screen'>
				<SplashScreen />
			</div>
		);
	}

	return (
		<div className='w-screen h-screen'>
			{isUserAuthenticated ? (
				<AuthErrorBoundary>
					<HomePage />
				</AuthErrorBoundary>
			) : (
				<Onboard />
			)}
		</div>
	);
}
