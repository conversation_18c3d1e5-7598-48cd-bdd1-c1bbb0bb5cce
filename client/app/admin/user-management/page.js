'use client';
import React, { useState } from 'react';
import { FaSearch, FaEdit, FaTrash } from 'react-icons/fa';
import { toast } from 'react-hot-toast';
import {
	useDeleteUser,
	useGetAllUsers,
	useUpdateUserStatus,
} from '@/src/lib/hooks/useUser';
import AdminLayout from '@/src/components/layout/AdminLayout';
import { Pagination } from '@/src/components/common';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/src/components/ui/dialog';
import { Button } from '@/src/components/ui/button';

const UserManagement = () => {
	const [currentPage, setCurrentPage] = useState(1);
	const [searchTerm, setSearchTerm] = useState('');
	const [showCreateModal, setShowCreateModal] = useState(false);
	const [deleteDialog, setDeleteDialog] = useState({
		isOpen: false,
		user: null,
	});

	const {
		data: allUsers,
		isLoading,
		error,
	} = useGetAllUsers({
		page: currentPage,
		limit: 10,
		search: searchTerm,
	});

	const updateUserStatusMutation = useUpdateUserStatus();
	const deleteUserMutation = useDeleteUser();

	const users = allUsers?.data || [];
	const totalPages = allUsers?.pagination?.totalPages || 1;

	const handleSearch = (e) => {
		setSearchTerm(e.target.value);
		setCurrentPage(1); // Reset to first page when searching
	};

	const handleStatusChange = async (userId, newStatus) => {
		try {
			await updateUserStatusMutation.mutateAsync({ userId, status: newStatus });
			toast.success('User status updated successfully');
		} catch (error) {
			toast.error('Failed to update user status');
			console.error('Error updating user status:', error);
		}
	};

	const openDeleteDialog = (user) => {
		setDeleteDialog({
			isOpen: true,
			user,
		});
	};

	const closeDeleteDialog = () => {
		setDeleteDialog({
			isOpen: false,
			user: null,
		});
	};

	const handleDeleteUser = async () => {
		const { user } = deleteDialog;

		try {
			await deleteUserMutation.mutateAsync(user._id);
			toast.success('User deleted successfully');
		} catch (error) {
			toast.error('Failed to delete user');
			console.error('Error deleting user:', error);
		} finally {
			closeDeleteDialog();
		}
	};

	const formatDate = (dateString) => {
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	};

	return (
		<AdminLayout>
			<div className='p-6 bg-cyan-100 text-black min-h-screen'>
				{/* Header */}
				<div className='flex justify-between items-center mb-6'>
					<div className='flex items-center border rounded-md bg-white px-3 py-2 w-full max-w-sm'>
						<FaSearch className='text-gray-500 mr-2' />
						<input
							type='text'
							value={searchTerm}
							onChange={handleSearch}
							placeholder='Search by name or email...'
							className='outline-none w-full text-sm'
						/>
					</div>
					<button
						onClick={() => setShowCreateModal(true)}
						className='ml-4 px-4 py-2 bg-teal-600 text-white rounded-md shadow hover:bg-teal-700 transition-all'>
						Create New User
					</button>
				</div>

				{/* Table */}
				<div className='overflow-x-auto rounded-md'>
					<table className='w-full bg-white text-sm'>
						<thead>
							<tr className='bg-teal-100 text-left text-gray-800'>
								<th className='px-4 py-2'>Name</th>
								<th className='px-4 py-2'>Email</th>
								<th className='px-4 py-2'>Status</th>
								<th className='px-4 py-2'>Reg. Date</th>
								<th className='px-4 py-2'>Action</th>
							</tr>
						</thead>
						<tbody>
							{isLoading ? (
								<tr>
									<td
										colSpan='5'
										className='px-4 py-8 text-center text-gray-500'>
										Loading users...
									</td>
								</tr>
							) : error ? (
								<tr>
									<td
										colSpan='5'
										className='px-4 py-8 text-center text-red-500'>
										Error loading users: {error.message}
									</td>
								</tr>
							) : users.length === 0 ? (
								<tr>
									<td
										colSpan='5'
										className='px-4 py-8 text-center text-gray-500'>
										No users found
									</td>
								</tr>
							) : (
								users.map((user, index) => (
									<tr
										key={user._id}
										className={`${
											index % 2 === 0 ? 'bg-gray-100' : 'bg-white'
										} hover:bg-teal-50 transition-colors`}>
										<td className='px-4 py-2'>
											<div className='flex items-center'>
												{user.avatar ? (
													<img
														src={user.avatar}
														alt={user.name}
														className='w-8 h-8 rounded-full mr-2'
													/>
												) : (
													<div className='w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center mr-2 text-sm font-semibold text-gray-600'>
														{user.name?.charAt(0)?.toUpperCase()}
													</div>
												)}
												<div>
													<div className='font-medium'>{user.name}</div>
													<div className='text-xs text-gray-500'>
														{user.roles?.join(', ')}
													</div>
												</div>
											</div>
										</td>
										<td className='px-4 py-2'>{user.email}</td>
										<td className='px-4 py-2'>
											<select
												value={user.status}
												onChange={(e) =>
													handleStatusChange(user._id, e.target.value)
												}
												className={`px-3 py-1 rounded-full text-xs font-semibold border ${
													user.status === 'active'
														? 'bg-teal-100 text-teal-800 border-teal-300'
														: user.status === 'suspended'
														? 'bg-red-100 text-red-800 border-red-300'
														: 'bg-gray-100 text-gray-800 border-gray-300'
												}`}
												disabled={updateUserStatusMutation.isLoading}>
												<option value='active'>Active</option>
												<option value='inactive'>Inactive</option>
												<option value='suspended'>Suspended</option>
											</select>
										</td>
										<td className='px-4 py-2 text-sm text-gray-600'>
											{formatDate(user.createdAt)}
										</td>
										<td className='px-4 py-2'>
											<div className='flex items-center space-x-2'>
												<button
													onClick={() => console.log('Edit user:', user._id)}
													className='p-1 text-teal-600 hover:bg-teal-100 rounded transition-colors'
													title='Edit user'>
													<FaEdit size={14} />
												</button>
												<button
													onClick={() => openDeleteDialog(user)}
													className='p-1 text-red-600 hover:bg-red-100 rounded transition-colors'
													title='Delete user'
													disabled={deleteUserMutation.isLoading}>
													<FaTrash size={12} />
												</button>
											</div>
										</td>
									</tr>
								))
							)}
						</tbody>
					</table>
				</div>

				{/* Pagination */}
				<Pagination
					currentPage={currentPage}
					totalPages={totalPages}
					onPageChange={setCurrentPage}
				/>

				{/* Delete Confirmation Dialog */}
				<Dialog
					open={deleteDialog.isOpen}
					onOpenChange={closeDeleteDialog}>
					<DialogContent className='sm:max-w-[425px]'>
						<DialogHeader>
							<DialogTitle className='text-teal-800'>Delete User</DialogTitle>
							<DialogDescription className='text-gray-600'>
								Are you sure you want to delete user "{deleteDialog.user?.name}
								"? This action cannot be undone.
							</DialogDescription>
						</DialogHeader>
						<DialogFooter className='flex gap-2 sm:gap-0'>
							<Button
								variant='outline'
								onClick={closeDeleteDialog}
								className='border-teal-300 text-teal-700 hover:bg-teal-50'>
								Cancel
							</Button>
							<Button
								onClick={handleDeleteUser}
								className='bg-red-600 hover:bg-red-700 text-white'
								disabled={deleteUserMutation.isLoading}>
								{deleteUserMutation.isLoading ? 'Deleting...' : 'Delete'}
							</Button>
						</DialogFooter>
					</DialogContent>
				</Dialog>
			</div>
		</AdminLayout>
	);
};

export default UserManagement;
