'use client';
import { Pagination } from '@/src/components/common';
import AdminLayout from '@/src/components/layout/AdminLayout';
import React, { useState } from 'react';
import { FaSearch } from 'react-icons/fa';

const users = [
	{
		name: '<PERSON>',
		email: '<EMAIL>',
		status: 'Active',
		date: '02/02/2025',
	},
	{
		name: '<PERSON>',
		email: 'johndo<PERSON>@gmail.com',
		status: 'Active',
		date: '29/01/2025',
	},
	{
		name: '<PERSON>',
		email: '<EMAIL>',
		status: 'Inactive',
		date: '26/01/2025',
	},
	{
		name: '<PERSON>',
		email: 'aaeck<PERSON>@gmail.com',
		status: 'Active',
		date: '24/01/2025',
	},
	{
		name: '<PERSON>',
		email: '<EMAIL>',
		status: 'Inactive',
		date: '24/01/2025',
	},
	{
		name: '<PERSON>',
		email: 'johndo<PERSON>@gmail.com',
		status: 'Active',
		date: '29/01/2025',
	},
	{
		name: '<PERSON>',
		email: '<EMAIL>',
		status: 'Inactive',
		date: '26/01/2025',
	},
	{
		name: '<PERSON> <PERSON>ck<PERSON>',
		email: 'aaeck<PERSON>@gmail.com',
		status: 'Active',
		date: '24/01/2025',
	},
	{
		name: 'Elena Eckhart',
		email: '<EMAIL>',
		status: 'Inactive',
		date: '24/01/2025',
	},
	{
		name: '<PERSON> <PERSON>e',
		email: '<EMAIL>',
		status: 'Active',
		date: '29/01/2025',
	},
];

const BookingManagement = () => {
	const [currentPage, setCurrentPage] = useState(1);
	const totalPages = 25;

	return (
		<AdminLayout>
			<div className='p-6 bg-cyan-100 text-black min-h-screen'>
				{/* Header */}
				<div className='flex justify-between items-center mb-6'>
					<div className='flex items-center border rounded-md bg-white px-3 py-2 w-full max-w-sm'>
						<FaSearch className='text-gray-500 mr-2' />
						<input
							type='text'
							placeholder='Search'
							className='outline-none w-full text-sm'
						/>
					</div>
					<button className='ml-4 px-4 py-2 bg-teal-600 text-white rounded-md shadow hover:bg-teal-700 transition-all'>
						Create New Users
					</button>
				</div>

				{/* Table */}
				<div className='overflow-x-auto rounded-md'>
					<table className='w-full bg-white text-sm'>
						<thead>
							<tr className='bg-blue-100 text-left text-gray-800'>
								<th className='px-4 py-2'>Date & Time</th>
								<th className='px-4 py-2'>User</th>
								<th className='px-4 py-2'>Session</th>
								<th className='px-4 py-2'>Status</th>
								<th className='px-4 py-2'>Action</th>
							</tr>
						</thead>
						<tbody>
							{users.map((user, index) => (
								<tr
									key={index}
									className={`${index % 2 === 0 ? 'bg-gray-100' : 'bg-white'}`}>
									<td className='px-4 py-2'>{user.email}</td>
									<td className='px-4 py-2'>{user.name}</td>
									<td className='px-4 py-2 font-semibold'>
										{user.status === 'Active' ? (
											<span className='text-green-600'>{user.status}</span>
										) : (
											<span className='text-red-500'>{user.status}</span>
										)}
									</td>
									<td className='px-4 py-2'>{user.date}</td>
									<td className='px-4 py-2'>
										<select
											className='border rounded px-2 py-1 text-sm bg-gray-50'
											defaultValue={
												user.status === 'Active' ? 'deactivate' : 'activate'
											}>
											<option value='activate'>Activate</option>
											<option value='deactivate'>Deactivate</option>
										</select>
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>

				{/* Pagination */}
				<Pagination
					currentPage={currentPage}
					totalPages={totalPages}
					onPageChange={setCurrentPage}
				/>
			</div>
		</AdminLayout>
	);
};

export default BookingManagement;
