'use client';
import React, { useState, useEffect } from 'react';
import { FaChevronDown } from 'react-icons/fa';
import toast from 'react-hot-toast';
import {
	checkExistingSubscription,
	useSendNotification,
	useSubscribeToNotifications,
	useUnsubscribeFromNotifications,
	useUserProfile,
} from '@/src/lib/hooks';
import { NotificationBroadcast } from '@/src/components/common';
import AdminLayout from '@/src/components/layout/AdminLayout';

const Settings = () => {
	const [isSubscribed, setIsSubscribed] = useState(false);

	const [testNotification, setTestNotification] = useState({
		title: '',
		message: '',
	});
	const [loading, setLoading] = useState(false);

	const { data: userProfile } = useUserProfile();
	const userData = userProfile?.data?.data;
	console.log(userData);

	const subscribeNotificationMutation = useSubscribeToNotifications(userData);
	const unsubscribeNotificationMutation =
		useUnsubscribeFromNotifications(userData);
	const sendNotificationMutation = useSendNotification();

	// Check subscription status on component mount
	useEffect(() => {
		const checkSubscription = async () => {
			if (isNotificationSupported()) {
				const subscribed = await checkExistingSubscription();
				setIsSubscribed(subscribed);
			}
		};
		checkSubscription();
	}, []);

	const handleSubscriptionToggle = async () => {
		setLoading(true);
		try {
			if (isSubscribed) {
				await unsubscribeNotificationMutation.mutateAsync();
				setIsSubscribed(false);
				toast.success('🔕 Push notifications disabled');
			} else {
				await subscribeNotificationMutation.mutateAsync();
				setIsSubscribed(true);
				toast.success('🔔 Push notifications enabled');
			}
		} catch (error) {
			toast.error(
				`Failed to ${isSubscribed ? 'disable' : 'enable'} notifications`,
			);
		} finally {
			setLoading(false);
		}
	};

	const handleTestNotification = async () => {
		if (!testNotification.title || !testNotification.message) {
			toast.error('Please provide both title and message');
			return;
		}

		if (!userData?._id) {
			toast.error('User data not available');
			return;
		}

		setLoading(true);
		try {
			await sendNotificationMutation.mutateAsync({
				recipientUid: userData._id,
				senderName: 'Admin Test',
				messageContent: testNotification.message,
			});
			toast.success('Test notification sent!');
			setTestNotification({ title: '', message: '' });
		} catch (error) {
			toast.error('Failed to send test notification');
		} finally {
			setLoading(false);
		}
	};

	const handleBroadcastToAll = async () => {
		if (!testNotification.title || !testNotification.message) {
			toast.error('Please provide both title and message');
			return;
		}

		const confirmed = window.confirm(
			'Are you sure you want to send this notification to ALL users? This action cannot be undone.',
		);

		if (!confirmed) return;

		setLoading(true);
		try {
			// Send to all users by using a special recipient ID
			await sendNotificationMutation.mutateAsync({
				recipientUid: 'all',
				senderName: 'YouLife Admin',
				messageContent: testNotification.message,
			});
			toast.success('Notification broadcasted to all users!');
			setTestNotification({ title: '', message: '' });
		} catch (error) {
			toast.error('Failed to broadcast notification');
		} finally {
			setLoading(false);
		}
	};

	return (
		<AdminLayout>
			<div className='flex flex-col md:flex-row justify-between p-6 bg-gradient-to-b from-teal-100 to-white min-h-screen'>
				<div className='bg-white p-6 rounded-lg shadow-md mb-6 md:mb-0 md:mr-6 w-full md:w-1/2'>
					<h2 className='text-xl font-semibold text-black mb-4'>
						General Settings
					</h2>
					<div className='mb-4'>
						<label className='block text-black text-sm font-semibold mb-2'>
							Name
						</label>
						<input
							type='text'
							defaultValue='YOULIFE'
							className='w-full p-3 border border-gray-300 rounded-lg bg-gray-50 text-black'
						/>
					</div>
					<div className='mb-4'>
						<label className='block text-black text-sm font-semibold mb-2'>
							Email
						</label>
						<input
							type='email'
							defaultValue='<EMAIL>'
							className='w-full p-3 border border-gray-300 rounded-lg bg-gray-50 text-black'
						/>
					</div>
					<div className='mb-6'>
						<label className='block text-black text-sm font-semibold mb-2'>
							Default Timezone
						</label>
						<div className='relative'>
							<select className='w-full p-3 border border-gray-300 rounded-lg bg-gray-50 text-black appearance-none'>
								<option>Select Timezone</option>
							</select>
							<FaChevronDown className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500' />
						</div>
					</div>
					<button className='bg-teal-600 text-white px-6 py-2 rounded-lg hover:bg-teal-700 font-medium'>
						Save Changes
					</button>
				</div>
				<div className='flex flex-col w-full md:w-1/2'>
					<div className='bg-white p-6 rounded-lg shadow-md mb-6'>
						<h2 className='text-xl font-semibold text-black mb-4'>
							User Registration
						</h2>
						<div className='flex items-center justify-between mb-6'>
							<span className='text-black font-medium'>
								Allow New User Registration
							</span>
							<label className='relative inline-flex items-center cursor-pointer'>
								<input
									type='checkbox'
									defaultChecked
									className='sr-only peer'
								/>
								<div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-600"></div>
							</label>
						</div>
						<button className='bg-teal-600 text-white px-6 py-2 rounded-lg hover:bg-teal-700 font-medium'>
							Save Changes
						</button>
					</div>
					<div className='bg-white p-6 rounded-lg shadow-md mb-6'>
						<h2 className='text-xl font-semibold text-black mb-4'>
							Notifications Settings
						</h2>
						<div className='flex items-center justify-between mb-6'>
							<span className='text-black font-medium'>
								Admin Notification Email
							</span>
							<label className='relative inline-flex items-center cursor-pointer'>
								<input
									type='checkbox'
									defaultChecked
									className='sr-only peer'
								/>
								<div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-600"></div>
							</label>
						</div>
						<button className='bg-teal-600 text-white px-6 py-2 rounded-lg hover:bg-teal-700 font-medium'>
							Save Changes
						</button>
					</div>
					<NotificationBroadcast />
				</div>
			</div>
		</AdminLayout>
	);
};

export default Settings;
