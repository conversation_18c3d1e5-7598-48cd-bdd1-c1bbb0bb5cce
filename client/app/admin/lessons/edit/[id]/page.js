'use client';
import LessonEditForm from '@/src/components/features/lessons/LessonEditForm';
import ProtectedRoute from '@/src/components/layout/ProtectedRoute';
import { use } from 'react';

export default function EditLessonPage({ params }) {
	const unwrappedParams = use(params);
	return (
		<ProtectedRoute requiredRoles={['superadmin', 'tutor']}>
			<LessonEditForm lessonId={unwrappedParams.id} />
		</ProtectedRoute>
	);
}
