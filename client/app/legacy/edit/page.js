'use client';
import FixedHeader from '@/src/components/layout/FixedHeader';
import Sidebar from '@/src/components/layout/Sidebar';
import React, { useState } from 'react';
import {
	HiOutlinePhotograph,
	HiPlay,
	HiStar,
	HiChevronUp,
	HiChevronDown,
} from 'react-icons/hi';

const StarRating = () => (
	<div className='flex items-center space-x-1'>
		{[...Array(4)].map((_, i) => (
			<HiStar
				key={`filled-${i}`}
				className='h-6 w-6 text-yellow-400'
			/>
		))}
		<HiStar className='h-6 w-6 text-white/50' />
	</div>
);

export default function EditLegacyInterface() {
	// Controls which sections are open
	const [expandedSections, setExpandedSections] = useState({});

	// Toggle specific section
	const toggleSection = (section) => {
		setExpandedSections((prev) => ({
			[section]: !prev[section],
		}));
	};

	// Section definitions
	const sections = [
		{ id: 'basicInfo', title: 'Basic Info' },
		{ id: 'biography', title: 'Biography' },
		{ id: 'image', title: 'Image' },
		{ id: 'lessons', title: 'Lessons' },
		{ id: 'reviews', title: 'Reviews' },
		{ id: 'stories', title: 'Stories' },
	];

	const [isSidebarOpen, setIsSidebarOpen] = useState(false);
	const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);

	return (
		<div className='flex flex-col min-h-screen'>
			<FixedHeader
				title='Edit Legacy'
				toggleSidebar={toggleSidebar}
			/>
			<p className='px-6 mb-6 text-xl font-semibold'>
				What do you want people to think of you when you're dead?
			</p>

			<div className='space-y-2 px-6'>
				{/* Basic Info */}
				<div className='mb-4'>
					<button
						onClick={() => toggleSection('basicInfo')}
						className={`w-full flex justify-between items-center p-4 text-left bg-[#00a99d] text-white ${
							expandedSections.basicInfo ? 'rounded-t-xl' : 'rounded-xl'
						}`}>
						<span className='font-bold text-xl'>Basic Info</span>
						{expandedSections.basicInfo ? (
							<HiChevronUp className='h-6 w-6' />
						) : (
							<HiChevronDown className='h-6 w-6' />
						)}
					</button>
					{expandedSections.basicInfo && (
						<div className='bg-[#00a99d] px-4 pb-4 rounded-b-lg'>
							<p className='text-white/90 text-sm mb-2 pt-2'>Your Full Name</p>
							<input
								type='text'
								placeholder='Enter your full name'
								className='w-full p-3 rounded-lg bg-white text-sm text-black placeholder:text-gray-400'
							/>
							<div className='flex justify-end mt-3'>
								<button className='bg-black text-white px-5 py-2 rounded-lg text-sm font-semibold'>
									Save
								</button>
							</div>
						</div>
					)}
				</div>

				{/* Biography */}
				<div className='mb-4'>
					<button
						onClick={() => toggleSection('biography')}
						className={`w-full flex justify-between items-center p-4 text-left bg-[#00a99d] text-white ${
							expandedSections.biography ? 'rounded-t-xl' : 'rounded-xl'
						}`}>
						<span className='font-bold text-xl'>Biography</span>
						{expandedSections.biography ? (
							<HiChevronUp className='h-6 w-6' />
						) : (
							<HiChevronDown className='h-6 w-6' />
						)}
					</button>
					{expandedSections.biography && (
						<div className='bg-[#00a99d] px-4 pb-4 rounded-b-lg'>
							<p className='text-white/90 text-sm mb-2 pt-2'>A Short Bio</p>
							<textarea
								placeholder='Write a short bio about you'
								className='w-full p-3 rounded-lg bg-white text-sm text-black placeholder:text-gray-400 h-24 resize-none'
							/>
							<div className='flex justify-end mt-3'>
								<button className='bg-black text-white px-5 py-2 rounded-lg text-sm font-semibold'>
									Save
								</button>
							</div>
						</div>
					)}
				</div>

				{/* Image Upload */}
				<div className='mb-4'>
					<button
						onClick={() => toggleSection('image')}
						className={`w-full flex justify-between items-center p-4 text-left bg-[#00a99d] text-white ${
							expandedSections.image ? 'rounded-t-xl' : 'rounded-xl'
						}`}>
						<span className='font-bold text-xl'>Image</span>
						{expandedSections.image ? (
							<HiChevronUp className='h-6 w-6' />
						) : (
							<HiChevronDown className='h-6 w-6' />
						)}
					</button>
					{expandedSections.image && (
						<div className='bg-[#00a99d] px-4 pb-4 rounded-b-lg'>
							<p className='text-white/90 text-sm mb-2 pt-2'>
								Add your best images
							</p>
							<input
								type='text'
								placeholder='Add image URL'
								className='w-full p-3 rounded-lg bg-white text-sm text-black placeholder:text-gray-400 mb-3'
							/>
							<div className='bg-gray-100 rounded-lg p-6 flex flex-col items-center justify-center text-center'>
								<HiOutlinePhotograph className='w-10 h-10 text-gray-400 mb-2' />
								<span className='text-gray-500 text-xs'>
									drag & drop your image here
								</span>
							</div>
							<div className='flex justify-end mt-3'>
								<button className='bg-black text-white px-5 py-2 rounded-lg text-sm font-semibold'>
									Save
								</button>
							</div>
						</div>
					)}
				</div>

				{/* Lessons */}
				<div className='mb-4'>
					<button
						onClick={() => toggleSection('lessons')}
						className={`w-full flex justify-between items-center p-4 text-left bg-[#00a99d] text-white ${
							expandedSections.lessons ? 'rounded-t-xl' : 'rounded-xl'
						}`}>
						<span className='font-bold text-xl'>Lessons</span>
						{expandedSections.lessons ? (
							<HiChevronUp className='h-6 w-6' />
						) : (
							<HiChevronDown className='h-6 w-6' />
						)}
					</button>
					{expandedSections.lessons && (
						<div className='bg-[#00a99d] px-4 pb-4 rounded-b-lg'>
							<p className='text-white/90 text-sm mb-2 pt-2'>
								What have you learnt?
							</p>
							<input
								type='text'
								placeholder='Lesson Title'
								className='w-full p-3 rounded-lg bg-white text-sm text-black placeholder:text-gray-400 mb-3'
							/>
							<textarea
								placeholder='Description'
								className='w-full p-3 rounded-lg bg-white text-sm text-black placeholder:text-gray-400 h-24 resize-none mb-3'
							/>
							<div className='bg-white/20 backdrop-blur-sm rounded-lg p-6 flex items-center justify-center'>
								<div className='bg-black rounded-full h-12 w-12 flex items-center justify-center'>
									<HiPlay className='w-7 h-7 text-white' />
								</div>
							</div>
							<div className='flex justify-end mt-3'>
								<button className='bg-black text-white px-5 py-2 rounded-lg text-sm font-semibold'>
									Save
								</button>
							</div>
						</div>
					)}
				</div>

				{/* Reviews */}
				<div className='mb-4'>
					<button
						onClick={() => toggleSection('reviews')}
						className={`w-full flex justify-between items-center p-4 text-left bg-[#00a99d] text-white ${
							expandedSections.reviews ? 'rounded-t-xl' : 'rounded-xl'
						}`}>
						<span className='font-bold text-xl'>Reviews</span>
						{expandedSections.reviews ? (
							<HiChevronUp className='h-6 w-6' />
						) : (
							<HiChevronDown className='h-6 w-6' />
						)}
					</button>
					{expandedSections.reviews && (
						<div className='bg-[#00a99d] px-4 pb-4 rounded-b-lg'>
							<p className='text-white/90 text-sm mb-2 pt-2'>
								What people say about you
							</p>
							<input
								type='text'
								placeholder='Reviewer Name'
								className='w-full p-3 rounded-lg bg-white text-sm text-black placeholder:text-gray-400 mb-3'
							/>
							<textarea
								placeholder="Write from the reviewer's POV"
								className='w-full p-3 rounded-lg bg-white text-sm text-black placeholder:text-gray-400 h-24 resize-none mb-3'
							/>
							<div className='flex justify-between items-center'>
								<StarRating />
								<button className='bg-black text-white px-5 py-2 rounded-lg text-sm font-semibold'>
									Save
								</button>
							</div>
						</div>
					)}
				</div>

				{/* Stories */}
				<div className='mb-4'>
					<button
						onClick={() => toggleSection('stories')}
						className={`w-full flex justify-between items-center p-4 text-left bg-[#00a99d] text-white ${
							expandedSections.stories ? 'rounded-t-xl' : 'rounded-xl'
						}`}>
						<span className='font-bold text-xl'>Stories</span>
						{expandedSections.stories ? (
							<HiChevronUp className='h-6 w-6' />
						) : (
							<HiChevronDown className='h-6 w-6' />
						)}
					</button>
					{expandedSections.stories && (
						<div className='bg-[#00a99d] px-4 pb-4 rounded-b-lg'>
							<p className='text-white/90 text-sm mb-2 pt-2'>
								How you want to be remembered
							</p>
							<input
								type='text'
								placeholder='Title'
								className='w-full p-3 rounded-lg bg-white text-sm text-black placeholder:text-gray-400 mb-3'
							/>
							<textarea
								placeholder='Content'
								className='w-full p-3 rounded-lg bg-white text-sm text-black placeholder:text-gray-400 h-24 resize-none'
							/>
							<div className='flex justify-end mt-3'>
								<button className='bg-black text-white px-5 py-2 rounded-lg text-base font-semibold'>
									Save
								</button>
							</div>
						</div>
					)}
				</div>
				<div className='flex justify-end mt-3'>
					<button className='bg-white text-black px-5 py-2 rounded-lg text-sm font-semibold'>
						Save
					</button>
				</div>
			</div>
			<Sidebar
				isOpen={isSidebarOpen}
				onToggle={toggleSidebar}
			/>
		</div>
	);
}
