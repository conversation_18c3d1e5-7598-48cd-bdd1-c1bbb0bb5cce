'use client';
import FixedHeader from '@/src/components/layout/FixedHeader';
import Sidebar from '@/src/components/layout/Sidebar';
import React, { useState } from 'react';
import { HiPlay, HiSearch, HiUser } from 'react-icons/hi';

const Legacy = () => {
	const [isSidebarOpen, setIsSidebarOpen] = useState(false);
	const toggleSidebar = () => {
		setIsSidebarOpen(!isSidebarOpen);
	};
	const [searchValue, setSearchValue] = useState('<PERSON>');
	return (
		<div className='flex flex-col min-h-screen'>
			<FixedHeader
				title='Legacy'
				toggleSidebar={toggleSidebar}
			/>
			<div>
				<p className='px-4 text-xl font-semibold'>
					When you are dead, if someone Google-searched your name, what would
					show up in the results?
				</p>
				<div className='w-full max-w-2xl mx-auto p-4'>
					<div className='relative'>
						<div className='absolute inset-y-0 left-0 flex items-center pl-4'>
							<HiSearch className='h-6 w-6 text-gray-600' />
						</div>
						<input
							type='text'
							value={searchValue}
							onChange={(e) => setSearchValue(e.target.value)}
							className='w-full pl-12 pr-4 py-4 text-lg font-medium text-gray-900 bg-white rounded-2xl border-0 shadow-sm focus:ring-2 focus:ring-blue-500 focus:outline-none'
							placeholder='Search...'
						/>
					</div>
				</div>
			</div>

			<div className='max-w-md mx-auto min-h-screen'>
				{/* Header */}
				<div className='p-6'>
					<h1 className='text-2xl font-bold'>Craig Jones</h1>
					<p className='text-gray-300 text-lg font-semibold'>Life Coach</p>

					{/* Profile Image Placeholder */}
					<div className='mt-4 w-full h-32 bg-gray-200 rounded-lg flex items-center justify-center'>
						<div className='w-8 h-8 bg-gray-300 rounded opacity-50'></div>
					</div>
				</div>

				{/* Short Bio Section */}
				<div className='px-6 mb-6'>
					<div className='bg-gray-100 rounded-lg p-4'>
						<h2 className='text-black font-semibold mb-2'>Short Bio</h2>
						<hr className='text-black h-2' />
						<p className='text-gray-700 text-sm leading-relaxed'>
							Craig Jones is a dedicated and experienced life coach who helps
							individuals achieve their personal growth and life vision goals.
							With a supportive and results-oriented approach, he empowers
							clients to unlock their potential and navigate challenges.
						</p>
					</div>
				</div>

				{/* Lessons Section */}
				<h2 className='font-semibold mb-3 ml-6 text-lg'>Lessons</h2>
				<div className='px-6 mb-6'>
					<div className='bg-gray-100 rounded-lg p-4'>
						<h3 className='text-black font-medium mb-2'>Time Management</h3>
						<hr className='text-black h-2' />
						{/* Video Placeholder */}
						<div className='relative w-full h-20 bg-gray-300 rounded-lg mb-3 flex items-center justify-center'>
							<HiPlay className='w-8 h-8 text-gray-600' />
						</div>

						<p className='text-gray-700 text-sm leading-relaxed'>
							His guidance One thing I really learnt was that managing time
							isn't just about cramming more into your day, it's about being
							intentional with what you give your energy to. I know this sounds
							simple but most people are busy, but not necessarily productive
							and supportive approach helped me clarify my goals and develop
							actionable strategies.
						</p>
					</div>
				</div>

				{/* What People Are Saying Section */}
				<h2 className='font-semibold mb-3 ml-6'>What people are saying</h2>
				<div className='px-6 mb-6'>
					<div className='bg-gray-100 rounded-lg p-4'>
						<div className='grid grid-cols-2 gap-3'>
							{/* Testimonial 1 */}
							<div className='bg-white rounded-lg p-3 relative'>
								<div className='flex items-center mb-2'>
									<div className='w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center mr-2'>
										<HiUser className='w-3 h-3 text-white' />
									</div>
									<span className='text-xs text-gray-600'>John Doe</span>
								</div>
								<p className='text-xs text-gray-700 leading-relaxed'>
									His guidance and supportive approach helped me clarify my
									goals and develop actionable strategies
								</p>
							</div>

							{/* Testimonial 2 */}
							<div className='bg-white rounded-lg p-3 relative'>
								<div className='flex items-center mb-2'>
									<div className='w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center mr-2'>
										<HiUser className='w-3 h-3 text-white' />
									</div>
									<span className='text-xs text-gray-600'>John Doe</span>
								</div>
								<p className='text-xs text-gray-700 leading-relaxed'>
									His guidance and supportive approach helped me clarify my
									goals and develop actionable strategies
								</p>
							</div>
						</div>
					</div>
				</div>

				{/* Stories Section */}
				<h2 className='font-semibold mb-3 ml-6'>Stories</h2>
				<div className='px-6 pb-6'>
					<div className='bg-gray-100 rounded-lg p-4'>
						<h3 className='text-black font-medium mb-2'>A Cup of Tea</h3>
						<hr className='text-black h-2' />
						<p className='text-gray-700 text-sm leading-relaxed'>
							I remember when Craig made tea without water, just milk and two
							teabags. He was distracted, phone calls and texts, sip and nearly
							choked. He sipped his like it was fine wine. To this day the
							defends that disaster. "You just don't get gourmet," he says.
							Hilarious.
						</p>
					</div>
				</div>
			</div>
			<Sidebar
				isOpen={isSidebarOpen}
				onToggle={toggleSidebar}
			/>
		</div>
	);
};

export default Legacy;
