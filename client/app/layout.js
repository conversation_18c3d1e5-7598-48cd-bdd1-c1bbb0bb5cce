import { Geist, <PERSON><PERSON>st_Mono } from 'next/font/google';
import './globals.css';
import { Toaster } from 'react-hot-toast';
import Script from 'next/script';
import { DarkModeProvider } from '@/contexts/DarkModeContext';
import QueryProvider from '@/providers/QueryProvider';
import { NotificationPrompt } from '@/src/components/common';

const geistSans = Geist({
	variable: '--font-geist-sans',
	subsets: ['latin'],
});

const geistMono = Geist_Mono({
	variable: '--font-geist-mono',
	subsets: ['latin'],
});

export const metadata = {
	title: 'YouLife',
	description:
		'Transform your life with personalized courses, goal tracking, and life coaching tools',
	manifest: '/manifest.json',
	other: {
		'apple-mobile-web-app-capable': 'yes',
		'apple-mobile-web-app-status-bar-style': 'default',
		'apple-mobile-web-app-title': 'YouLife',
		'mobile-web-app-capable': 'yes',
		'msapplication-TileColor': '#3b82f6',
		'msapplication-tap-highlight': 'no',
		'theme-color': '#3b82f6',
	},
	icons: {
		icon: [
			{ url: '/icon', sizes: '32x32', type: 'image/png' },
			{ url: '/icon.png', sizes: '16x16', type: 'image/png' },
		],
		apple: [{ url: '/icon.png', sizes: '180x180', type: 'image/png' }],
	},
	viewport: {
		width: 'device-width',
		initialScale: 1,
		maximumScale: 1,
		userScalable: false,
		viewportFit: 'cover',
	},
};

export default function RootLayout({ children }) {
	return (
		<html lang='en'>
			<body
				className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
				<Script
					src='https://widget.cloudinary.com/v2.0/global/all.js'
					strategy='beforeInteractive'
				/>
				<DarkModeProvider>
					<QueryProvider>
						{children}
						<NotificationPrompt />
						<Toaster position='top-right' />
					</QueryProvider>
				</DarkModeProvider>
			</body>
		</html>
	);
}
