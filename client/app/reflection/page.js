'use client';
import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import FixedHeader from '@/src/components/layout/FixedHeader';
import Overview from '@/src/components/common/Overview';
import Sidebar from '@/src/components/layout/Sidebar';

const Reflection = () => {
	const [activeTab, setActiveTab] = useState('D');
	const [overview, setOverview] = useState(false);
	const [visitedTabs, setVisitedTabs] = useState(new Set(['D']));

	const tabs = ['D', 'E', 'A', 'T', 'H'];
	const reflections = {
		D: {
			title: 'Dedication',
			questions: [
				'How dedicated have you been today towards achieving your life goals?',
				'Have you used your time wisely?',
				'What excuses has crept in?',
				'Where was your focus? Past or Future',
			],
		},
		E: {
			title: 'Ethical',
			questions: [
				'How ethical have you been today?',
				'Are you comfortable with the actions you have taken?',
				'Have you responded or reacted?',
				'How much value did you add to others?',
				'Have you responded or reacted?',
			],
		},
		A: {
			title: 'Authentic',
			questions: [
				'How authentic were you today?',
				'Did you show up or did someone else?',
				'What beliefs did you operate from, are they the truth?',
				'What sort of person were you when no one was looking?',
			],
		},
		T: {
			title: 'Transformative',
			questions: [
				'How transformative have you been?',
				"Are today's results caused by you or external factors?",
				'What unhelpful behaviours did you catch?',
				'What corrective actions did you take?',
				'Did you ask what and why?',
				'Were you living at the cause or effect of something?',
			],
		},
		H: {
			title: 'Humility',
			questions: [
				'Have you identified flaws in your behaviours and actions today?',
				'Are you owning it?',
				'What mindset did you operate out from?',
				'Were you rigid in your thinking or were you flexible?',
				'What made you feel uncomfortable?',
				'Did you seek to understand?',
			],
		},
	};

	const [isSidebarOpen, setIsSidebarOpen] = useState(false);
	const toggleSidebar = () => {
		setIsSidebarOpen(!isSidebarOpen);
	};

	const handleSaveClick = () => {
		// Mark current tab as visited
		setVisitedTabs((prev) => new Set(prev).add(activeTab));

		if (activeTab === 'H') {
			// If we're on the last tab, go to overview
			setOverview(true);
			setActiveTab(null);
		} else {
			// Find the next tab in sequence
			const currentIndex = tabs.indexOf(activeTab);
			if (currentIndex < tabs.length - 1) {
				const nextTab = tabs[currentIndex + 1];
				setActiveTab(nextTab);
				setOverview(false);
				// Add the next tab to visited tabs
				setVisitedTabs((prev) => new Set(prev).add(nextTab));
			}
		}
	};

	const handleTabClick = (tab) => {
		setActiveTab(tab);
		setOverview(false);
		setVisitedTabs((prev) => new Set(prev).add(tab));
	};

	return (
		<div className='flex flex-col'>
			{/* Fixed Header */}
			<FixedHeader
				toggleSidebar={toggleSidebar}
				title='Reflection'
				showBackButton={false}
			/>
			<div className='flex items-center justify-center px-4 py-2'>
				{tabs.map((tab) => (
					<motion.button
						key={tab}
						className={`text-4xl w-12 h-12 p-2 rounded-full font-extrabold flex items-center justify-center cursor-pointer ${
							activeTab === tab && !overview
								? 'bg-gray-700 dark:bg-white dark:text-gray-900 text-white'
								: 'text-black dark:text-white'
						}`}
						onClick={() => handleTabClick(tab)}
						whileTap={{ scale: 0.95 }}
						whileHover={{ scale: 1.05 }}
						transition={{ type: 'spring', stiffness: 300 }}>
						{tab}
					</motion.button>
				))}
			</div>

			<motion.h1
				className='text-3xl font-bold text-center my-3'
				key={(activeTab || 'overview') + '-title'}
				initial={{ opacity: 0, y: -10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.3 }}>
				{overview ? 'Overview' : reflections[activeTab].title}
			</motion.h1>

			<AnimatePresence mode='wait'>
				<motion.div
					key={overview ? 'overview' : activeTab}
					initial={{ opacity: 0, x: 20 }}
					animate={{ opacity: 1, x: 0 }}
					exit={{ opacity: 0, x: -20 }}
					transition={{ duration: 0.3 }}
					className='p-6 bg-gray-200 dark:bg-gray-50 dark:text-black rounded-2xl mx-5'>
					{overview ? (
						<Overview />
					) : (
						reflections[activeTab].questions.map((question, index) => (
							<motion.div
								key={index}
								initial={{ opacity: 0, y: 10 }}
								animate={{ opacity: 1, y: 0 }}
								transition={{ duration: 0.3, delay: index * 0.1 }}>
								<p className='list-disc space-y-2 font-semibold text-lg mt-4'>
									{question}
								</p>
								<textarea
									className='w-full mt-4 p-3 rounded-lg border-2 border-black'
									rows={4}
								/>
							</motion.div>
						))
					)}

					<div className='flex justify-end'>
						<motion.button
							className='mt-4 px-6 py-2 dark:bg-teal-600 bg-[#0C142A] text-white rounded-lg hover:bg-teal-700 transition'
							whileHover={{ scale: 1.05 }}
							whileTap={{ scale: 0.95 }}
							onClick={handleSaveClick}>
							Save
						</motion.button>
					</div>
				</motion.div>
			</AnimatePresence>

			<Sidebar
				isOpen={isSidebarOpen}
				onToggle={toggleSidebar}
			/>
		</div>
	);
};

export default Reflection;
