# YouLife Frontend

A modern, scalable React/Next.js application for personal development and life coaching.

## 🚀 Project Structure

```
client/
├── src/                          # Source code
│   ├── components/               # React components
│   │   ├── ui/                  # Shadcn/ui components
│   │   ├── common/              # Reusable components
│   │   ├── features/            # Feature-specific components
│   │   │   ├── auth/           # Authentication components
│   │   │   ├── lessons/        # Lesson-related components
│   │   │   ├── diary/          # Diary components
│   │   │   ├── goals/          # Goal management components
│   │   │   └── admin/          # Admin panel components
│   │   └── layout/             # Layout components
│   ├── lib/                     # Utilities and configurations
│   │   ├── api/                # API services
│   │   ├── hooks/              # Custom React hooks
│   │   ├── utils/              # Utility functions
│   │   ├── constants/          # App constants
│   │   └── types/              # Type definitions
│   ├── assets/                  # Static assets
│   │   ├── images/
│   │   ├── icons/
│   │   └── svgs/
│   └── providers/              # Context providers
├── app/                         # Next.js 13+ app directory
├── public/                      # Static files
└── docs/                        # Documentation
```

## 🛠 Technology Stack

- **Framework**: Next.js 15.3.1
- **React**: 19.0.0
- **Styling**: Tailwind CSS 4.0
- **State Management**: TanStack Query (React Query)
- **UI Components**: Radix UI + Custom components
- **Icons**: React Icons + Lucide React
- **Animations**: Framer Motion
- **Forms**: React Hook Form (recommended)
- **HTTP Client**: Axios

## 📦 Key Features

### API Layer

- **Consolidated Services**: All API calls organized by feature
- **Error Handling**: Centralized error handling with interceptors
- **Authentication**: Automatic token management
- **Type Safety**: JSDoc documentation for all API methods

### Component Architecture

- **Reusable Components**: Well-documented, flexible components
- **Feature Organization**: Components grouped by functionality
- **Consistent Styling**: Unified design system
- **Accessibility**: ARIA compliant components

### Custom Hooks

- **Authentication**: Login, registration, password reset
- **Data Fetching**: Lessons, user progress, diary entries
- **Real-time Features**: Notifications, live updates
- **Utilities**: Form handling, local storage, etc.

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

### Environment Variables

Create a `.env.local` file:

```env
NEXT_PUBLIC_BASE_URL=http://localhost:5000
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloudinary_name
NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET=your_upload_preset
```
