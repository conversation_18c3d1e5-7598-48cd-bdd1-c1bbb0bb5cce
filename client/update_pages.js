#!/usr/bin/env node

/**
 * Update Pages Script
 * This script updates all pages in the application to use the new refactored components and API structure
 */

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  projectRoot: '/home/<USER>/Documents/Projects/You-Life/client',
  oldComponentsPath: '/home/<USER>/Documents/Projects/You-Life/client/components',
  newComponentsPath: '/home/<USER>/Documents/Projects/You-Life/client/src/components',
  
  // Import path mappings
  importMappings: {
    // Old -> New API imports
    "from '@/lib/hooks'": "from '@/src/lib/hooks'",
    "from '@/lib/api'": "from '@/src/lib/api'",
    "from '@/hooks/useAuth'": "from '@/src/lib/hooks'",
    "from '@/api'": "from '@/src/lib/api'",
    "from '@/services'": "from '@/src/lib/api'",
    
    // Old -> New component imports
    "from '@/components/ui": "from '@/src/components/ui",
    "from '@/components/common": "from '@/src/components/common",
    "from '@/components/layout": "from '@/src/components/layout",
    "from '@/components/features": "from '@/src/components/features",
    "from '@/components/": "from '@/src/components/",
    
    // Utils imports
    "from '@/lib/utils'": "from '@/src/lib/utils'",
    "from '@/utils'": "from '@/src/lib/utils'",
    
    // Asset imports
    "from '@/assets'": "from '@/assets'",
    
    // Context imports
    "from '@/contexts'": "from '@/src/providers'",
    "from '@/providers'": "from '@/src/providers'",
  },
  
  // Component usage updates
  componentUpdates: {
    // Replace manual input/button elements with new components
    inputPatterns: [
      {
        pattern: /<input\s+([^>]*type=['"]email['"][^>]*)\s*\/?>/g,
        replacement: '<Input type="email" $1 />'
      },
      {
        pattern: /<input\s+([^>]*type=['"]password['"][^>]*)\s*\/?>/g,
        replacement: '<Input type="password" $1 />'
      },
      {
        pattern: /<input\s+([^>]*type=['"]text['"][^>]*)\s*\/?>/g,
        replacement: '<Input type="text" $1 />'
      }
    ],
    
    buttonPatterns: [
      {
        pattern: /<button\s+([^>]*type=['"]submit['"][^>]*)\s*>(.*?)<\/button>/g,
        replacement: '<Button type="submit" $1>$2</Button>'
      }
    ]
  },
  
  // Files to update
  targetFiles: [
    'app/**/page.js',
    'app/**/page.jsx',
    'app/**/page.ts',
    'app/**/page.tsx',
    'app/**/layout.js',
    'app/**/layout.jsx',
    'app/**/layout.ts',
    'app/**/layout.tsx',
  ]
};

// Utility functions
function findFiles(dir, pattern) {
  const files = [];
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      files.push(...findFiles(fullPath, pattern));
    } else if (stat.isFile() && pattern.test(item)) {
      files.push(fullPath);
    }
  }
  
  return files;
}

function updateImports(content) {
  let updated = content;
  
  // Update import paths
  for (const [oldPath, newPath] of Object.entries(config.importMappings)) {
    const regex = new RegExp(oldPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
    updated = updated.replace(regex, newPath);
  }
  
  return updated;
}

function updateComponentUsage(content) {
  let updated = content;
  
  // Add necessary imports if components are being used
  const hasInputUsage = /\<Input\s/.test(updated);
  const hasButtonUsage = /\<Button\s/.test(updated);
  
  if (hasInputUsage || hasButtonUsage) {
    // Check if import already exists
    const hasComponentImport = /import.*{.*?(Button|Input).*?}.*?from.*?@\/src\/components\/common/.test(updated);
    
    if (!hasComponentImport) {
      // Add import
      const importComponents = [];
      if (hasInputUsage) importComponents.push('Input');
      if (hasButtonUsage) importComponents.push('Button');
      
      const importLine = `import { ${importComponents.join(', ')} } from '@/src/components/common';`;
      
      // Find the best place to insert the import
      const importMatch = updated.match(/import.*?from.*?['"][^'"]*['"];/g);
      if (importMatch) {
        const lastImport = importMatch[importMatch.length - 1];
        const importIndex = updated.indexOf(lastImport) + lastImport.length;
        updated = updated.slice(0, importIndex) + '\n' + importLine + updated.slice(importIndex);
      } else {
        // Add at the beginning after 'use client' if it exists
        const useClientMatch = updated.match(/['"]use client['"];/);
        if (useClientMatch) {
          const insertIndex = updated.indexOf(useClientMatch[0]) + useClientMatch[0].length;
          updated = updated.slice(0, insertIndex) + '\n' + importLine + updated.slice(insertIndex);
        } else {
          updated = importLine + '\n' + updated;
        }
      }
    }
  }
  
  return updated;
}

function updateApiUsage(content) {
  let updated = content;
  
  // Update API service calls
  const apiUpdates = {
    // Auth API updates
    'authAPI.login': 'authService.login',
    'authAPI.register': 'authService.register',
    'authAPI.logout': 'authService.logout',
    'authAPI.googleLogin': 'authService.googleLogin',
    'authAPI.forgotPassword': 'authService.forgotPassword',
    'authAPI.resetPassword': 'authService.resetPassword',
    'authAPI.verifyEmail': 'authService.verifyEmail',
    'authAPI.refreshToken': 'authService.refreshToken',
    'authAPI.resendOTP': 'authService.resendOTP',
    
    // User API updates
    'userAPI.getProfile': 'userService.getProfile',
    'userAPI.updateProfile': 'userService.updateProfile',
    'userAPI.getAllUsers': 'userService.getAllUsers',
    'userAPI.updateUserRole': 'userService.updateUserRole',
    'userAPI.deleteUser': 'userService.deleteUser',
    'userAPI.uploadAvatar': 'userService.uploadAvatar',
    'userAPI.changePassword': 'userService.changePassword',
    'userAPI.getUserStats': 'userService.getUserStats',
    'userAPI.getPreferences': 'userService.getPreferences',
    'userAPI.updatePreferences': 'userService.updatePreferences',
    
    // Lessons API updates
    'lessonsAPI.getLessons': 'lessonsService.getLessons',
    'lessonsAPI.getLessonById': 'lessonsService.getLessonById',
    'lessonsAPI.createLesson': 'lessonsService.createLesson',
    'lessonsAPI.updateLesson': 'lessonsService.updateLesson',
    'lessonsAPI.deleteLesson': 'lessonsService.deleteLesson',
    'lessonsAPI.uploadVideo': 'lessonsService.uploadVideo',
    'lessonsAPI.getCategories': 'lessonsService.getCategories',
    'lessonsAPI.getFeaturedLessons': 'lessonsService.getFeaturedLessons',
    'lessonsAPI.getRecentLessons': 'lessonsService.getRecentLessons',
    'lessonsAPI.searchLessons': 'lessonsService.searchLessons',
    
    // Goals API updates
    'goalsAPI.getUserGoals': 'goalsService.getUserGoals',
    'goalsAPI.getPublicGoals': 'goalsService.getPublicGoals',
    'goalsAPI.getGoalById': 'goalsService.getGoalById',
    'goalsAPI.createGoal': 'goalsService.createGoal',
    'goalsAPI.updateGoal': 'goalsService.updateGoal',
    'goalsAPI.deleteGoal': 'goalsService.deleteGoal',
    'goalsAPI.addTask': 'goalsService.addTask',
    'goalsAPI.updateTask': 'goalsService.updateTask',
    'goalsAPI.deleteTask': 'goalsService.deleteTask',
    'goalsAPI.completeTask': 'goalsService.completeTask',
    'goalsAPI.getGoalStats': 'goalsService.getGoalStats',
    
    // Diary API updates
    'diaryAPI.createEntry': 'diaryService.createEntry',
    'diaryAPI.getEntries': 'diaryService.getEntries',
    'diaryAPI.getRecentEntries': 'diaryService.getRecentEntries',
    'diaryAPI.getEntryById': 'diaryService.getEntryById',
    'diaryAPI.getEntryByDate': 'diaryService.getEntryByDate',
    'diaryAPI.updateEntry': 'diaryService.updateEntry',
    'diaryAPI.deleteEntry': 'diaryService.deleteEntry',
    'diaryAPI.uploadImage': 'diaryService.uploadImage',
    
    // User Progress API updates
    'userProgressAPI.updateProgress': 'userProgressService.updateProgress',
    'userProgressAPI.getProgress': 'userProgressService.getProgress',
    'userProgressAPI.getAllProgress': 'userProgressService.getAllProgress',
    'userProgressAPI.updateReflection': 'userProgressService.updateReflection',
    'userProgressAPI.deleteReflection': 'userProgressService.deleteReflection',
    'userProgressAPI.getReflections': 'userProgressService.getReflections',
    'userProgressAPI.getLearningStats': 'userProgressService.getLearningStats',
    'userProgressAPI.markLessonCompleted': 'userProgressService.markLessonCompleted',
    'userProgressAPI.resetProgress': 'userProgressService.resetProgress',
    'userProgressAPI.getCompletedLessons': 'userProgressService.getCompletedLessons',
  };
  
  // Apply API updates
  for (const [oldCall, newCall] of Object.entries(apiUpdates)) {
    const regex = new RegExp(oldCall.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
    updated = updated.replace(regex, newCall);
  }
  
  return updated;
}

function updateFile(filePath) {
  console.log(`Updating: ${filePath}`);
  
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    
    // Apply updates
    content = updateImports(content);
    content = updateApiUsage(content);
    content = updateComponentUsage(content);
    
    // Only write if content changed
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`  ✓ Updated: ${filePath}`);
      return true;
    } else {
      console.log(`  - No changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`  ✗ Error updating ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🚀 Starting page updates...\n');
  
  // Find all page files
  const pageFiles = findFiles(path.join(config.projectRoot, 'app'), /\.(js|jsx|ts|tsx)$/);
  
  console.log(`Found ${pageFiles.length} files to update:\n`);
  
  let updatedCount = 0;
  let totalCount = 0;
  
  for (const filePath of pageFiles) {
    totalCount++;
    if (updateFile(filePath)) {
      updatedCount++;
    }
  }
  
  console.log(`\n📊 Summary:`);
  console.log(`  Total files processed: ${totalCount}`);
  console.log(`  Files updated: ${updatedCount}`);
  console.log(`  Files unchanged: ${totalCount - updatedCount}`);
  
  console.log('\n✅ Page updates complete!');
  console.log('\nNext steps:');
  console.log('1. Test the updated pages');
  console.log('2. Remove old component files');
  console.log('3. Update any remaining imports');
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  updateFile,
  updateImports,
  updateApiUsage,
  updateComponentUsage,
  config
};
