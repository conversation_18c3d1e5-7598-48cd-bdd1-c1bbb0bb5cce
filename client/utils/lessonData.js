import { lessonOne, lessonTwo } from '@/assets/images';
// Centralized lesson data to avoid duplication
export const lessons = [
	{
		id: 1,
		title: 'How to Build Your Self 1',
		instructor: '<PERSON>',
		image: lessonOne,
		videoUrl:
			'https://ik.imagekit.io/uploaddoc/y2mate.com%20-%20O%20Come%20O%20Come%20Emmanuel%20%20Tommee%20Profitt%20OFFICIAL%20MUSIC%20VIDEO_1080.mp4?updatedAt=1746023886228',
		description: 'This lesson is about how to build your self',
		category: 'YOU',
	},
	{
		id: 2,
		title: 'How to Build Your Self 2',
		instructor: '<PERSON>',
		image: lessonTwo,
		videoUrl:
			'https://ik.imagekit.io/uploaddoc/sample-video.mp4?updatedAt=1738873566711',
		description: 'This lesson continues our journey of self-building',
		category: 'YOU',
	},
	{
		id: 3,
		title: 'Scope of Living 3',
		instructor: '<PERSON>',
		image: lessonOne,
		videoUrl:
			'https://ik.imagekit.io/uploaddoc/unfaced.mp4?updatedAt=1746121584895',
		description: 'Understanding your scope and potential',
		category: '=',
	},
	{
		id: 4,
		title: 'How to Build Your Self 4',
		instructor: 'Craig',
		image: lessonTwo,
		videoUrl:
			'https://ik.imagekit.io/uploaddoc/xdownloader_WJ-hFC_O4.mp4?updatedAt=1746121582684',
		description: 'This lesson is about how to build your self',
		category: 'YOU',
	},
	{
		id: 5,
		title: 'How to Build Your Self 5',
		instructor: 'Craig',
		image: lessonOne,
		videoUrl:
			'https://ik.imagekit.io/uploaddoc/videoplayback.mp4?updatedAt=1746121576819',
		description: 'Advanced techniques for self-development',
		category: 'YOU',
	},
	{
		id: 6,
		title: 'Living a Better Life 2',
		instructor: 'Craig',
		image: lessonTwo,
		videoUrl: 'https://example.com/video6.mp4',
		description: 'Practical strategies for everyday improvement',
		category: 'LIFE',
	},
];

// Helper function to get a lesson by ID
export const getLessonById = (id) => {
	const numericId = parseInt(id);
	return lessons.find((lesson) => lesson.id === numericId) || null;
};

// Helper function to get lessons by category
export const getLessonsByCategory = (category) => {
	return lessons.filter((lesson) => lesson.category === category);
};

// Helper function to get the next lesson in sequence
export const getNextLesson = (currentLessonId) => {
	const numericId = parseInt(currentLessonId);
	return lessons.find((lesson) => lesson.id === numericId + 1) || null;
};
