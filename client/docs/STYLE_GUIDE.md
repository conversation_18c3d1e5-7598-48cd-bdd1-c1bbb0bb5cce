# Style Guide

This document outlines the coding standards and best practices for the YouLife frontend project.

## Code Formatting

### Prettier Configuration

The project uses <PERSON><PERSON><PERSON> for code formatting. Configuration is in `.prettierrc`:

```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

### ESLint Configuration

ESLint is configured for code quality and consistency. Key rules:

- Use single quotes for strings
- Semicolons are required
- No unused variables
- Consistent indentation (2 spaces)
- Prefer const over let when possible

## Naming Conventions

### Files and Directories

- **Components**: PascalCase (e.g., `UserProfile.jsx`)
- **Hooks**: camelCase with `use` prefix (e.g., `useUserProfile.js`)
- **Utilities**: camelCase (e.g., `formatDate.js`)
- **Constants**: camelCase file, UPPER_SNAKE_CASE exports (e.g., `apiConstants.js`)
- **Directories**: kebab-case (e.g., `user-profile/`)

### Variables and Functions

```javascript
// Variables: camelCase
const userName = 'John Doe';
const isLoggedIn = true;

// Functions: camelCase
function getUserProfile() {}
const handleSubmit = () => {};

// Constants: UPPER_SNAKE_CASE
const API_BASE_URL = 'https://api.example.com';
const MAX_RETRY_ATTEMPTS = 3;

// Boolean variables: is/has/can/should prefix
const isLoading = false;
const hasError = true;
const canEdit = false;
const shouldUpdate = true;
```

### React Components

```javascript
// Component names: PascalCase
const UserProfile = () => {};
const LoginForm = () => {};

// Props: camelCase
const Button = ({ isLoading, onClick, children }) => {};

// Event handlers: handle prefix
const handleClick = () => {};
const handleSubmit = () => {};
const handleInputChange = () => {};
```

## Component Structure

### Component File Template

```javascript
/**
 * Component description
 * @param {Object} props - Component props
 * @param {string} props.title - Title prop description
 * @param {Function} props.onClick - Click handler description
 * @returns {React.ReactElement} Component JSX
 */
'use client'; // Only if needed

import React, { useState, useEffect } from 'react';
import { cn } from '@/src/lib/utils';

// Import types if using TypeScript
// import { ComponentProps } from '@/src/lib/types';

const ComponentName = ({ 
  title, 
  onClick, 
  className = '',
  ...rest 
}) => {
  // State declarations
  const [isLoading, setIsLoading] = useState(false);
  
  // Custom hooks
  const { data, error } = useCustomHook();
  
  // Effects
  useEffect(() => {
    // Effect logic
  }, []);
  
  // Event handlers
  const handleClick = () => {
    onClick?.();
  };
  
  // Early returns
  if (error) {
    return <div>Error occurred</div>;
  }
  
  // Main render
  return (
    <div className={cn('base-classes', className)} {...rest}>
      <h1>{title}</h1>
      <button onClick={handleClick}>
        Click me
      </button>
    </div>
  );
};

export default ComponentName;
```

### Component Organization

1. **Imports**: External libraries first, then internal imports
2. **Component definition**: Main component function
3. **State**: useState hooks
4. **Effects**: useEffect hooks
5. **Event handlers**: Functions that handle events
6. **Render helpers**: Functions that return JSX
7. **Main render**: The return statement

## JSDoc Documentation

### Component Documentation

```javascript
/**
 * User profile card component
 * @param {Object} props - Component props
 * @param {User} props.user - User data object
 * @param {boolean} [props.isEditable=false] - Whether profile can be edited
 * @param {Function} [props.onEdit] - Edit handler function
 * @param {string} [props.className] - Additional CSS classes
 * @returns {React.ReactElement} UserProfile component
 */
const UserProfile = ({ user, isEditable = false, onEdit, className }) => {
  // Component implementation
};
```

### Function Documentation

```javascript
/**
 * Format date to readable string
 * @param {Date|string} date - Date to format
 * @param {Object} [options={}] - Formatting options
 * @param {string} [options.format='long'] - Date format (short, long, numeric)
 * @param {string} [options.locale='en-US'] - Locale for formatting
 * @returns {string} Formatted date string
 * @example
 * formatDate(new Date(), { format: 'short' }) // "Jan 1, 2024"
 */
const formatDate = (date, options = {}) => {
  // Function implementation
};
```

### Hook Documentation

```javascript
/**
 * Custom hook for user authentication
 * @param {Object} [options={}] - Hook options
 * @param {boolean} [options.redirectOnLogout=true] - Redirect on logout
 * @returns {Object} Authentication state and methods
 * @returns {User|null} returns.user - Current user or null
 * @returns {boolean} returns.isLoading - Loading state
 * @returns {Function} returns.login - Login function
 * @returns {Function} returns.logout - Logout function
 */
const useAuth = (options = {}) => {
  // Hook implementation
};
```

## CSS and Styling

### Tailwind CSS Guidelines

```javascript
// Use semantic class names
<div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">

// Group related classes
<button className={cn(
  // Base styles
  'px-4 py-2 rounded-lg font-medium transition-colors',
  // State styles
  'hover:bg-blue-700 focus:ring-2 focus:ring-blue-500',
  // Conditional styles
  isLoading && 'opacity-50 cursor-not-allowed',
  className
)}>

// Use consistent spacing scale
<div className="space-y-4"> // Vertical spacing
  <div className="space-x-2"> // Horizontal spacing
```

### Dark Mode Support

```javascript
// Always include dark mode variants
<div className="bg-white dark:bg-gray-800 text-gray-900 dark:text-white">

// Use semantic color names
<div className="text-gray-600 dark:text-gray-400"> // Secondary text
<div className="border-gray-200 dark:border-gray-700"> // Borders
```

## Error Handling

### Component Error Boundaries

```javascript
// Use error boundaries for component errors
<ErrorBoundary fallback={<ErrorFallback />}>
  <ComponentThatMightFail />
</ErrorBoundary>

// Implement proper error states
if (error) {
  return (
    <div className="text-center p-8">
      <p className="text-red-600">Something went wrong</p>
      <button onClick={retry}>Try Again</button>
    </div>
  );
}
```

### API Error Handling

```javascript
// Handle different error types
try {
  const data = await apiCall();
  return data;
} catch (error) {
  if (error.response?.status === 401) {
    // Handle unauthorized
    logout();
  } else if (error.response?.status === 404) {
    // Handle not found
    showNotFound();
  } else {
    // Handle general error
    showError(error.message);
  }
}
```

## Performance Best Practices

### Component Optimization

```javascript
// Use React.memo for expensive components
const ExpensiveComponent = React.memo(({ data }) => {
  return <div>{/* Expensive rendering */}</div>;
});

// Use useCallback for event handlers
const handleClick = useCallback(() => {
  // Handler logic
}, [dependency]);

// Use useMemo for expensive calculations
const expensiveValue = useMemo(() => {
  return expensiveCalculation(data);
}, [data]);
```

### Image Optimization

```javascript
// Use Next.js Image component
import Image from 'next/image';

<Image
  src="/image.jpg"
  alt="Description"
  width={500}
  height={300}
  priority // For above-the-fold images
  placeholder="blur"
  blurDataURL="data:image/jpeg;base64,..."
/>
```

## Testing Guidelines

### Component Testing

```javascript
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from '@/src/components/common';

describe('Button Component', () => {
  test('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button')).toHaveTextContent('Click me');
  });

  test('calls onClick when clicked', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  test('shows loading state', () => {
    render(<Button loading>Click me</Button>);
    expect(screen.getByRole('button')).toBeDisabled();
  });
});
```

## Accessibility Guidelines

### ARIA Labels and Roles

```javascript
// Use semantic HTML
<button type="button" onClick={handleClick}>
  Submit
</button>

// Add ARIA labels when needed
<button
  type="button"
  aria-label="Close modal"
  onClick={onClose}
>
  <CloseIcon />
</button>

// Use proper heading hierarchy
<h1>Page Title</h1>
<h2>Section Title</h2>
<h3>Subsection Title</h3>
```

### Keyboard Navigation

```javascript
// Handle keyboard events
const handleKeyDown = (e) => {
  if (e.key === 'Enter' || e.key === ' ') {
    handleClick();
  }
  if (e.key === 'Escape') {
    onClose();
  }
};

// Ensure focusable elements
<div
  role="button"
  tabIndex={0}
  onKeyDown={handleKeyDown}
  onClick={handleClick}
>
  Custom button
</div>
```

## Git Commit Guidelines

### Commit Message Format

```
type(scope): description

[optional body]

[optional footer]
```

### Commit Types

- **feat**: New feature
- **fix**: Bug fix
- **docs**: Documentation changes
- **style**: Code style changes (formatting, etc.)
- **refactor**: Code refactoring
- **test**: Adding or updating tests
- **chore**: Maintenance tasks

### Examples

```
feat(auth): add Google OAuth login
fix(ui): resolve button loading state issue
docs(api): update API documentation
refactor(components): extract reusable Button component
```
