# Component Documentation

This document provides detailed information about the component architecture and usage guidelines.

## Component Structure

### Directory Organization

```
src/components/
├── ui/                    # Shadcn/ui base components
├── common/                # Reusable components
├── features/              # Feature-specific components
│   ├── auth/             # Authentication components
│   ├── lessons/          # Lesson-related components
│   ├── diary/            # Diary components
│   ├── goals/            # Goal management components
│   └── admin/            # Admin panel components
└── layout/               # Layout components
```

## Common Components

### Button Component

A flexible button component with multiple variants and states.

**Props:**
- `variant`: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive'
- `size`: 'sm' | 'md' | 'lg'
- `loading`: boolean
- `disabled`: boolean
- `leftIcon`: React.ReactNode
- `rightIcon`: React.ReactNode

**Usage:**
```jsx
import { Button } from '@/src/components/common';

<Button 
  variant="primary" 
  size="md" 
  loading={isLoading}
  leftIcon={<FaUser />}
  onClick={handleClick}
>
  Submit
</Button>
```

### Input Component

A comprehensive input component with validation and icons.

**Props:**
- `type`: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url'
- `label`: string
- `error`: string
- `helperText`: string
- `leftIcon`: React.ReactNode
- `rightIcon`: React.ReactNode
- `size`: 'sm' | 'md' | 'lg'

**Usage:**
```jsx
import { Input } from '@/src/components/common';

<Input
  label="Email Address"
  type="email"
  placeholder="Enter your email"
  value={email}
  onChange={setEmail}
  error={errors.email}
  leftIcon={<FaEnvelope />}
  required
/>
```

### Modal Component

An accessible modal component with backdrop and animations.

**Props:**
- `isOpen`: boolean
- `onClose`: function
- `title`: string
- `size`: 'sm' | 'md' | 'lg' | 'xl' | 'full'
- `closeOnBackdrop`: boolean
- `closeOnEscape`: boolean
- `footer`: React.ReactNode

**Usage:**
```jsx
import { Modal, Button } from '@/src/components/common';

<Modal
  isOpen={isModalOpen}
  onClose={() => setIsModalOpen(false)}
  title="Confirm Action"
  size="md"
  footer={
    <>
      <Button variant="outline" onClick={() => setIsModalOpen(false)}>
        Cancel
      </Button>
      <Button variant="primary" onClick={handleConfirm}>
        Confirm
      </Button>
    </>
  }
>
  <p>Are you sure you want to perform this action?</p>
</Modal>
```

### Loading Components

Various loading indicators for different use cases.

**Available Components:**
- `Loading.Spinner`: Basic spinner
- `Loading.Dots`: Animated dots
- `Loading.Skeleton`: Skeleton placeholder
- `Loading.Page`: Full page loading
- `Loading.Inline`: Inline loading with text
- `Loading.Button`: Button loading state
- `Loading.Card`: Card skeleton

**Usage:**
```jsx
import Loading from '@/src/components/common/Loading';

// Spinner
<Loading.Spinner size="md" />

// Page loading
<Loading.Page message="Loading your data..." />

// Skeleton
<Loading.Skeleton lines={3} />
```

### CustomImage Component

Enhanced image component with fallbacks and loading states.

**Props:**
- `src`: string
- `alt`: string
- `showLoading`: boolean
- `fallback`: React.ReactNode
- `onLoad`: function
- `onError`: function

**Usage:**
```jsx
import { CustomImage } from '@/src/components/common';

<CustomImage
  src={user.avatar}
  alt={user.name}
  width={100}
  height={100}
  showLoading={true}
  fallback={<div>No image available</div>}
/>
```

## Feature Components

### Authentication Components

Located in `src/components/features/auth/`:

- **LoginForm**: Complete login form with validation
- **SignupForm**: Registration form with email verification
- **ForgotPasswordForm**: Password reset request form
- **ResetPasswordForm**: Password reset form with OTP

### Lesson Components

Located in `src/components/features/lessons/`:

- **LessonCard**: Display lesson information
- **VideoPlayer**: Custom video player with progress tracking
- **LessonList**: Paginated list of lessons
- **LessonUploadForm**: Admin form for uploading lessons

### Diary Components

Located in `src/components/features/diary/`:

- **DiaryEntry**: Single diary entry display
- **DiaryForm**: Form for creating/editing entries
- **DiaryCalendar**: Calendar view of diary entries
- **MoodSelector**: Mood selection component

## Layout Components

### Header Component

Main navigation header with user menu and notifications.

**Props:**
- `user`: User object
- `onMenuToggle`: function
- `showNotifications`: boolean

### Sidebar Component

Navigation sidebar with menu items and user info.

**Props:**
- `isOpen`: boolean
- `onClose`: function
- `currentPath`: string

### AdminLayout Component

Layout wrapper for admin pages with admin-specific navigation.

## Component Guidelines

### Naming Conventions

- **Components**: PascalCase (e.g., `UserProfile.jsx`)
- **Props**: camelCase (e.g., `isLoading`, `onClick`)
- **Event Handlers**: `handle` prefix (e.g., `handleSubmit`)
- **Boolean Props**: `is`, `has`, `can`, `should` prefixes

### JSDoc Documentation

All components should include comprehensive JSDoc comments:

```jsx
/**
 * User profile card component
 * @param {Object} props - Component props
 * @param {Object} props.user - User data object
 * @param {boolean} props.isEditable - Whether profile can be edited
 * @param {Function} props.onEdit - Edit handler function
 * @returns {React.ReactElement} UserProfile component
 */
const UserProfile = ({ user, isEditable = false, onEdit }) => {
  // Component implementation
};
```

### Accessibility Guidelines

- Include proper ARIA labels and roles
- Ensure keyboard navigation support
- Maintain proper heading hierarchy
- Use semantic HTML elements
- Test with screen readers

### Performance Best Practices

- Use `React.memo` for expensive components
- Implement proper loading states
- Optimize re-renders with `useCallback` and `useMemo`
- Lazy load heavy components
- Use proper key props in lists

### Styling Guidelines

- Use Tailwind CSS classes consistently
- Implement dark mode support
- Follow responsive design principles
- Use the `cn()` utility for conditional classes
- Maintain consistent spacing and typography

## Testing Components

### Unit Testing

Use Jest and React Testing Library for component testing:

```jsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from '@/src/components/common';

test('renders button with correct text', () => {
  render(<Button>Click me</Button>);
  expect(screen.getByRole('button')).toHaveTextContent('Click me');
});

test('calls onClick when clicked', () => {
  const handleClick = jest.fn();
  render(<Button onClick={handleClick}>Click me</Button>);
  fireEvent.click(screen.getByRole('button'));
  expect(handleClick).toHaveBeenCalledTimes(1);
});
```

### Integration Testing

Test component interactions and data flow:

```jsx
test('form submission works correctly', async () => {
  const mockSubmit = jest.fn();
  render(<LoginForm onSubmit={mockSubmit} />);
  
  fireEvent.change(screen.getByLabelText('Email'), {
    target: { value: '<EMAIL>' }
  });
  fireEvent.change(screen.getByLabelText('Password'), {
    target: { value: 'password123' }
  });
  fireEvent.click(screen.getByRole('button', { name: 'Sign In' }));
  
  await waitFor(() => {
    expect(mockSubmit).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password123'
    });
  });
});
```
