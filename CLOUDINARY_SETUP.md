# Cloudinary Frontend Upload Setup Guide

This guide will help you configure Cloudinary for frontend video uploads, moving the video upload process from the server to the client-side to handle large files better.

## Why This Change?

- **Vercel Limitations**: Vercel has limitations on file upload sizes and execution time
- **Better Performance**: Direct uploads to Cloudinary are faster and more reliable
- **Reduced Server Load**: Server no longer handles large file uploads
- **Better User Experience**: Users get real-time upload progress

## Prerequisites

1. A Cloudinary account (free tier is sufficient for testing)
2. Access to your Cloudinary dashboard

## Setup Steps

### 1. Create an Unsigned Upload Preset

1. Log into your [Cloudinary Dashboard](https://cloudinary.com/console)
2. Go to **Settings** → **Upload** → **Upload presets**
3. Click **Add upload preset**
4. Configure the preset:
   - **Upload preset name**: Choose a name (e.g., `you-life-uploads`)
   - **Signing Mode**: Select **Unsigned**
   - **Folder**: Set a folder name (e.g., `you-life`)
   - **Resource type**: Select **Auto** (allows both images and videos)
   - **File size limit**: Set to your desired limit (e.g., 500MB for videos)
   - **Allowed formats**: For videos: `mp4,mov,avi,wmv,flv,webm`. For images: `jpg,png,gif,webp`

### 2. Configure Environment Variables

1. Copy the example environment file:
   ```bash
   cp client/.env.local.example client/.env.local
   ```

2. Fill in your Cloudinary credentials in `client/.env.local`:
   ```env
   NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloud_name_here
   NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET=your_unsigned_upload_preset_here
   NEXT_PUBLIC_API_URL=http://localhost:5000
   ```

   You can find your **Cloud Name** in your Cloudinary Dashboard under **Account Details**.

### 3. Backend Changes (Already Implemented)

The backend has been updated to:
- Accept video and thumbnail URLs instead of files
- Remove file upload middleware (multer) from lesson routes
- Validate URLs instead of file sizes
- Store URLs directly in the database

### 4. Frontend Changes (Already Implemented)

The frontend now includes:
- `CloudinaryUpload` component for handling uploads
- `useCloudinaryUpload` hook for upload management
- Updated lesson forms to use URL-based uploads
- Cloudinary upload widget integration

## How It Works

1. **User selects files**: User clicks upload areas in the lesson forms
2. **Cloudinary widget opens**: A professional upload widget appears
3. **Direct upload**: Files are uploaded directly to Cloudinary
4. **URLs returned**: Cloudinary returns secure URLs
5. **Backend storage**: URLs are sent to backend and stored in database

## Upload Process Flow

```
User → CloudinaryUpload Component → Cloudinary Servers → Secure URLs → Backend → Database
```

## Security Features

- **Unsigned uploads**: No API secrets exposed to frontend
- **Upload presets**: Control what can be uploaded
- **Folder organization**: Files organized by type
- **Size limits**: Enforced by Cloudinary
- **Format restrictions**: Only allowed file types accepted

## Folder Structure

Your Cloudinary media library will be organized as:
```
your-cloud/
├── lesson-videos/
│   ├── video1.mp4
│   └── video2.mp4
├── lesson-thumbnails/
│   ├── thumb1.jpg
│   └── thumb2.jpg
└── diary-images/
    ├── image1.jpg
    └── image2.jpg
```

## Testing

1. Start your development servers:
   ```bash
   # Backend
   cd server && npm run dev
   
   # Frontend
   cd client && npm run dev
   ```

2. Navigate to the lesson upload page
3. Try uploading a video and thumbnail
4. Verify uploads appear in your Cloudinary dashboard
5. Check that lesson is created successfully

## Troubleshooting

### Upload Widget Not Loading
- Ensure Cloudinary script is loaded in layout.js
- Check browser console for JavaScript errors
- Verify environment variables are set correctly

### Upload Fails
- Check upload preset configuration
- Verify file size and format restrictions
- Check browser network tab for failed requests

### Backend Errors
- Ensure backend expects URLs not files
- Check API endpoint responses
- Verify database can store URL strings

## Advanced Configuration

### Custom Upload Widget Styling
Modify the widget configuration in `hooks/useCloudinaryUpload.js`:

```javascript
styles: {
  palette: {
    window: '#FFFFFF',
    tabIcon: '#0078FF',
    // ... customize colors
  }
}
```

### Upload Transformations
Add transformations in your upload preset:
- Video: Auto quality optimization
- Images: Auto format and quality
- Thumbnails: Resize and crop

### Multiple Upload Presets
Create separate presets for different use cases:
- `videos-preset`: For lesson videos
- `images-preset`: For thumbnails and images
- `profile-preset`: For user profile pictures

## Production Deployment

1. Update environment variables on your hosting platform
2. Ensure Cloudinary script is accessible from your domain
3. Consider enabling delivery optimizations
4. Set up webhook notifications if needed

## Cost Considerations

- Cloudinary free tier includes generous limits
- Uploads count towards your monthly transformation quota
- Consider storage and bandwidth usage for large videos
- Monitor usage in Cloudinary dashboard

## Support

If you encounter issues:
1. Check Cloudinary documentation
2. Verify upload preset configuration
3. Test with smaller files first
4. Check browser developer tools for errors
